"""
Extrator de componentes do Figma.

Este módulo é responsável por:
- Identificar diferentes tipos de componentes
- Extrair propriedades específicas de cada tipo
- Organizar componentes em hierarquias
- Preparar dados para geração de código

DEPRECATED: Este módulo será substituído pelo novo sistema de conversão
baseado na lógica do MCP server para melhor qualidade de saída.
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum

from src.parsers.figma_parser import FigmaProject

logger = logging.getLogger(__name__)


class ComponentType(Enum):
    """Tipos de componentes identificados."""
    CONTAINER = "container"  # FRAME que atua como container
    TEXT = "text"           # Elementos de texto
    BUTTON = "button"       # Botões e elementos clicáveis
    INPUT = "input"         # Campos de entrada
    IMAGE = "image"         # Imagens e ícones
    SHAPE = "shape"         # Formas geométricas
    LAYOUT = "layout"       # Elementos de layout
    COMPONENT = "component" # Componentes reutilizáveis


@dataclass
class ExtractedComponent:
    """Representa um componente extraído do Figma."""
    id: str
    name: str
    type: ComponentType
    original_type: str  # Tipo original do Figma
    properties: Dict[str, Any] = field(default_factory=dict)
    children: List['ExtractedComponent'] = field(default_factory=list)
    parent_id: Optional[str] = None
    
    # Propriedades específicas para geração de código
    html_tag: str = "div"
    css_classes: List[str] = field(default_factory=list)
    css_properties: Dict[str, str] = field(default_factory=dict)
    content: str = ""


class ComponentExtractor:
    """Extrator de componentes do Figma."""
    
    def __init__(self, project: FigmaProject):
        """
        Inicializa o extrator.
        
        Args:
            project: Projeto do Figma processado
        """
        self.project = project
        self.extracted_components: Dict[str, ExtractedComponent] = {}
        
    def extract_all_components(self) -> Dict[str, ExtractedComponent]:
        """
        Extrai todos os componentes do projeto.
        
        Returns:
            Dict mapeando ID para componente extraído
        """
        logger.info("Iniciando extração de componentes...")
        
        # Processar todos os nodes do projeto
        for node_id, node_data in self.project.nodes.items():
            if 'document' in node_data:
                self._extract_node_recursive(node_data['document'])
        
        logger.info(f"Total de componentes extraídos: {len(self.extracted_components)}")
        return self.extracted_components
    
    def _extract_node_recursive(self, node_data: Dict[str, Any], parent_id: Optional[str] = None) -> Optional[ExtractedComponent]:
        """
        Extrai um node e seus filhos recursivamente.
        
        Args:
            node_data: Dados do node
            parent_id: ID do componente pai
            
        Returns:
            Componente extraído ou None
        """
        node_id = node_data.get('id', '')
        node_type = node_data.get('type', '')
        node_name = node_data.get('name', '')
        
        # Determinar tipo do componente
        component_type = self._determine_component_type(node_data)
        
        # Criar componente extraído
        component = ExtractedComponent(
            id=node_id,
            name=node_name,
            type=component_type,
            original_type=node_type,
            parent_id=parent_id
        )
        
        # Extrair propriedades específicas
        self._extract_properties(component, node_data)
        
        # Processar filhos
        if 'children' in node_data:
            for child_data in node_data['children']:
                child_component = self._extract_node_recursive(child_data, node_id)
                if child_component:
                    component.children.append(child_component)
        
        # Armazenar componente
        self.extracted_components[node_id] = component
        
        return component
    
    def _determine_component_type(self, node_data: Dict[str, Any]) -> ComponentType:
        """
        Determina o tipo do componente baseado nos dados do node.
        
        Args:
            node_data: Dados do node
            
        Returns:
            Tipo do componente
        """
        node_type = node_data.get('type', '')
        node_name = node_data.get('name', '').lower()
        
        # Verificar se é um componente reutilizável
        if 'componentId' in node_data or node_type == 'INSTANCE':
            return ComponentType.COMPONENT
        
        # Verificar por nome/tipo específico
        if node_type == 'TEXT':
            return ComponentType.TEXT
        
        if 'button' in node_name or 'btn' in node_name:
            return ComponentType.BUTTON
        
        if any(keyword in node_name for keyword in ['input', 'field', 'text field']):
            return ComponentType.INPUT
        
        if node_type in ['VECTOR', 'ELLIPSE', 'POLYGON']:
            # Verificar se é ícone ou forma
            if any(keyword in node_name for keyword in ['icon', 'ico', 'symbol']):
                return ComponentType.IMAGE
            return ComponentType.SHAPE
        
        if node_type == 'RECTANGLE':
            # Verificar se é imagem ou forma decorativa
            if 'image' in node_name or 'img' in node_name:
                return ComponentType.IMAGE
            return ComponentType.SHAPE
        
        if node_type == 'FRAME':
            # Verificar se é container ou layout
            if any(keyword in node_name for keyword in ['container', 'box', 'wrapper']):
                return ComponentType.CONTAINER
            if any(keyword in node_name for keyword in ['layout', 'grid', 'flex']):
                return ComponentType.LAYOUT
            return ComponentType.CONTAINER
        
        # Padrão
        return ComponentType.CONTAINER
    
    def _extract_properties(self, component: ExtractedComponent, node_data: Dict[str, Any]) -> None:
        """
        Extrai propriedades específicas do node.
        
        Args:
            component: Componente sendo processado
            node_data: Dados do node
        """
        # Propriedades básicas
        component.properties = {
            'visible': node_data.get('visible', True),
            'locked': node_data.get('locked', False),
            'opacity': node_data.get('opacity', 1.0)
        }
        
        # Posicionamento e dimensões
        if 'absoluteBoundingBox' in node_data:
            bbox = node_data['absoluteBoundingBox']
            component.properties['position'] = {
                'x': bbox.get('x', 0),
                'y': bbox.get('y', 0),
                'width': bbox.get('width', 0),
                'height': bbox.get('height', 0)
            }
            
            # Converter para CSS
            component.css_properties.update({
                'width': f"{bbox.get('width', 0)}px",
                'height': f"{bbox.get('height', 0)}px",
                'position': 'absolute',
                'left': f"{bbox.get('x', 0)}px",
                'top': f"{bbox.get('y', 0)}px"
            })
        
        # Propriedades específicas por tipo
        if component.type == ComponentType.TEXT:
            self._extract_text_properties(component, node_data)
        elif component.type == ComponentType.BUTTON:
            self._extract_button_properties(component, node_data)
        elif component.type == ComponentType.CONTAINER:
            self._extract_container_properties(component, node_data)
        
        # Propriedades visuais gerais
        self._extract_visual_properties(component, node_data)
    
    def _extract_text_properties(self, component: ExtractedComponent, node_data: Dict[str, Any]) -> None:
        """Extrai propriedades específicas de texto."""
        component.html_tag = "span"

        # Conteúdo do texto
        if 'characters' in node_data:
            component.content = node_data['characters']

        # Estilo do texto
        if 'style' in node_data:
            style = node_data['style']

            if 'fontSize' in style:
                component.css_properties['font-size'] = f"{style['fontSize']}px"

            if 'fontFamily' in style:
                component.css_properties['font-family'] = style['fontFamily']

            if 'fontWeight' in style:
                component.css_properties['font-weight'] = str(style['fontWeight'])

            if 'textAlignHorizontal' in style:
                align_map = {
                    'LEFT': 'left',
                    'CENTER': 'center',
                    'RIGHT': 'right',
                    'JUSTIFIED': 'justify'
                }
                component.css_properties['text-align'] = align_map.get(
                    style['textAlignHorizontal'], 'left'
                )

        # Cor do texto (APENAS color, não background-color para texto)
        if 'fills' in node_data and node_data['fills']:
            fill = node_data['fills'][0]

            # Verificar se fill está visível
            if fill.get('visible', True):
                if fill.get('type') == 'SOLID' and 'color' in fill:
                    color = fill['color']
                    opacity = fill.get('opacity', 1.0)

                    # Aplicar opacidade se diferente de 1.0
                    if opacity != 1.0:
                        rgba = f"rgba({int(color['r']*255)}, {int(color['g']*255)}, {int(color['b']*255)}, {opacity})"
                        component.css_properties['color'] = rgba
                    else:
                        rgb = f"rgb({int(color['r']*255)}, {int(color['g']*255)}, {int(color['b']*255)})"
                        component.css_properties['color'] = rgb

                elif fill.get('type') == 'VARIABLE_ALIAS':
                    # Marcar como variável do design system para processamento posterior
                    component.properties['text_color_variable'] = fill.get('id', '')
    
    def _extract_button_properties(self, component: ExtractedComponent, node_data: Dict[str, Any]) -> None:
        """Extrai propriedades específicas de botão."""
        component.html_tag = "button"
        component.css_classes.append("btn")
        
        # Propriedades de interação
        if 'reactions' in node_data:
            component.properties['interactive'] = True
            component.properties['reactions'] = node_data['reactions']
    
    def _extract_container_properties(self, component: ExtractedComponent, node_data: Dict[str, Any]) -> None:
        """Extrai propriedades específicas de container."""
        component.html_tag = "div"
        
        # Layout properties
        if 'layoutMode' in node_data:
            layout_mode = node_data['layoutMode']
            if layout_mode == 'HORIZONTAL':
                component.css_properties['display'] = 'flex'
                component.css_properties['flex-direction'] = 'row'
            elif layout_mode == 'VERTICAL':
                component.css_properties['display'] = 'flex'
                component.css_properties['flex-direction'] = 'column'
        
        # Padding
        if 'paddingLeft' in node_data:
            component.css_properties['padding-left'] = f"{node_data['paddingLeft']}px"
        if 'paddingRight' in node_data:
            component.css_properties['padding-right'] = f"{node_data['paddingRight']}px"
        if 'paddingTop' in node_data:
            component.css_properties['padding-top'] = f"{node_data['paddingTop']}px"
        if 'paddingBottom' in node_data:
            component.css_properties['padding-bottom'] = f"{node_data['paddingBottom']}px"
    
    def _extract_visual_properties(self, component: ExtractedComponent, node_data: Dict[str, Any]) -> None:
        """Extrai propriedades visuais gerais."""
        # Background/Fill (NÃO aplicar em elementos de texto)
        if 'fills' in node_data and node_data['fills'] and component.type != ComponentType.TEXT:
            fill = node_data['fills'][0]

            # Verificar se fill está visível
            if fill.get('visible', True):
                if fill.get('type') == 'SOLID' and 'color' in fill:
                    color = fill['color']
                    opacity = fill.get('opacity', 1.0)

                    # Aplicar opacidade se diferente de 1.0
                    if opacity != 1.0:
                        rgba = f"rgba({int(color['r']*255)}, {int(color['g']*255)}, {int(color['b']*255)}, {opacity})"
                        component.css_properties['background-color'] = rgba
                    else:
                        rgb = f"rgb({int(color['r']*255)}, {int(color['g']*255)}, {int(color['b']*255)})"
                        component.css_properties['background-color'] = rgb

                elif fill.get('type') == 'VARIABLE_ALIAS':
                    # Marcar como variável do design system para processamento posterior
                    component.properties['background_variable'] = fill.get('id', '')
        
        # Border
        if 'strokes' in node_data and node_data['strokes']:
            stroke = node_data['strokes'][0]
            if stroke.get('type') == 'SOLID' and 'color' in stroke:
                color = stroke['color']
                rgb = f"rgb({int(color['r']*255)}, {int(color['g']*255)}, {int(color['b']*255)})"
                component.css_properties['border-color'] = rgb
                
                stroke_weight = node_data.get('strokeWeight', 1)
                component.css_properties['border-width'] = f"{stroke_weight}px"
                component.css_properties['border-style'] = 'solid'
        
        # Border radius
        if 'cornerRadius' in node_data:
            component.css_properties['border-radius'] = f"{node_data['cornerRadius']}px"
        
        # Opacity
        if 'opacity' in node_data and node_data['opacity'] != 1.0:
            component.css_properties['opacity'] = str(node_data['opacity'])
    
    def get_components_by_type(self, component_type: ComponentType) -> List[ExtractedComponent]:
        """
        Retorna componentes de um tipo específico.
        
        Args:
            component_type: Tipo do componente
            
        Returns:
            Lista de componentes do tipo especificado
        """
        return [comp for comp in self.extracted_components.values() 
                if comp.type == component_type]
    
    def get_component_hierarchy(self) -> List[ExtractedComponent]:
        """
        Retorna a hierarquia de componentes (apenas componentes raiz).
        
        Returns:
            Lista de componentes raiz
        """
        return [comp for comp in self.extracted_components.values() 
                if comp.parent_id is None]
    
    def get_extraction_summary(self) -> Dict[str, Any]:
        """
        Retorna um resumo da extração.
        
        Returns:
            Dict com estatísticas da extração
        """
        type_counts = {}
        for comp in self.extracted_components.values():
            type_name = comp.type.value
            type_counts[type_name] = type_counts.get(type_name, 0) + 1
        
        return {
            'total_components': len(self.extracted_components),
            'components_by_type': type_counts,
            'root_components': len(self.get_component_hierarchy())
        }
