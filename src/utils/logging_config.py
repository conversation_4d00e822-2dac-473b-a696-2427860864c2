"""
Configuração centralizada de logging para o projeto.

Este módulo configura logging para console e arquivo, com rotação automática
e diferentes níveis de log para desenvolvimento e produção.
"""

import logging
import logging.handlers
from pathlib import Path
from datetime import datetime
from typing import Optional


def setup_logging(
    log_level: str = "INFO",
    log_to_file: bool = True,
    log_dir: str = "data/logs",
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    Configura logging para o projeto.
    
    Args:
        log_level: Nível de log (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_to_file: Se deve salvar logs em arquivo
        log_dir: Diretório para arquivos de log
        max_file_size: Tamanho máximo do arquivo de log em bytes
        backup_count: Número de arquivos de backup a manter
        
    Returns:
        Logger configurado
    """
    # Converter string para nível de logging
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Criar diretório de logs
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Configurar formatador
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configurar logger raiz
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Remover handlers existentes
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Handler para console
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Handler para arquivo (se habilitado)
    if log_to_file:
        # Nome do arquivo com timestamp
        timestamp = datetime.now().strftime("%Y%m%d")
        log_file = log_path / f"figma_to_code_{timestamp}.log"
        
        # Handler com rotação
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # Log inicial
        root_logger.info(f"Logging configurado - Arquivo: {log_file}")
    
    return root_logger


def get_logger(name: str) -> logging.Logger:
    """
    Obtém um logger com nome específico.
    
    Args:
        name: Nome do logger (geralmente __name__)
        
    Returns:
        Logger configurado
    """
    return logging.getLogger(name)


def log_function_call(func_name: str, args: dict = None, result: str = None) -> None:
    """
    Log padronizado para chamadas de função.
    
    Args:
        func_name: Nome da função
        args: Argumentos da função
        result: Resultado da função
    """
    logger = get_logger("function_calls")
    
    if args:
        logger.debug(f"Chamando {func_name} com args: {args}")
    else:
        logger.debug(f"Chamando {func_name}")
    
    if result:
        logger.debug(f"{func_name} retornou: {result}")


def log_performance(operation: str, duration: float, details: dict = None) -> None:
    """
    Log padronizado para métricas de performance.
    
    Args:
        operation: Nome da operação
        duration: Duração em segundos
        details: Detalhes adicionais
    """
    logger = get_logger("performance")
    
    message = f"Performance - {operation}: {duration:.2f}s"
    if details:
        message += f" | Detalhes: {details}"
    
    logger.info(message)


def log_api_call(endpoint: str, method: str = "GET", status_code: int = None, 
                 duration: float = None) -> None:
    """
    Log padronizado para chamadas de API.
    
    Args:
        endpoint: Endpoint da API
        method: Método HTTP
        status_code: Código de status da resposta
        duration: Duração da chamada em segundos
    """
    logger = get_logger("api_calls")
    
    message = f"API {method} {endpoint}"
    if status_code:
        message += f" | Status: {status_code}"
    if duration:
        message += f" | Duração: {duration:.2f}s"
    
    logger.info(message)


def log_error_with_context(error: Exception, context: dict = None) -> None:
    """
    Log padronizado para erros com contexto.
    
    Args:
        error: Exceção capturada
        context: Contexto adicional do erro
    """
    logger = get_logger("errors")
    
    message = f"Erro: {type(error).__name__}: {str(error)}"
    if context:
        message += f" | Contexto: {context}"
    
    logger.error(message, exc_info=True)


class LoggingContext:
    """Context manager para logging com contexto específico."""
    
    def __init__(self, operation: str, logger_name: str = None):
        """
        Inicializa o contexto de logging.
        
        Args:
            operation: Nome da operação
            logger_name: Nome do logger (opcional)
        """
        self.operation = operation
        self.logger = get_logger(logger_name or "operations")
        self.start_time = None
    
    def __enter__(self):
        """Inicia o contexto."""
        self.start_time = datetime.now()
        self.logger.info(f"Iniciando operação: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Finaliza o contexto."""
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.info(f"Operação concluída: {self.operation} ({duration:.2f}s)")
        else:
            self.logger.error(
                f"Operação falhou: {self.operation} ({duration:.2f}s) - "
                f"{exc_type.__name__}: {exc_val}"
            )
        
        return False  # Não suprimir exceções


# Configuração padrão para importação simples
def configure_default_logging():
    """Configura logging padrão para o projeto."""
    return setup_logging(
        log_level="INFO",
        log_to_file=True,
        log_dir="data/logs"
    )
