"""
Utilitário para carregar configurações de projeto de arquivos YAML/JSON.
"""

import os
import yaml
import json
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigLoader:
    """Carregador de configurações de projeto."""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Inicializa o carregador de configurações.
        
        Args:
            config_file: Caminho para o arquivo de configuração. 
                        Se None, procura por project_config.yaml ou project_config.json na raiz.
        """
        self.config_file = config_file
        self.config_data = None
        
    def load_config(self) -> Dict[str, Any]:
        """
        Carrega as configurações do arquivo.
        
        Returns:
            Dicionário com as configurações carregadas.
            
        Raises:
            FileNotFoundError: Se o arquivo de configuração não for encontrado.
            ValueError: Se o arquivo não puder ser parseado.
        """
        if self.config_data is not None:
            return self.config_data
            
        config_path = self._find_config_file()
        
        if not config_path.exists():
            raise FileNotFoundError(
                f"Arquivo de configuração não encontrado: {config_path}\n"
                f"Crie um arquivo project_config.yaml ou project_config.json na raiz do projeto."
            )
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml' or config_path.suffix.lower() == '.yml':
                    self.config_data = yaml.safe_load(f)
                elif config_path.suffix.lower() == '.json':
                    self.config_data = json.load(f)
                else:
                    raise ValueError(f"Formato de arquivo não suportado: {config_path.suffix}")
                    
        except (yaml.YAMLError, json.JSONDecodeError) as e:
            raise ValueError(f"Erro ao parsear arquivo de configuração: {e}")
            
        return self.config_data
    
    def _find_config_file(self) -> Path:
        """
        Encontra o arquivo de configuração.
        
        Returns:
            Path para o arquivo de configuração.
        """
        if self.config_file:
            return Path(self.config_file)
            
        # Procura na raiz do projeto
        root_dir = Path(__file__).parent.parent.parent
        
        # Ordem de prioridade: YAML primeiro, depois JSON
        for filename in ['project_config.yaml', 'project_config.yml', 'project_config.json']:
            config_path = root_dir / filename
            if config_path.exists():
                return config_path
                
        # Se não encontrou, retorna o padrão YAML
        return root_dir / 'project_config.yaml'
    
    def get_figma_config(self) -> Dict[str, str]:
        """
        Obtém as configurações específicas do Figma.
        
        Returns:
            Dicionário com file_key e token.
            
        Raises:
            KeyError: Se as configurações obrigatórias não estiverem presentes.
        """
        config = self.load_config()
        
        figma_config = config.get('figma', {})
        
        required_keys = ['file_key', 'token']
        missing_keys = [key for key in required_keys if key not in figma_config]
        
        if missing_keys:
            raise KeyError(
                f"Configurações obrigatórias do Figma não encontradas: {missing_keys}\n"
                f"Verifique o arquivo de configuração."
            )
            
        return {
            'file_key': figma_config['file_key'],
            'token': figma_config['token']
        }
    
    def get_processing_config(self) -> Dict[str, Any]:
        """
        Obtém as configurações de processamento.
        
        Returns:
            Dicionário com configurações de processamento.
        """
        config = self.load_config()
        
        default_processing = {
            'max_depth': 3,
            'components_only': False
        }
        
        processing_config = config.get('processing', {})
        default_processing.update(processing_config)
        
        return default_processing
    
    def get_output_config(self) -> Dict[str, Any]:
        """
        Obtém as configurações de saída.
        
        Returns:
            Dicionário com configurações de saída.
        """
        config = self.load_config()
        
        default_output = {
            'base_dir': 'data/output',
            'format': 'html'
        }
        
        output_config = config.get('output', {})
        default_output.update(output_config)
        
        return default_output


def load_project_config(config_file: Optional[str] = None) -> ConfigLoader:
    """
    Função de conveniência para carregar configurações de projeto.
    
    Args:
        config_file: Caminho opcional para o arquivo de configuração.
        
    Returns:
        Instância do ConfigLoader com configurações carregadas.
    """
    return ConfigLoader(config_file)
