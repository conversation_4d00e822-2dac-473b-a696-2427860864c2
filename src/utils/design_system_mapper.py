"""
Mapeador para integração com design systems.

Este módulo é responsável por:
- Preservar nomes originais do Figma
- Mapear componentes para design systems
- Gerar metadados para integração
- Criar índices de busca por nome/tipo
"""

import logging
import re
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class ComponentMapping:
    """Mapeamento de um componente para design system."""
    figma_id: str
    figma_name: str
    figma_type: str
    sanitized_id: str
    sanitized_class: str
    design_system_name: Optional[str] = None
    design_system_variant: Optional[str] = None
    properties: Dict[str, Any] = field(default_factory=dict)
    tokens: Dict[str, str] = field(default_factory=dict)


class DesignSystemMapper:
    """Mapeador para integração com design systems."""
    
    def __init__(self, components_data: Dict[str, Any]):
        """
        Inicializa o mapeador.
        
        Args:
            components_data: Dados dos componentes processados
        """
        self.components_data = components_data
        self.components = components_data.get('components', {})
        self.mappings: Dict[str, ComponentMapping] = {}
        self.name_index: Dict[str, List[str]] = {}
        self.type_index: Dict[str, List[str]] = {}
        
    def create_mappings(self) -> Dict[str, ComponentMapping]:
        """
        Cria mapeamentos para todos os componentes.
        
        Returns:
            Dict com mapeamentos por ID
        """
        logger.info("Criando mapeamentos para design system...")
        
        for comp_id, comp_data in self.components.items():
            mapping = self._create_component_mapping(comp_id, comp_data)
            self.mappings[comp_id] = mapping
            
            # Indexar por nome
            name_key = mapping.figma_name.lower()
            if name_key not in self.name_index:
                self.name_index[name_key] = []
            self.name_index[name_key].append(comp_id)
            
            # Indexar por tipo
            type_key = mapping.figma_type.lower()
            if type_key not in self.type_index:
                self.type_index[type_key] = []
            self.type_index[type_key].append(comp_id)
        
        logger.info(f"Mapeamentos criados: {len(self.mappings)}")
        return self.mappings
    
    def _create_component_mapping(self, comp_id: str, comp_data: Dict[str, Any]) -> ComponentMapping:
        """
        Cria mapeamento para um componente específico.
        
        Args:
            comp_id: ID do componente
            comp_data: Dados do componente
            
        Returns:
            Mapeamento do componente
        """
        figma_name = comp_data.get('name', '')
        figma_type = comp_data.get('original_type', '')
        
        # Sanitizar IDs e classes
        sanitized_id = self._sanitize_css_id(comp_id)
        sanitized_class = self._sanitize_css_class(figma_name)
        
        # Detectar componente de design system
        design_system_name, design_system_variant = self._detect_design_system_component(figma_name, comp_data)
        
        # Extrair tokens de design
        tokens = self._extract_design_tokens(comp_data)
        
        return ComponentMapping(
            figma_id=comp_id,
            figma_name=figma_name,
            figma_type=figma_type,
            sanitized_id=sanitized_id,
            sanitized_class=sanitized_class,
            design_system_name=design_system_name,
            design_system_variant=design_system_variant,
            properties=comp_data.get('properties', {}),
            tokens=tokens
        )
    
    def _detect_design_system_component(self, name: str, comp_data: Dict[str, Any]) -> tuple[Optional[str], Optional[str]]:
        """
        Detecta se o componente pertence a um design system.
        
        Args:
            name: Nome do componente
            comp_data: Dados do componente
            
        Returns:
            Tupla (nome_do_componente, variante)
        """
        # Padrões comuns de design systems
        patterns = {
            'button': r'(?i)button|btn',
            'input': r'(?i)input|field|text.*field',
            'card': r'(?i)card',
            'modal': r'(?i)modal|dialog',
            'dropdown': r'(?i)dropdown|select',
            'checkbox': r'(?i)checkbox|check',
            'radio': r'(?i)radio',
            'switch': r'(?i)switch|toggle',
            'slider': r'(?i)slider|range',
            'progress': r'(?i)progress|loading',
            'alert': r'(?i)alert|notification|toast',
            'badge': r'(?i)badge|tag|chip',
            'avatar': r'(?i)avatar|profile',
            'breadcrumb': r'(?i)breadcrumb|crumb',
            'pagination': r'(?i)pagination|pager',
            'tab': r'(?i)tab',
            'accordion': r'(?i)accordion|collapse',
            'tooltip': r'(?i)tooltip|hint',
            'popover': r'(?i)popover|popup'
        }
        
        for component_type, pattern in patterns.items():
            if re.search(pattern, name):
                # Tentar detectar variante
                variant = self._detect_variant(name, component_type)
                return component_type, variant
        
        # Verificar se é instância de componente
        if comp_data.get('type') == 'component' and 'componentId' in comp_data.get('properties', {}):
            return 'component_instance', None
        
        return None, None
    
    def _detect_variant(self, name: str, component_type: str) -> Optional[str]:
        """
        Detecta variante do componente.
        
        Args:
            name: Nome do componente
            component_type: Tipo do componente
            
        Returns:
            Variante detectada ou None
        """
        # Padrões de variantes comuns
        variant_patterns = {
            'size': r'(?i)(small|sm|medium|md|large|lg|xl|xs)',
            'color': r'(?i)(primary|secondary|success|warning|danger|error|info|light|dark)',
            'state': r'(?i)(default|hover|active|disabled|focused|pressed)',
            'style': r'(?i)(solid|outline|ghost|link|text)'
        }
        
        for variant_type, pattern in variant_patterns.items():
            match = re.search(pattern, name)
            if match:
                return match.group(1).lower()
        
        return None
    
    def _extract_design_tokens(self, comp_data: Dict[str, Any]) -> Dict[str, str]:
        """
        Extrai design tokens do componente.
        
        Args:
            comp_data: Dados do componente
            
        Returns:
            Dict com tokens extraídos
        """
        tokens = {}
        css_props = comp_data.get('css_properties', {})
        
        # Mapear propriedades CSS para tokens
        token_mapping = {
            'color': 'text-color',
            'background-color': 'bg-color',
            'border-color': 'border-color',
            'font-family': 'font-family',
            'font-size': 'font-size',
            'font-weight': 'font-weight',
            'border-radius': 'border-radius',
            'padding': 'spacing',
            'margin': 'spacing',
            'width': 'size',
            'height': 'size'
        }
        
        for css_prop, token_name in token_mapping.items():
            if css_prop in css_props:
                tokens[token_name] = css_props[css_prop]
        
        return tokens
    
    def _sanitize_css_id(self, css_id: str) -> str:
        """Sanitiza um ID para ser válido em CSS."""
        sanitized = re.sub(r'[^a-zA-Z0-9_-]', '_', css_id)
        
        if sanitized and not re.match(r'^[a-zA-Z_]', sanitized):
            sanitized = f"figma_{sanitized}"
        
        if not sanitized:
            sanitized = "figma_component"
        
        return sanitized
    
    def _sanitize_css_class(self, class_name: str) -> str:
        """Sanitiza um nome de classe para ser válido em CSS."""
        sanitized = re.sub(r'[^a-zA-Z0-9_-]', '-', class_name.lower())
        sanitized = re.sub(r'[-_]+', '-', sanitized)
        sanitized = sanitized.strip('-_')
        
        if not sanitized:
            sanitized = "component"
        
        return sanitized
    
    def find_components_by_name(self, name: str) -> List[ComponentMapping]:
        """
        Encontra componentes por nome.
        
        Args:
            name: Nome a buscar
            
        Returns:
            Lista de componentes encontrados
        """
        name_key = name.lower()
        comp_ids = self.name_index.get(name_key, [])
        return [self.mappings[comp_id] for comp_id in comp_ids]
    
    def find_components_by_type(self, comp_type: str) -> List[ComponentMapping]:
        """
        Encontra componentes por tipo.
        
        Args:
            comp_type: Tipo a buscar
            
        Returns:
            Lista de componentes encontrados
        """
        type_key = comp_type.lower()
        comp_ids = self.type_index.get(type_key, [])
        return [self.mappings[comp_id] for comp_id in comp_ids]
    
    def find_design_system_components(self) -> Dict[str, List[ComponentMapping]]:
        """
        Encontra componentes que pertencem a design systems.
        
        Returns:
            Dict agrupado por tipo de componente
        """
        design_components = {}
        
        for mapping in self.mappings.values():
            if mapping.design_system_name:
                if mapping.design_system_name not in design_components:
                    design_components[mapping.design_system_name] = []
                design_components[mapping.design_system_name].append(mapping)
        
        return design_components
    
    def generate_design_system_report(self) -> Dict[str, Any]:
        """
        Gera relatório de integração com design system.
        
        Returns:
            Relatório detalhado
        """
        design_components = self.find_design_system_components()
        
        report = {
            'total_components': len(self.mappings),
            'design_system_components': len([m for m in self.mappings.values() if m.design_system_name]),
            'component_types': {},
            'variants_found': set(),
            'tokens_extracted': 0,
            'naming_patterns': {}
        }
        
        # Analisar tipos e variantes
        for mapping in self.mappings.values():
            if mapping.design_system_name:
                comp_type = mapping.design_system_name
                if comp_type not in report['component_types']:
                    report['component_types'][comp_type] = 0
                report['component_types'][comp_type] += 1
                
                if mapping.design_system_variant:
                    report['variants_found'].add(mapping.design_system_variant)
                
                report['tokens_extracted'] += len(mapping.tokens)
        
        # Converter set para list para JSON
        report['variants_found'] = list(report['variants_found'])
        
        return report
    
    def export_mappings(self) -> Dict[str, Any]:
        """
        Exporta mapeamentos para uso externo.
        
        Returns:
            Dict com todos os mapeamentos
        """
        export_data = {
            'mappings': {},
            'indexes': {
                'by_name': self.name_index,
                'by_type': self.type_index
            },
            'design_system_report': self.generate_design_system_report()
        }
        
        # Converter mappings para dict serializável
        for comp_id, mapping in self.mappings.items():
            export_data['mappings'][comp_id] = {
                'figma_id': mapping.figma_id,
                'figma_name': mapping.figma_name,
                'figma_type': mapping.figma_type,
                'sanitized_id': mapping.sanitized_id,
                'sanitized_class': mapping.sanitized_class,
                'design_system_name': mapping.design_system_name,
                'design_system_variant': mapping.design_system_variant,
                'properties': mapping.properties,
                'tokens': mapping.tokens
            }
        
        return export_data
