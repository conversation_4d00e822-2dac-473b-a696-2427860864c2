"""
Conversor de estilos do Figma para CSS.

Este módulo implementa a conversão de propriedades visuais do Figma
para CSS, incluindo strokes, effects e fills.
"""

import logging
from typing import Dict, Any, Optional, List
from .simplified_node import SimplifiedStroke, SimplifiedEffects, StrokeWeights
from .utils import parse_paint

logger = logging.getLogger(__name__)


def build_simplified_strokes(node: Dict[str, Any]) -> SimplifiedStroke:
    """
    Constrói strokes simplificados para um node.
    
    Args:
        node: Dados do node
        
    Returns:
        SimplifiedStroke com propriedades de stroke
    """
    stroke = SimplifiedStroke()
    
    # Processar strokes
    if 'strokes' in node and node['strokes']:
        stroke.colors = []
        for stroke_paint in node['strokes']:
            if stroke_paint.get('visible', True):
                fill = parse_paint(stroke_paint)
                stroke.colors.append(fill)
    
    # Stroke weights
    stroke_weight = node.get('strokeWeight', 0)
    if stroke_weight > 0:
        # Verificar se há pesos individuais
        individual_weights = node.get('individualStrokeWeights')
        if individual_weights:
            stroke.weights = StrokeWeights(
                top=individual_weights.get('top', stroke_weight),
                right=individual_weights.get('right', stroke_weight),
                bottom=individual_weights.get('bottom', stroke_weight),
                left=individual_weights.get('left', stroke_weight)
            )
        else:
            # Usar peso uniforme
            stroke.weights = StrokeWeights(
                top=stroke_weight,
                right=stroke_weight,
                bottom=stroke_weight,
                left=stroke_weight
            )
    
    # Stroke align
    stroke_align = node.get('strokeAlign')
    if stroke_align:
        align_map = {
            'INSIDE': 'inside',
            'OUTSIDE': 'outside',
            'CENTER': 'center'
        }
        stroke.align = align_map.get(stroke_align, 'inside')
    
    # Dash pattern
    stroke_dashes = node.get('strokeDashes')
    if stroke_dashes:
        stroke.dash_pattern = stroke_dashes
    
    return stroke


def build_simplified_effects(node: Dict[str, Any]) -> SimplifiedEffects:
    """
    Constrói efeitos simplificados para um node.
    
    Args:
        node: Dados do node
        
    Returns:
        SimplifiedEffects com propriedades de efeitos
    """
    effects = SimplifiedEffects()
    
    if 'effects' not in node or not node['effects']:
        return effects
    
    drop_shadows = []
    inner_shadows = []
    blur_effect = None
    
    for effect in node['effects']:
        if not effect.get('visible', True):
            continue
        
        effect_type = effect.get('type')
        
        if effect_type == 'DROP_SHADOW':
            shadow = {
                'color': _convert_effect_color(effect.get('color', {})),
                'offset': effect.get('offset', {'x': 0, 'y': 0}),
                'radius': effect.get('radius', 0),
                'spread': effect.get('spread', 0),
                'blend_mode': effect.get('blendMode', 'NORMAL').lower()
            }
            drop_shadows.append(shadow)
        
        elif effect_type == 'INNER_SHADOW':
            shadow = {
                'color': _convert_effect_color(effect.get('color', {})),
                'offset': effect.get('offset', {'x': 0, 'y': 0}),
                'radius': effect.get('radius', 0),
                'spread': effect.get('spread', 0),
                'blend_mode': effect.get('blendMode', 'NORMAL').lower()
            }
            inner_shadows.append(shadow)
        
        elif effect_type in ['LAYER_BLUR', 'BACKGROUND_BLUR']:
            blur_effect = {
                'type': effect_type.lower(),
                'radius': effect.get('radius', 0)
            }
    
    if drop_shadows:
        effects.drop_shadow = drop_shadows
    
    if inner_shadows:
        effects.inner_shadow = inner_shadows
    
    if blur_effect:
        effects.blur = blur_effect
    
    return effects


def _convert_effect_color(color: Dict[str, Any]) -> str:
    """
    Converte cor de efeito para CSS.
    
    Args:
        color: Dados da cor do efeito
        
    Returns:
        String CSS da cor
    """
    if not color:
        return 'rgba(0, 0, 0, 0.25)'  # Padrão
    
    r = int(color.get('r', 0) * 255)
    g = int(color.get('g', 0) * 255)
    b = int(color.get('b', 0) * 255)
    a = color.get('a', 1.0)
    
    return f"rgba({r}, {g}, {b}, {a})"


def build_text_style(node: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Constrói estilo de texto simplificado.
    
    Args:
        node: Dados do node de texto
        
    Returns:
        Dict com propriedades de estilo de texto
    """
    if 'style' not in node:
        return None
    
    style = node['style']
    text_style = {}
    
    # Font family
    if 'fontFamily' in style:
        text_style['font_family'] = style['fontFamily']
    
    # Font weight
    if 'fontWeight' in style:
        text_style['font_weight'] = style['fontWeight']
    
    # Font size
    if 'fontSize' in style:
        text_style['font_size'] = style['fontSize']
    
    # Line height
    if 'lineHeightPx' in style and 'fontSize' in style:
        line_height_ratio = style['lineHeightPx'] / style['fontSize']
        text_style['line_height'] = f"{line_height_ratio}em"
    elif 'lineHeightPercent' in style:
        text_style['line_height'] = f"{style['lineHeightPercent']}%"
    
    # Letter spacing
    if 'letterSpacing' in style and style['letterSpacing'] != 0:
        if 'fontSize' in style:
            letter_spacing_percent = (style['letterSpacing'] / style['fontSize']) * 100
            text_style['letter_spacing'] = f"{letter_spacing_percent}%"
        else:
            text_style['letter_spacing'] = f"{style['letterSpacing']}px"
    
    # Text case
    if 'textCase' in style:
        case_map = {
            'UPPER': 'uppercase',
            'LOWER': 'lowercase',
            'TITLE': 'capitalize'
        }
        text_case = case_map.get(style['textCase'])
        if text_case:
            text_style['text_case'] = text_case
    
    # Text align
    if 'textAlignHorizontal' in style:
        align_map = {
            'LEFT': 'left',
            'CENTER': 'center',
            'RIGHT': 'right',
            'JUSTIFIED': 'justify'
        }
        text_align = align_map.get(style['textAlignHorizontal'])
        if text_align:
            text_style['text_align_horizontal'] = text_align
    
    if 'textAlignVertical' in style:
        valign_map = {
            'TOP': 'top',
            'CENTER': 'middle',
            'BOTTOM': 'bottom'
        }
        text_valign = valign_map.get(style['textAlignVertical'])
        if text_valign:
            text_style['text_align_vertical'] = text_valign
    
    return text_style if text_style else None


def convert_fills_to_css(fills: List[Any]) -> List[str]:
    """
    Converte fills do Figma para CSS.

    Args:
        fills: Lista de fills (podem ser dicts ou SimplifiedFill)

    Returns:
        Lista de valores CSS
    """
    from .simplified_node import SimplifiedFill

    css_values = []

    for fill in fills:
        # Se já é SimplifiedFill, usar diretamente
        if isinstance(fill, SimplifiedFill):
            simplified_fill = fill
        else:
            # Se é dict, verificar visibilidade e converter
            if isinstance(fill, dict) and not fill.get('visible', True):
                continue
            simplified_fill = parse_paint(fill)

        if simplified_fill.rgba:
            css_values.append(simplified_fill.rgba)
        elif simplified_fill.hex:
            css_values.append(simplified_fill.hex)

    return css_values


def convert_strokes_to_css(stroke: Any) -> Dict[str, str]:
    """
    Converte stroke simplificado para CSS.

    Args:
        stroke: SimplifiedStroke ou dict

    Returns:
        Dict com propriedades CSS
    """
    from .simplified_node import SimplifiedStroke

    css_props = {}

    # Se não é SimplifiedStroke, tentar converter
    if not isinstance(stroke, SimplifiedStroke):
        return css_props

    if not stroke.colors:
        return css_props

    # Border color (usar primeira cor)
    first_color = stroke.colors[0]
    if hasattr(first_color, 'rgba') and first_color.rgba:
        css_props['border-color'] = first_color.rgba
    elif hasattr(first_color, 'hex') and first_color.hex:
        css_props['border-color'] = first_color.hex

    # Border width
    if stroke.weights:
        weights = stroke.weights
        if (weights.top == weights.right == weights.bottom == weights.left):
            css_props['border-width'] = f"{weights.top}px"
        else:
            css_props['border-top-width'] = f"{weights.top}px"
            css_props['border-right-width'] = f"{weights.right}px"
            css_props['border-bottom-width'] = f"{weights.bottom}px"
            css_props['border-left-width'] = f"{weights.left}px"

    # Border style
    if stroke.dash_pattern:
        css_props['border-style'] = 'dashed'
    else:
        css_props['border-style'] = 'solid'

    return css_props


def convert_effects_to_css(effects: Any) -> Dict[str, str]:
    """
    Converte efeitos simplificados para CSS.

    Args:
        effects: SimplifiedEffects ou dict

    Returns:
        Dict com propriedades CSS
    """
    from .simplified_node import SimplifiedEffects

    css_props = {}

    # Se não é SimplifiedEffects, tentar converter
    if not isinstance(effects, SimplifiedEffects):
        return css_props

    # Box shadow
    shadows = []

    if effects.drop_shadow:
        for shadow in effects.drop_shadow:
            offset = shadow['offset']
            shadow_value = f"{offset['x']}px {offset['y']}px {shadow['radius']}px"
            if shadow.get('spread', 0) != 0:
                shadow_value += f" {shadow['spread']}px"
            shadow_value += f" {shadow['color']}"
            shadows.append(shadow_value)

    if effects.inner_shadow:
        for shadow in effects.inner_shadow:
            offset = shadow['offset']
            shadow_value = f"inset {offset['x']}px {offset['y']}px {shadow['radius']}px"
            if shadow.get('spread', 0) != 0:
                shadow_value += f" {shadow['spread']}px"
            shadow_value += f" {shadow['color']}"
            shadows.append(shadow_value)

    if shadows:
        css_props['box-shadow'] = ', '.join(shadows)

    # Filter blur
    if effects.blur:
        blur_radius = effects.blur['radius']
        if blur_radius > 0:
            css_props['filter'] = f"blur({blur_radius}px)"

    return css_props
