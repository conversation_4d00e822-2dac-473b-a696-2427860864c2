"""
Utilitários para conversão do Figma.

Este módulo contém funções auxiliares para processamento de dados do Figma,
baseadas na implementação do MCP server.
"""

import re
import uuid
from typing import Dict, Any, Optional, List, Union
from .simplified_node import StyleId, ColorValue, SimplifiedFill


def has_value(key: str, obj: Dict[str, Any], validator=None) -> bool:
    """
    Verifica se um objeto tem uma propriedade com valor válido.
    
    Args:
        key: Chave a verificar
        obj: Objeto a verificar
        validator: Função opcional para validar o valor
        
    Returns:
        True se a propriedade existe e tem valor válido
    """
    if key not in obj:
        return False
    
    value = obj[key]
    
    if value is None:
        return False
    
    if validator:
        return validator(value)
    
    # Verificações padrão
    if isinstance(value, str):
        return len(value.strip()) > 0
    elif isinstance(value, (list, dict)):
        return len(value) > 0
    elif isinstance(value, (int, float)):
        return True
    elif isinstance(value, bool):
        return True
    
    return value is not None


def is_truthy(value: Any) -> bool:
    """Verifica se um valor é truthy."""
    return bool(value)


def is_rectangle_corner_radii(value: Any) -> bool:
    """Verifica se é um array válido de corner radii."""
    return (isinstance(value, list) and 
            len(value) == 4 and 
            all(isinstance(x, (int, float)) for x in value))


def is_visible(node: Dict[str, Any]) -> bool:
    """Verifica se um node é visível."""
    return node.get('visible', True)


def remove_empty_keys(obj: Any) -> Any:
    """
    Remove chaves vazias de um objeto recursivamente.
    
    Args:
        obj: Objeto a limpar
        
    Returns:
        Objeto limpo
    """
    if isinstance(obj, dict):
        cleaned = {}
        for key, value in obj.items():
            cleaned_value = remove_empty_keys(value)
            if cleaned_value is not None and cleaned_value != {} and cleaned_value != []:
                cleaned[key] = cleaned_value
        return cleaned
    elif isinstance(obj, list):
        return [remove_empty_keys(item) for item in obj if item is not None]
    else:
        return obj


def generate_var_id(prefix: str) -> StyleId:
    """
    Gera um ID único para uma variável.
    
    Args:
        prefix: Prefixo para o ID
        
    Returns:
        ID único
    """
    # Gerar um ID curto baseado em UUID
    short_id = str(uuid.uuid4())[:8]
    return f"{prefix}_{short_id}"


def pixel_round(value: float) -> int:
    """
    Arredonda um valor para o pixel mais próximo.
    
    Args:
        value: Valor a arredondar
        
    Returns:
        Valor arredondado
    """
    return round(value)


def generate_css_shorthand(values: Dict[str, float]) -> str:
    """
    Gera CSS shorthand para propriedades como padding/margin.
    
    Args:
        values: Dict com top, right, bottom, left
        
    Returns:
        String CSS shorthand
    """
    top = values.get('top', 0)
    right = values.get('right', 0)
    bottom = values.get('bottom', 0)
    left = values.get('left', 0)
    
    # Otimizar shorthand
    if top == right == bottom == left:
        return f"{top}px"
    elif top == bottom and left == right:
        return f"{top}px {right}px"
    elif left == right:
        return f"{top}px {right}px {bottom}px"
    else:
        return f"{top}px {right}px {bottom}px {left}px"


def parse_paint(paint: Dict[str, Any]) -> SimplifiedFill:
    """
    Converte um paint do Figma para SimplifiedFill.
    
    Args:
        paint: Dados do paint do Figma
        
    Returns:
        SimplifiedFill processado
    """
    fill = SimplifiedFill()
    
    fill.type = paint.get('type')
    
    if paint.get('type') == 'SOLID' and 'color' in paint:
        color = paint['color']
        opacity = paint.get('opacity', 1.0)
        
        # Converter para RGB
        r = int(color.get('r', 0) * 255)
        g = int(color.get('g', 0) * 255)
        b = int(color.get('b', 0) * 255)
        a = color.get('a', 1.0) * opacity
        
        # Gerar hex
        fill.hex = f"#{r:02x}{g:02x}{b:02x}"
        
        # Gerar RGBA se necessário
        if a != 1.0:
            fill.rgba = f"rgba({r}, {g}, {b}, {a})"
        
        fill.opacity = opacity
    
    elif paint.get('type') == 'GRADIENT_LINEAR':
        # Processar gradiente linear
        if 'gradientHandlePositions' in paint:
            fill.gradient_handle_positions = paint['gradientHandlePositions']
        
        if 'gradientStops' in paint:
            stops = []
            for stop in paint['gradientStops']:
                color = stop.get('color', {})
                r = int(color.get('r', 0) * 255)
                g = int(color.get('g', 0) * 255)
                b = int(color.get('b', 0) * 255)
                a = color.get('a', 1.0)
                
                stops.append({
                    'position': stop.get('position', 0),
                    'color': f"rgba({r}, {g}, {b}, {a})"
                })
            fill.gradient_stops = stops
    
    elif paint.get('type') == 'IMAGE':
        # Processar imagem
        fill.image_ref = paint.get('imageRef', '')
        fill.scale_mode = paint.get('scaleMode', 'FILL')
    
    return fill


def sanitize_css_class_name(name: str) -> str:
    """
    Sanitiza um nome para uso como classe CSS.
    
    Args:
        name: Nome original
        
    Returns:
        Nome sanitizado para CSS
    """
    # Converter para lowercase
    name = name.lower()
    
    # Remover caracteres especiais e substituir por hífen
    name = re.sub(r'[^a-z0-9\-_]', '-', name)
    
    # Remover hífens múltiplos
    name = re.sub(r'-+', '-', name)
    
    # Remover hífens no início e fim
    name = name.strip('-')
    
    # Garantir que não comece com número
    if name and name[0].isdigit():
        name = f"c-{name}"
    
    return name or "component"


def find_or_create_var(global_vars: Dict[str, Any], value: Any, prefix: str) -> StyleId:
    """
    Encontra ou cria uma variável global.
    
    Args:
        global_vars: Dicionário de variáveis globais
        value: Valor a armazenar
        prefix: Prefixo para o ID da variável
        
    Returns:
        ID da variável
    """
    # Verificar se o valor já existe
    for var_id, existing_value in global_vars.items():
        if existing_value == value:
            return var_id
    
    # Criar nova variável
    var_id = generate_var_id(prefix)
    global_vars[var_id] = value
    return var_id
