"""
Conversor de layout do Figma para CSS.

Este módulo implementa a conversão de propriedades de layout do Figma
para CSS flexbox, baseado na lógica do MCP server.
"""

import logging
from typing import Dict, Any, Optional, List
from .simplified_node import SimplifiedLayout
from .utils import generate_css_shorthand, pixel_round

logger = logging.getLogger(__name__)


def is_frame(node: Optional[Dict[str, Any]]) -> bool:
    """Verifica se um node é um FRAME."""
    if node is None:
        return False
    return node.get('type') == 'FRAME'


def is_layout(node: Optional[Dict[str, Any]]) -> bool:
    """Verifica se um node tem propriedades de layout."""
    if node is None:
        return False
    layout_props = [
        'layoutSizingHorizontal', 'layoutSizingVertical',
        'layoutAlign', 'layoutPositioning'
    ]
    return any(prop in node for prop in layout_props)


def is_rectangle(bbox_key: str, node: Optional[Dict[str, Any]]) -> bool:
    """Verifica se um node tem bounding box válido."""
    if node is None or bbox_key not in node:
        return False

    bbox = node[bbox_key]
    return (isinstance(bbox, dict) and
            'width' in bbox and 'height' in bbox and
            bbox['width'] > 0 and bbox['height'] > 0)


def is_in_auto_layout_flow(node: Dict[str, Any], parent: Optional[Dict[str, Any]]) -> bool:
    """Verifica se um node está em um fluxo de auto-layout."""
    if not parent or not is_frame(parent):
        return False

    # Se o parent tem layoutMode e o node não é absolute
    has_layout_mode = parent.get('layoutMode') in ['HORIZONTAL', 'VERTICAL']
    is_not_absolute = node.get('layoutPositioning') != 'ABSOLUTE'

    return has_layout_mode and is_not_absolute


def convert_align(axis_align: Optional[str], stretch_info: Optional[Dict[str, Any]] = None) -> Optional[str]:
    """
    Converte alinhamento do Figma para CSS.
    
    Args:
        axis_align: Alinhamento do eixo do Figma
        stretch_info: Informações sobre stretch (opcional)
        
    Returns:
        Valor CSS de alinhamento
    """
    if stretch_info and stretch_info.get('should_stretch'):
        return 'stretch'
    
    align_map = {
        'MIN': None,  # flex-start é o padrão
        'MAX': 'flex-end',
        'CENTER': 'center',
        'SPACE_BETWEEN': 'space-between',
        'BASELINE': 'baseline'
    }
    
    return align_map.get(axis_align)


def convert_self_align(align: Optional[str]) -> Optional[str]:
    """Converte align-self do Figma para CSS."""
    align_map = {
        'MIN': None,  # flex-start é o padrão
        'MAX': 'flex-end',
        'CENTER': 'center',
        'STRETCH': 'stretch'
    }
    
    return align_map.get(align)


def convert_sizing(sizing: Optional[str]) -> Optional[str]:
    """Converte sizing do Figma para CSS."""
    sizing_map = {
        'FIXED': 'fixed',
        'FILL': 'fill',
        'HUG': 'hug'
    }
    
    return sizing_map.get(sizing)


def get_direction(axis: str, mode: str) -> str:
    """
    Determina a direção baseada no eixo e modo.
    
    Args:
        axis: 'primary' ou 'counter'
        mode: 'row' ou 'column'
        
    Returns:
        'horizontal' ou 'vertical'
    """
    if axis == 'primary':
        return 'horizontal' if mode == 'row' else 'vertical'
    else:  # counter
        return 'vertical' if mode == 'row' else 'horizontal'


def build_simplified_frame_values(node: Dict[str, Any]) -> SimplifiedLayout:
    """
    Constrói valores de layout para um FRAME.
    
    Args:
        node: Dados do node FRAME
        
    Returns:
        SimplifiedLayout com propriedades do frame
    """
    if not is_frame(node):
        return SimplifiedLayout(mode="none")
    
    layout_mode = node.get('layoutMode', 'NONE')
    
    if layout_mode == 'NONE':
        mode = "none"
    elif layout_mode == 'HORIZONTAL':
        mode = "row"
    elif layout_mode == 'VERTICAL':
        mode = "column"
    else:
        mode = "none"
    
    frame_values = SimplifiedLayout(mode=mode)
    
    # Overflow scroll
    overflow_scroll = []
    overflow_direction = node.get('overflowDirection', [])
    if 'HORIZONTAL' in overflow_direction:
        overflow_scroll.append('x')
    if 'VERTICAL' in overflow_direction:
        overflow_scroll.append('y')
    
    if overflow_scroll:
        frame_values.overflow_scroll = overflow_scroll
    
    if mode == "none":
        return frame_values
    
    # Alinhamento
    children = node.get('children', [])
    
    # Justify content (eixo principal)
    primary_align = node.get('primaryAxisAlignItems', 'MIN')
    frame_values.justify_content = convert_align(primary_align)
    
    # Align items (eixo cruzado)
    counter_align = node.get('counterAxisAlignItems', 'MIN')
    frame_values.align_items = convert_align(counter_align)
    
    # Align self
    layout_align = node.get('layoutAlign')
    frame_values.align_self = convert_self_align(layout_align)
    
    # Wrap
    if node.get('layoutWrap') == 'WRAP':
        frame_values.wrap = True
    
    # Gap
    item_spacing = node.get('itemSpacing')
    if item_spacing:
        frame_values.gap = f"{item_spacing}px"
    
    # Padding
    padding_values = {}
    for side in ['Top', 'Right', 'Bottom', 'Left']:
        padding_key = f'padding{side}'
        if padding_key in node:
            padding_values[side.lower()] = node[padding_key]
    
    if padding_values:
        frame_values.padding = generate_css_shorthand(padding_values)
    
    return frame_values


def build_simplified_layout_values(
    node: Dict[str, Any], 
    parent: Optional[Dict[str, Any]], 
    mode: str
) -> Optional[SimplifiedLayout]:
    """
    Constrói valores de layout para um node.
    
    Args:
        node: Dados do node
        parent: Dados do node pai
        mode: Modo de layout ('row', 'column', 'none')
        
    Returns:
        SimplifiedLayout com propriedades do layout
    """
    if not is_layout(node):
        return None
    
    layout_values = SimplifiedLayout(mode=mode)
    
    # Sizing
    horizontal_sizing = convert_sizing(node.get('layoutSizingHorizontal'))
    vertical_sizing = convert_sizing(node.get('layoutSizingVertical'))
    
    if horizontal_sizing or vertical_sizing:
        layout_values.sizing = {}
        if horizontal_sizing:
            layout_values.sizing['horizontal'] = horizontal_sizing
        if vertical_sizing:
            layout_values.sizing['vertical'] = vertical_sizing
    
    # Positioning (apenas se não estiver em auto-layout ou se for absolute)
    if (is_frame(parent) and not is_in_auto_layout_flow(node, parent)):
        
        if node.get('layoutPositioning') == 'ABSOLUTE':
            layout_values.position = 'absolute'
        
        # Posição relativa ao pai
        if ('absoluteBoundingBox' in node and parent and 
            'absoluteBoundingBox' in parent):
            
            node_bbox = node['absoluteBoundingBox']
            parent_bbox = parent['absoluteBoundingBox']
            
            layout_values.location_relative_to_parent = {
                'x': pixel_round(node_bbox['x'] - parent_bbox['x']),
                'y': pixel_round(node_bbox['y'] - parent_bbox['y'])
            }
    
    # Dimensões
    if is_rectangle('absoluteBoundingBox', node):
        bbox = node['absoluteBoundingBox']
        dimensions = {}
        
        # Incluir dimensões baseadas no modo de layout
        if mode == "row":
            # AutoLayout row
            if (not node.get('layoutGrow') and 
                node.get('layoutSizingHorizontal') == 'FIXED'):
                dimensions['width'] = pixel_round(bbox['width'])
            
            if (node.get('layoutAlign') != 'STRETCH' and 
                node.get('layoutSizingVertical') == 'FIXED'):
                dimensions['height'] = pixel_round(bbox['height'])
        
        elif mode == "column":
            # AutoLayout column
            if (node.get('layoutAlign') != 'STRETCH' and 
                node.get('layoutSizingHorizontal') == 'FIXED'):
                dimensions['width'] = pixel_round(bbox['width'])
            
            if (not node.get('layoutGrow') and 
                node.get('layoutSizingVertical') == 'FIXED'):
                dimensions['height'] = pixel_round(bbox['height'])
            
            # Aspect ratio
            if node.get('preserveRatio'):
                dimensions['aspect_ratio'] = bbox['width'] / bbox['height']
        
        else:
            # Não é AutoLayout
            if (not node.get('layoutSizingHorizontal') or 
                node.get('layoutSizingHorizontal') == 'FIXED'):
                dimensions['width'] = pixel_round(bbox['width'])
            
            if (not node.get('layoutSizingVertical') or 
                node.get('layoutSizingVertical') == 'FIXED'):
                dimensions['height'] = pixel_round(bbox['height'])
        
        if dimensions:
            layout_values.dimensions = dimensions
    
    return layout_values


def build_simplified_layout(
    node: Dict[str, Any], 
    parent: Optional[Dict[str, Any]] = None
) -> SimplifiedLayout:
    """
    Constrói layout simplificado para um node.
    
    Args:
        node: Dados do node
        parent: Dados do node pai (opcional)
        
    Returns:
        SimplifiedLayout completo
    """
    frame_values = build_simplified_frame_values(node)
    layout_values = build_simplified_layout_values(node, parent, frame_values.mode)
    
    if layout_values:
        # Combinar valores do frame e layout
        for key, value in layout_values.__dict__.items():
            if value is not None and key != 'mode':
                setattr(frame_values, key, value)
    
    return frame_values
