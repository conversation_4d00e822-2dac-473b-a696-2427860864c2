"""
Conversor principal do Figma para estruturas simplificadas.

Este módulo implementa a lógica principal de conversão baseada no MCP server,
transformando dados brutos do Figma em estruturas otimizadas para geração de código.
"""

import logging
from typing import Dict, List, Any, Optional
from .simplified_node import (
    SimplifiedDesign, SimplifiedNode, GlobalVars, BoundingBox,
    ComponentProperties
)
from .utils import (
    has_value, is_truthy, is_rectangle_corner_radii, is_visible,
    remove_empty_keys, find_or_create_var
)
from .layout_converter import build_simplified_layout
from .style_converter import (
    build_simplified_strokes, build_simplified_effects, build_text_style
)

logger = logging.getLogger(__name__)


class FigmaConverter:
    """
    Conversor principal do Figma para estruturas simplificadas.
    
    Esta classe implementa a lógica de conversão superior baseada no MCP server,
    criando estruturas otimizadas para geração de código HTML/CSS.
    """
    
    def __init__(self):
        """Inicializa o conversor."""
        self.global_vars = GlobalVars()
    
    def parse_figma_response(self, data: Dict[str, Any]) -> SimplifiedDesign:
        """
        Converte resposta da API do Figma para SimplifiedDesign.
        
        Args:
            data: Dados da API do Figma (GetFileResponse ou GetFileNodesResponse)
            
        Returns:
            SimplifiedDesign processado
        """
        logger.info("Iniciando conversão de dados do Figma...")
        
        # Reset global vars para nova conversão
        self.global_vars = GlobalVars()
        
        # Determinar tipo de resposta e extrair nodes
        nodes_to_parse = self._extract_nodes_from_response(data)
        
        # Processar components e component sets
        components = self._extract_components(data)
        component_sets = self._extract_component_sets(data)
        
        # Converter nodes para SimplifiedNode
        simplified_nodes = []
        for node in nodes_to_parse:
            if is_visible(node):
                simplified_node = self._parse_node(node)
                if simplified_node:
                    simplified_nodes.append(simplified_node)
        
        # Criar SimplifiedDesign
        simplified_design = SimplifiedDesign(
            name=data.get('name', ''),
            last_modified=data.get('lastModified', ''),
            thumbnail_url=data.get('thumbnailUrl', ''),
            nodes=simplified_nodes,
            components=components,
            component_sets=component_sets,
            global_vars=self.global_vars
        )
        
        logger.info(f"Conversão concluída: {len(simplified_nodes)} nodes processados")
        return remove_empty_keys(simplified_design)
    
    def _extract_nodes_from_response(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extrai nodes da resposta da API."""
        if 'nodes' in data:
            # GetFileNodesResponse
            nodes = []
            for node_response in data['nodes'].values():
                if 'document' in node_response:
                    nodes.append(node_response['document'])
            return nodes
        elif 'document' in data:
            # GetFileResponse
            return data['document'].get('children', [])
        else:
            logger.warning("Formato de resposta não reconhecido")
            return []
    
    def _extract_components(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai components da resposta."""
        components = {}
        
        if 'components' in data:
            components.update(data['components'])
        
        if 'nodes' in data:
            for node_response in data['nodes'].values():
                if 'components' in node_response:
                    components.update(node_response['components'])
        
        return components
    
    def _extract_component_sets(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai component sets da resposta."""
        component_sets = {}
        
        if 'componentSets' in data:
            component_sets.update(data['componentSets'])
        
        if 'nodes' in data:
            for node_response in data['nodes'].values():
                if 'componentSets' in node_response:
                    component_sets.update(node_response['componentSets'])
        
        return component_sets
    
    def _parse_node(
        self, 
        node: Dict[str, Any], 
        parent: Optional[Dict[str, Any]] = None
    ) -> Optional[SimplifiedNode]:
        """
        Converte um node do Figma para SimplifiedNode.
        
        Args:
            node: Dados do node
            parent: Node pai (opcional)
            
        Returns:
            SimplifiedNode ou None se inválido
        """
        node_id = node.get('id', '')
        node_name = node.get('name', '')
        node_type = node.get('type', '')
        
        if not node_id:
            return None
        
        # Criar SimplifiedNode básico
        simplified = SimplifiedNode(
            id=node_id,
            name=node_name,
            type=node_type
        )
        
        # Processar propriedades específicas de INSTANCE
        if node_type == 'INSTANCE':
            if has_value('componentId', node):
                simplified.component_id = node['componentId']
            
            if has_value('componentProperties', node):
                simplified.component_properties = self._parse_component_properties(
                    node['componentProperties']
                )
        
        # Processar bounding box
        if has_value('absoluteBoundingBox', node):
            bbox = node['absoluteBoundingBox']
            simplified.bounding_box = BoundingBox(
                x=bbox.get('x', 0),
                y=bbox.get('y', 0),
                width=bbox.get('width', 0),
                height=bbox.get('height', 0)
            )
        
        # Processar texto
        if has_value('characters', node, is_truthy):
            simplified.text = node['characters']
        
        # Processar estilo de texto
        text_style = build_text_style(node)
        if text_style:
            simplified.text_style = find_or_create_var(
                self.global_vars.styles, text_style, 'style'
            )
        
        # Processar fills
        if has_value('fills', node) and isinstance(node['fills'], list) and node['fills']:
            from .utils import parse_paint
            fills = [parse_paint(fill) for fill in node['fills'] if fill.get('visible', True)]
            if fills:
                simplified.fills = find_or_create_var(
                    self.global_vars.styles, fills, 'fill'
                )
        
        # Processar strokes
        strokes = build_simplified_strokes(node)
        if strokes.colors:
            simplified.strokes = find_or_create_var(
                self.global_vars.styles, strokes, 'stroke'
            )
        
        # Processar effects
        effects = build_simplified_effects(node)
        if any(getattr(effects, attr) for attr in ['drop_shadow', 'inner_shadow', 'blur']):
            simplified.effects = find_or_create_var(
                self.global_vars.styles, effects, 'effect'
            )
        
        # Processar layout
        layout = build_simplified_layout(node, parent)
        if len(layout.__dict__) > 1:  # Mais que apenas 'mode'
            simplified.layout = find_or_create_var(
                self.global_vars.styles, layout, 'layout'
            )
        
        # Processar opacity
        if has_value('opacity', node) and isinstance(node['opacity'], (int, float)) and node['opacity'] != 1:
            simplified.opacity = node['opacity']
        
        # Processar border radius
        if has_value('cornerRadius', node) and isinstance(node['cornerRadius'], (int, float)):
            simplified.border_radius = f"{node['cornerRadius']}px"
        
        if has_value('rectangleCornerRadii', node, is_rectangle_corner_radii):
            radii = node['rectangleCornerRadii']
            simplified.border_radius = f"{radii[0]}px {radii[1]}px {radii[2]}px {radii[3]}px"
        
        # Processar children recursivamente
        if has_value('children', node) and len(node['children']) > 0:
            children = []
            for child_data in node['children']:
                if is_visible(child_data):
                    child_node = self._parse_node(child_data, node)
                    if child_node:
                        children.append(child_node)
            
            if children:
                simplified.children = children
        
        # Converter VECTOR para IMAGE-SVG
        if node_type == 'VECTOR':
            simplified.type = 'IMAGE-SVG'
        
        return simplified
    
    def _parse_component_properties(
        self, 
        component_props: Dict[str, Any]
    ) -> List[ComponentProperties]:
        """
        Converte propriedades de componente do Figma.
        
        Args:
            component_props: Propriedades do componente
            
        Returns:
            Lista de ComponentProperties
        """
        properties = []
        
        for name, prop_data in component_props.items():
            properties.append(ComponentProperties(
                name=name,
                value=str(prop_data.get('value', '')),
                type=prop_data.get('type', '')
            ))
        
        return properties
