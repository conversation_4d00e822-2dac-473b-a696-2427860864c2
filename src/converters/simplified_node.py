"""
Estruturas de dados simplificadas para conversão do Figma.

Este módulo implementa estruturas equivalentes ao MCP server para
representar dados do Figma de forma otimizada para geração de código.
"""

import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


@dataclass
class BoundingBox:
    """Representa uma caixa delimitadora."""
    x: float
    y: float
    width: float
    height: float


@dataclass
class TextStyle:
    """Representa propriedades de estilo de texto."""
    font_family: Optional[str] = None
    font_weight: Optional[int] = None
    font_size: Optional[float] = None
    line_height: Optional[str] = None
    letter_spacing: Optional[str] = None
    text_case: Optional[str] = None
    text_align_horizontal: Optional[str] = None
    text_align_vertical: Optional[str] = None


@dataclass
class StrokeWeights:
    """Representa pesos de stroke para cada lado."""
    top: float
    right: float
    bottom: float
    left: float


@dataclass
class SimplifiedLayout:
    """Representa propriedades de layout simplificadas."""
    mode: str = "none"  # "none", "row", "column"
    justify_content: Optional[str] = None
    align_items: Optional[str] = None
    align_self: Optional[str] = None
    wrap: Optional[bool] = None
    gap: Optional[str] = None
    location_relative_to_parent: Optional[Dict[str, float]] = None
    dimensions: Optional[Dict[str, float]] = None
    padding: Optional[str] = None
    sizing: Optional[Dict[str, str]] = None
    overflow_scroll: Optional[List[str]] = None
    position: Optional[str] = None


@dataclass
class ColorValue:
    """Representa um valor de cor."""
    hex: str
    opacity: float


@dataclass
class SimplifiedFill:
    """Representa um preenchimento simplificado."""
    type: Optional[str] = None
    hex: Optional[str] = None
    rgba: Optional[str] = None
    opacity: Optional[float] = None
    image_ref: Optional[str] = None
    scale_mode: Optional[str] = None
    gradient_handle_positions: Optional[List[Dict[str, float]]] = None
    gradient_stops: Optional[List[Dict[str, Any]]] = None


@dataclass
class SimplifiedStroke:
    """Representa propriedades de stroke simplificadas."""
    colors: List[SimplifiedFill] = field(default_factory=list)
    weights: Optional[StrokeWeights] = None
    align: Optional[str] = None
    dash_pattern: Optional[List[float]] = None


@dataclass
class SimplifiedEffects:
    """Representa efeitos simplificados."""
    drop_shadow: Optional[List[Dict[str, Any]]] = None
    inner_shadow: Optional[List[Dict[str, Any]]] = None
    blur: Optional[Dict[str, Any]] = None


@dataclass
class ComponentProperties:
    """Representa propriedades de componente."""
    name: str
    value: str
    type: str


@dataclass
class SimplifiedNode:
    """
    Representa um node simplificado do Figma.
    
    Esta estrutura é baseada na implementação do MCP server e otimizada
    para geração de código HTML/CSS de alta qualidade.
    """
    id: str
    name: str
    type: str  # e.g. FRAME, TEXT, INSTANCE, RECTANGLE, etc.
    
    # Geometry
    bounding_box: Optional[BoundingBox] = None
    
    # Text
    text: Optional[str] = None
    text_style: Optional[str] = None  # Reference to global style
    
    # Appearance
    fills: Optional[str] = None  # Reference to global fills
    styles: Optional[str] = None  # Reference to global styles
    strokes: Optional[str] = None  # Reference to global strokes
    effects: Optional[str] = None  # Reference to global effects
    opacity: Optional[float] = None
    border_radius: Optional[str] = None
    
    # Layout & alignment
    layout: Optional[str] = None  # Reference to global layout
    
    # Component-specific
    component_id: Optional[str] = None
    component_properties: Optional[List[ComponentProperties]] = None
    
    # Children
    children: Optional[List['SimplifiedNode']] = None


@dataclass
class GlobalVars:
    """Armazena variáveis globais para reutilização."""
    styles: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SimplifiedDesign:
    """
    Representa um design completo simplificado.
    
    Esta é a estrutura principal que contém todos os dados
    processados de um arquivo Figma.
    """
    name: str
    last_modified: str
    thumbnail_url: str
    nodes: List[SimplifiedNode]
    components: Dict[str, Any] = field(default_factory=dict)
    component_sets: Dict[str, Any] = field(default_factory=dict)
    global_vars: GlobalVars = field(default_factory=GlobalVars)


# Type aliases para compatibilidade
StyleId = str
CSSRGBAColor = str
CSSHexColor = str
SimplifiedFillType = Union[SimplifiedFill, CSSRGBAColor, CSSHexColor]
