"""
Gerador de CSS avançado a partir de componentes extraídos do Figma.

Este módulo é responsável por:
- Extrair design tokens do Figma
- Gerar CSS otimizado e organizado
- Criar variáveis CSS customizadas
- Implementar responsividade
"""

import logging
from typing import Dict, List, Any, Optional, Set
from collections import defaultdict
import re

logger = logging.getLogger(__name__)


class CSSGenerator:
    """Gerador de CSS avançado para componentes do Figma."""
    
    def __init__(self, components_data: Dict[str, Any]):
        """
        Inicializa o gerador de CSS.
        
        Args:
            components_data: Dados dos componentes processados
        """
        self.components_data = components_data
        self.components = components_data.get('components', {})
        self.design_tokens = {}
        self.css_variables = {}
        
    def extract_design_tokens(self) -> Dict[str, Any]:
        """
        Extrai design tokens dos componentes.
        
        Returns:
            Dict com design tokens organizados
        """
        logger.info("Extraindo design tokens...")
        
        tokens = {
            'colors': set(),
            'fonts': set(),
            'font_sizes': set(),
            'font_weights': set(),
            'spacing': set(),
            'border_radius': set(),
            'shadows': set()
        }
        
        for comp_data in self.components.values():
            css_props = comp_data.get('css_properties', {})
            
            # Cores
            for prop in ['color', 'background-color', 'border-color']:
                if prop in css_props:
                    tokens['colors'].add(css_props[prop])
            
            # Fontes
            if 'font-family' in css_props:
                tokens['fonts'].add(css_props['font-family'])
            
            if 'font-size' in css_props:
                tokens['font_sizes'].add(css_props['font-size'])
            
            if 'font-weight' in css_props:
                tokens['font_weights'].add(css_props['font-weight'])
            
            # Espaçamento
            for prop in ['padding', 'margin', 'gap']:
                if prop in css_props:
                    tokens['spacing'].add(css_props[prop])
            
            # Border radius
            if 'border-radius' in css_props:
                tokens['border_radius'].add(css_props['border-radius'])
        
        # Converter sets para listas ordenadas
        for key in tokens:
            tokens[key] = sorted(list(tokens[key]))
        
        self.design_tokens = tokens
        logger.info(f"Design tokens extraídos: {len(tokens)} categorias")
        
        return tokens
    
    def generate_css_variables(self) -> str:
        """
        Gera variáveis CSS customizadas baseadas nos design tokens.
        
        Returns:
            CSS com variáveis customizadas
        """
        if not self.design_tokens:
            self.extract_design_tokens()
        
        css_vars = [":root {"]
        
        # Cores
        colors = self.design_tokens.get('colors', [])
        for i, color in enumerate(colors):
            var_name = f"--color-{i+1}"
            css_vars.append(f"  {var_name}: {color};")
            self.css_variables[color] = var_name
        
        # Fontes
        fonts = self.design_tokens.get('fonts', [])
        for i, font in enumerate(fonts):
            var_name = f"--font-family-{i+1}"
            css_vars.append(f"  {var_name}: {font};")
            self.css_variables[font] = var_name
        
        # Tamanhos de fonte
        font_sizes = self.design_tokens.get('font_sizes', [])
        for i, size in enumerate(font_sizes):
            var_name = f"--font-size-{i+1}"
            css_vars.append(f"  {var_name}: {size};")
            self.css_variables[size] = var_name
        
        # Espaçamentos
        spacing = self.design_tokens.get('spacing', [])
        for i, space in enumerate(spacing):
            var_name = f"--spacing-{i+1}"
            css_vars.append(f"  {var_name}: {space};")
            self.css_variables[space] = var_name
        
        css_vars.append("}")
        
        return "\n".join(css_vars)
    
    def generate_utility_classes(self) -> str:
        """
        Gera classes utilitárias baseadas nos design tokens.
        
        Returns:
            CSS com classes utilitárias
        """
        css_classes = []
        
        # Classes de espaçamento
        css_classes.append("/* Utility Classes - Spacing */")
        for i in range(1, 9):
            spacing = f"{i * 4}px"
            css_classes.extend([
                f".m-{i} {{ margin: {spacing}; }}",
                f".mt-{i} {{ margin-top: {spacing}; }}",
                f".mr-{i} {{ margin-right: {spacing}; }}",
                f".mb-{i} {{ margin-bottom: {spacing}; }}",
                f".ml-{i} {{ margin-left: {spacing}; }}",
                f".p-{i} {{ padding: {spacing}; }}",
                f".pt-{i} {{ padding-top: {spacing}; }}",
                f".pr-{i} {{ padding-right: {spacing}; }}",
                f".pb-{i} {{ padding-bottom: {spacing}; }}",
                f".pl-{i} {{ padding-left: {spacing}; }}"
            ])
        
        # Classes de texto
        css_classes.append("\n/* Utility Classes - Typography */")
        css_classes.extend([
            ".text-left { text-align: left; }",
            ".text-center { text-align: center; }",
            ".text-right { text-align: right; }",
            ".text-justify { text-align: justify; }",
            ".font-bold { font-weight: bold; }",
            ".font-normal { font-weight: normal; }",
            ".font-light { font-weight: 300; }"
        ])
        
        # Classes de layout
        css_classes.append("\n/* Utility Classes - Layout */")
        css_classes.extend([
            ".flex { display: flex; }",
            ".flex-col { flex-direction: column; }",
            ".flex-row { flex-direction: row; }",
            ".items-center { align-items: center; }",
            ".items-start { align-items: flex-start; }",
            ".items-end { align-items: flex-end; }",
            ".justify-center { justify-content: center; }",
            ".justify-start { justify-content: flex-start; }",
            ".justify-end { justify-content: flex-end; }",
            ".justify-between { justify-content: space-between; }",
            ".w-full { width: 100%; }",
            ".h-full { height: 100%; }",
            ".relative { position: relative; }",
            ".absolute { position: absolute; }",
            ".hidden { display: none; }",
            ".block { display: block; }",
            ".inline { display: inline; }",
            ".inline-block { display: inline-block; }"
        ])
        
        return "\n".join(css_classes)
    
    def generate_component_css(self, use_variables: bool = True) -> str:
        """
        Gera CSS específico para componentes.
        
        Args:
            use_variables: Se deve usar variáveis CSS
            
        Returns:
            CSS dos componentes
        """
        css_rules = []
        
        for comp_id, comp_data in self.components.items():
            # Usar ID sanitizado
            sanitized_id = self._sanitize_css_id(comp_id)
            css_selector = f"#{sanitized_id}"
            css_properties = comp_data.get('css_properties', {})

            if css_properties:
                css_rule = f"{css_selector} {{"

                for prop, value in css_properties.items():
                    # Usar variáveis CSS se disponível e solicitado
                    if use_variables and value in self.css_variables:
                        value = f"var({self.css_variables[value]})"

                    css_rule += f"\n  {prop}: {value};"

                css_rule += "\n}"
                css_rules.append(css_rule)
        
        return "\n\n".join(css_rules)

    def _sanitize_css_id(self, css_id: str) -> str:
        """
        Sanitiza um ID para ser válido em CSS.

        Args:
            css_id: ID original

        Returns:
            ID sanitizado
        """
        import re

        # Substituir caracteres inválidos por underscores
        sanitized = re.sub(r'[^a-zA-Z0-9_-]', '_', css_id)

        # Garantir que comece com letra ou underscore
        if sanitized and not re.match(r'^[a-zA-Z_]', sanitized):
            sanitized = f"figma_{sanitized}"

        # Evitar IDs vazios
        if not sanitized:
            sanitized = "figma_component"

        return sanitized

    def generate_responsive_css(self) -> str:
        """
        Gera CSS responsivo baseado nos componentes.
        
        Returns:
            CSS responsivo
        """
        responsive_css = []
        
        # Breakpoints
        breakpoints = {
            'tablet': '768px',
            'mobile': '480px'
        }
        
        for device, max_width in breakpoints.items():
            responsive_css.append(f"/* {device.title()} Styles */")
            responsive_css.append(f"@media (max-width: {max_width}) {{")
            
            if device == 'tablet':
                responsive_css.extend([
                    "  .figma-component {",
                    "    position: relative !important;",
                    "    left: auto !important;",
                    "    top: auto !important;",
                    "  }",
                    "",
                    "  .figma-container {",
                    "    flex-direction: column !important;",
                    "    width: 100% !important;",
                    "  }",
                    "",
                    "  .figma-text {",
                    "    font-size: 14px !important;",
                    "  }"
                ])
            
            elif device == 'mobile':
                responsive_css.extend([
                    "  .figma-text {",
                    "    font-size: 12px !important;",
                    "    line-height: 1.4 !important;",
                    "  }",
                    "",
                    "  .figma-button {",
                    "    padding: 12px 16px !important;",
                    "    font-size: 14px !important;",
                    "    width: 100% !important;",
                    "  }",
                    "",
                    "  .figma-container {",
                    "    padding: 8px !important;",
                    "  }"
                ])
            
            responsive_css.append("}")
            responsive_css.append("")
        
        return "\n".join(responsive_css)
    
    def generate_animations(self) -> str:
        """
        Gera animações CSS para interações.
        
        Returns:
            CSS com animações
        """
        animations_css = """/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Interactive States */
.figma-button {
  transition: all 0.2s ease;
}

.figma-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.figma-button:active {
  transform: translateY(0);
}

.figma-input {
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.figma-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.figma-component {
  animation: fadeIn 0.3s ease;
}"""
        
        return animations_css
    
    def generate_complete_css(self) -> str:
        """
        Gera CSS completo com todas as funcionalidades.
        
        Returns:
            CSS completo
        """
        css_parts = []
        
        # Reset e base
        css_parts.append("""/* CSS Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
  color: #333;
  background-color: #fff;
}

/* Base Classes */
.figma-component {
  position: relative;
}""")
        
        # Variáveis CSS
        css_parts.append(self.generate_css_variables())
        
        # Classes utilitárias
        css_parts.append(self.generate_utility_classes())
        
        # CSS dos componentes
        css_parts.append("/* Component Styles */")
        css_parts.append(self.generate_component_css())
        
        # Animações
        css_parts.append(self.generate_animations())
        
        # CSS responsivo
        css_parts.append(self.generate_responsive_css())
        
        return "\n\n".join(css_parts)
    
    def optimize_css(self, css_content: str) -> str:
        """
        Otimiza o CSS removendo duplicatas e organizando.
        
        Args:
            css_content: CSS para otimizar
            
        Returns:
            CSS otimizado
        """
        # Remover comentários vazios
        css_content = re.sub(r'/\*\s*\*/', '', css_content)
        
        # Remover espaços extras
        css_content = re.sub(r'\n\s*\n\s*\n', '\n\n', css_content)
        
        # Remover espaços no final das linhas
        css_content = re.sub(r' +$', '', css_content, flags=re.MULTILINE)
        
        return css_content.strip()
    
    def get_css_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do CSS gerado.
        
        Returns:
            Dict com estatísticas
        """
        css_content = self.generate_complete_css()
        
        stats = {
            'total_lines': len(css_content.split('\n')),
            'total_characters': len(css_content),
            'total_rules': len(re.findall(r'{[^}]*}', css_content)),
            'design_tokens': len(self.design_tokens) if self.design_tokens else 0,
            'css_variables': len(self.css_variables),
            'components_with_styles': len([
                comp for comp in self.components.values() 
                if comp.get('css_properties')
            ])
        }
        
        return stats
