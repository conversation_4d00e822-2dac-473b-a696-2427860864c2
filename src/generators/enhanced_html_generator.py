"""
Gerador de HTML aprimorado usando o novo sistema de conversão.

Este módulo implementa geração de HTML de alta qualidade usando as estruturas
simplificadas e design tokens do novo sistema de conversão.
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from jinja2 import Environment, FileSystemLoader, Template

from converters.simplified_node import SimplifiedDesign, SimplifiedNode, GlobalVars
from converters.style_converter import (
    convert_fills_to_css, convert_strokes_to_css, convert_effects_to_css
)
from converters.utils import sanitize_css_class_name

logger = logging.getLogger(__name__)


class EnhancedHTMLGenerator:
    """
    Gerador de HTML aprimorado usando design tokens.
    
    Esta classe gera HTML e CSS de alta qualidade usando as estruturas
    simplificadas do novo sistema de conversão.
    """
    
    def __init__(self, simplified_design: SimplifiedDesign):
        """
        Inicializa o gerador.
        
        Args:
            simplified_design: Design simplificado processado
        """
        self.design = simplified_design
        self.css_rules = {}
        self.design_tokens = {}
        
        # Configurar Jinja2
        template_dir = Path(__file__).parent / 'templates'
        self.jinja_env = Environment(
            loader=FileSystemLoader(template_dir),
            trim_blocks=True,
            lstrip_blocks=True
        )
    
    def generate_complete_html(self, title: Optional[str] = None) -> str:
        """
        Gera HTML completo com CSS incorporado.
        
        Args:
            title: Título da página (opcional)
            
        Returns:
            HTML completo
        """
        logger.info("Gerando HTML completo...")
        
        # Gerar design tokens CSS
        self._generate_design_tokens()
        
        # Gerar estrutura HTML
        html_content = self._generate_html_structure()
        
        # Gerar CSS
        css_content = self._generate_css()
        
        # Template HTML completo
        html_template = """<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
{{ css_content }}
    </style>
</head>
<body>
{{ html_content }}
</body>
</html>"""
        
        template = Template(html_template)
        
        full_html = template.render(
            title=title or self.design.name or 'Figma Design',
            css_content=css_content,
            html_content=html_content
        )
        
        logger.info("HTML completo gerado com sucesso")
        return full_html
    
    def _generate_design_tokens(self) -> None:
        """Gera design tokens CSS a partir das variáveis globais."""
        logger.info("Gerando design tokens...")
        
        for var_id, value in self.design.global_vars.styles.items():
            css_var_name = f"--{var_id.replace('_', '-')}"
            
            if var_id.startswith('fill_'):
                # Processar fills
                if isinstance(value, list) and value:
                    css_values = convert_fills_to_css(value)
                    if css_values:
                        self.design_tokens[css_var_name] = css_values[0]
            
            elif var_id.startswith('style_'):
                # Processar text styles
                if isinstance(value, dict):
                    self._process_text_style_token(css_var_name, value)
            
            elif var_id.startswith('stroke_'):
                # Processar strokes
                stroke_css = convert_strokes_to_css(value)
                if stroke_css:
                    for prop, val in stroke_css.items():
                        token_name = f"{css_var_name}-{prop.replace('border-', '')}"
                        self.design_tokens[token_name] = val
            
            elif var_id.startswith('effect_'):
                # Processar effects
                effects_css = convert_effects_to_css(value)
                if effects_css:
                    for prop, val in effects_css.items():
                        token_name = f"{css_var_name}-{prop}"
                        self.design_tokens[token_name] = val
    
    def _process_text_style_token(self, css_var_name: str, style: Dict[str, Any]) -> None:
        """Processa um token de estilo de texto."""
        for prop, value in style.items():
            if value is not None:
                css_prop = prop.replace('_', '-')
                token_name = f"{css_var_name}-{css_prop}"
                self.design_tokens[token_name] = str(value)
    
    def _generate_html_structure(self) -> str:
        """Gera a estrutura HTML dos nodes."""
        html_parts = []
        
        for node in self.design.nodes:
            node_html = self._generate_node_html(node)
            if node_html:
                html_parts.append(node_html)
        
        return '\n'.join(html_parts)
    
    def _generate_node_html(self, node: SimplifiedNode, depth: int = 0) -> str:
        """
        Gera HTML para um node específico.
        
        Args:
            node: SimplifiedNode a processar
            depth: Profundidade de aninhamento
            
        Returns:
            HTML do node
        """
        indent = "  " * depth
        
        # Determinar tag HTML
        html_tag = self._determine_html_tag(node)
        
        # Gerar classe CSS
        css_class = self._generate_css_class(node)
        
        # Atributos
        attributes = []
        if css_class:
            attributes.append(f'class="{css_class}"')
        
        # ID único
        attributes.append(f'id="figma-{node.id}"')
        
        # Gerar CSS para este node
        self._generate_node_css(node, css_class)
        
        # Conteúdo
        content_parts = []
        
        # Texto
        if node.text:
            content_parts.append(node.text)
        
        # Children
        if node.children:
            for child in node.children:
                child_html = self._generate_node_html(child, depth + 1)
                if child_html:
                    content_parts.append(child_html)
        
        # Montar HTML
        attrs_str = ' '.join(attributes) if attributes else ''
        
        if content_parts:
            content = '\n'.join(content_parts)
            if node.children:
                # Adicionar indentação para children
                indented_content = '\n'.join(
                    f"{indent}  {line}" if line.strip() else line
                    for line in content.split('\n')
                )
                return f"{indent}<{html_tag} {attrs_str}>\n{indented_content}\n{indent}</{html_tag}>"
            else:
                return f"{indent}<{html_tag} {attrs_str}>{content}</{html_tag}>"
        else:
            return f"{indent}<{html_tag} {attrs_str}></{html_tag}>"
    
    def _determine_html_tag(self, node: SimplifiedNode) -> str:
        """Determina a tag HTML apropriada para um node."""
        node_type = node.type
        node_name = node.name.lower()
        
        # Mapeamento baseado no tipo
        if node_type == 'TEXT':
            return 'span'
        elif node_type == 'IMAGE-SVG':
            return 'svg'
        elif 'button' in node_name or 'btn' in node_name:
            return 'button'
        elif any(keyword in node_name for keyword in ['input', 'field']):
            return 'input'
        elif any(keyword in node_name for keyword in ['header', 'título']):
            return 'h2'
        elif node_type == 'FRAME':
            # Determinar se é container semântico
            if any(keyword in node_name for keyword in ['nav', 'navigation']):
                return 'nav'
            elif any(keyword in node_name for keyword in ['header', 'cabeçalho']):
                return 'header'
            elif any(keyword in node_name for keyword in ['footer', 'rodapé']):
                return 'footer'
            elif any(keyword in node_name for keyword in ['main', 'content', 'conteúdo']):
                return 'main'
            elif any(keyword in node_name for keyword in ['section', 'seção']):
                return 'section'
            else:
                return 'div'
        else:
            return 'div'
    
    def _generate_css_class(self, node: SimplifiedNode) -> str:
        """Gera nome de classe CSS para um node."""
        base_name = sanitize_css_class_name(node.name)
        
        # Adicionar prefixo baseado no tipo
        type_prefix = {
            'TEXT': 'text',
            'FRAME': 'container',
            'RECTANGLE': 'shape',
            'IMAGE-SVG': 'icon'
        }.get(node.type, 'element')
        
        return f"{type_prefix}-{base_name}"
    
    def _generate_node_css(self, node: SimplifiedNode, css_class: str) -> None:
        """Gera regras CSS para um node."""
        css_props = {}
        
        # Layout
        if node.layout:
            layout_data = self.design.global_vars.styles.get(node.layout)
            if layout_data:
                css_props.update(self._convert_layout_to_css(layout_data))
        
        # Fills (background)
        if node.fills and node.type != 'TEXT':
            fills_data = self.design.global_vars.styles.get(node.fills)
            if fills_data:
                css_values = convert_fills_to_css(fills_data)
                if css_values:
                    css_props['background-color'] = css_values[0]
        
        # Text color (para elementos de texto)
        if node.fills and node.type == 'TEXT':
            fills_data = self.design.global_vars.styles.get(node.fills)
            if fills_data:
                css_values = convert_fills_to_css(fills_data)
                if css_values:
                    css_props['color'] = css_values[0]
        
        # Text style
        if node.text_style:
            style_data = self.design.global_vars.styles.get(node.text_style)
            if style_data:
                css_props.update(self._convert_text_style_to_css(style_data))
        
        # Strokes
        if node.strokes:
            stroke_data = self.design.global_vars.styles.get(node.strokes)
            if stroke_data:
                css_props.update(convert_strokes_to_css(stroke_data))
        
        # Effects
        if node.effects:
            effects_data = self.design.global_vars.styles.get(node.effects)
            if effects_data:
                css_props.update(convert_effects_to_css(effects_data))
        
        # Opacity
        if node.opacity is not None:
            css_props['opacity'] = str(node.opacity)
        
        # Border radius
        if node.border_radius:
            css_props['border-radius'] = node.border_radius
        
        # Bounding box (para posicionamento absoluto)
        if node.bounding_box and not self._is_in_flex_layout(node):
            bbox = node.bounding_box
            css_props.update({
                'position': 'absolute',
                'left': f"{bbox.x}px",
                'top': f"{bbox.y}px",
                'width': f"{bbox.width}px",
                'height': f"{bbox.height}px"
            })
        
        # Armazenar regras CSS
        if css_props:
            self.css_rules[f".{css_class}"] = css_props
    
    def _convert_layout_to_css(self, layout_data: Any) -> Dict[str, str]:
        """Converte dados de layout para CSS."""
        css_props = {}
        
        if hasattr(layout_data, 'mode'):
            if layout_data.mode in ['row', 'column']:
                css_props['display'] = 'flex'
                css_props['flex-direction'] = layout_data.mode
                
                if layout_data.justify_content:
                    css_props['justify-content'] = layout_data.justify_content
                
                if layout_data.align_items:
                    css_props['align-items'] = layout_data.align_items
                
                if layout_data.gap:
                    css_props['gap'] = layout_data.gap
                
                if layout_data.padding:
                    css_props['padding'] = layout_data.padding
        
        return css_props
    
    def _convert_text_style_to_css(self, style_data: Dict[str, Any]) -> Dict[str, str]:
        """Converte estilo de texto para CSS."""
        css_props = {}
        
        prop_map = {
            'font_family': 'font-family',
            'font_weight': 'font-weight',
            'font_size': 'font-size',
            'line_height': 'line-height',
            'letter_spacing': 'letter-spacing',
            'text_align_horizontal': 'text-align',
            'text_case': 'text-transform'
        }
        
        for style_prop, css_prop in prop_map.items():
            if style_prop in style_data and style_data[style_prop] is not None:
                value = style_data[style_prop]
                if style_prop == 'font_size':
                    css_props[css_prop] = f"{value}px"
                else:
                    css_props[css_prop] = str(value)
        
        return css_props
    
    def _is_in_flex_layout(self, node: SimplifiedNode) -> bool:
        """Verifica se um node está em um layout flex."""
        # Simplificação: assumir que nodes com layout são flex
        return node.layout is not None
    
    def _generate_css(self) -> str:
        """Gera CSS completo."""
        css_parts = []
        
        # Design tokens
        if self.design_tokens:
            css_parts.append(":root {")
            for token_name, token_value in self.design_tokens.items():
                css_parts.append(f"  {token_name}: {token_value};")
            css_parts.append("}")
            css_parts.append("")
        
        # Reset básico
        css_parts.extend([
            "* {",
            "  box-sizing: border-box;",
            "  margin: 0;",
            "  padding: 0;",
            "}",
            "",
            "body {",
            "  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;",
            "}",
            ""
        ])
        
        # Regras dos componentes
        for selector, props in self.css_rules.items():
            css_parts.append(f"{selector} {{")
            for prop, value in props.items():
                css_parts.append(f"  {prop}: {value};")
            css_parts.append("}")
            css_parts.append("")
        
        return '\n'.join(css_parts)
