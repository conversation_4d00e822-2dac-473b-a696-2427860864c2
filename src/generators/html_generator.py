"""
Gerador de HTML a partir de componentes extraídos do Figma.

Este módulo é responsável por:
- Converter componentes extraídos em HTML estruturado
- Gerar CSS correspondente
- Criar arquivos HTML completos
- Otimizar estrutura para frameworks
"""

import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
from jinja2 import Template, Environment, FileSystemLoader

logger = logging.getLogger(__name__)


class HTMLGenerator:
    """Gerador de HTML a partir de componentes do Figma."""
    
    def __init__(self, components_data: Dict[str, Any]):
        """
        Inicializa o gerador.
        
        Args:
            components_data: Dados dos componentes processados
        """
        self.components_data = components_data
        self.components = components_data.get('components', {})
        self.css_rules = {}
        
        # Configurar Jinja2
        self.jinja_env = Environment(
            loader=FileSystemLoader(Path(__file__).parent / 'templates'),
            trim_blocks=True,
            lstrip_blocks=True
        )
    
    def generate_html(self, root_component_id: Optional[str] = None) -> str:
        """
        Gera HTML completo a partir dos componentes.
        
        Args:
            root_component_id: ID do componente raiz (se None, usa o primeiro encontrado)
            
        Returns:
            HTML gerado
        """
        logger.info("Iniciando geração de HTML...")
        
        # Encontrar componente raiz
        if not root_component_id:
            root_component_id = self._find_root_component()
        
        if not root_component_id:
            raise ValueError("Nenhum componente raiz encontrado")
        
        # Gerar estrutura HTML
        html_content = self._generate_component_html(root_component_id)
        
        # Gerar CSS
        css_content = self.generate_css()
        
        # Template HTML completo
        html_template = """<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
{{ css_content }}
    </style>
</head>
<body>
{{ html_content }}
</body>
</html>"""
        
        template = Template(html_template)
        
        full_html = template.render(
            title=self.components_data.get('project_summary', {}).get('project_name', 'Figma Design'),
            css_content=css_content,
            html_content=html_content
        )
        
        logger.info("HTML gerado com sucesso")
        return full_html
    
    def _find_root_component(self) -> Optional[str]:
        """
        Encontra o componente raiz (sem pai).
        
        Returns:
            ID do componente raiz ou None
        """
        for comp_id, comp_data in self.components.items():
            if not comp_data.get('parent_id'):
                return comp_id
        return None
    
    def _generate_component_html(self, component_id: str, indent_level: int = 0) -> str:
        """
        Gera HTML para um componente e seus filhos recursivamente.
        
        Args:
            component_id: ID do componente
            indent_level: Nível de indentação
            
        Returns:
            HTML do componente
        """
        if component_id not in self.components:
            return ""
        
        comp_data = self.components[component_id]
        indent = "  " * indent_level
        
        # Determinar tag HTML
        tag = comp_data.get('html_tag', 'div')
        
        # Gerar atributos
        attributes = self._generate_attributes(comp_data)
        
        # Conteúdo do elemento
        content = comp_data.get('content', '').strip()
        
        # Gerar HTML dos filhos
        children_html = ""
        for child_id in comp_data.get('children_ids', []):
            child_html = self._generate_component_html(child_id, indent_level + 1)
            if child_html:
                children_html += f"\n{child_html}"
        
        # Montar elemento HTML
        if content and not children_html:
            # Elemento com conteúdo de texto
            html = f"{indent}<{tag}{attributes}>{content}</{tag}>"
        elif children_html:
            # Elemento com filhos
            html = f"{indent}<{tag}{attributes}>{children_html}\n{indent}</{tag}>"
        else:
            # Elemento vazio
            if tag in ['img', 'input', 'br', 'hr']:
                html = f"{indent}<{tag}{attributes}>"
            else:
                html = f"{indent}<{tag}{attributes}></{tag}>"
        
        return html
    
    def _generate_attributes(self, comp_data: Dict[str, Any]) -> str:
        """
        Gera atributos HTML para um componente.

        Args:
            comp_data: Dados do componente

        Returns:
            String com atributos HTML
        """
        attributes = []

        # ID do componente - sanitizado para CSS válido
        original_id = comp_data.get('id', '')
        sanitized_id = self._sanitize_css_id(original_id)
        attributes.append(f'id="{sanitized_id}"')

        # Classes CSS
        css_classes = ['figma-component', f"figma-{comp_data.get('type', 'unknown')}"]

        # Adicionar classe baseada no nome do componente (sanitizada)
        comp_name = comp_data.get('name', '')
        if comp_name:
            sanitized_name = self._sanitize_css_class(comp_name)
            css_classes.append(f"figma-name-{sanitized_name}")

        css_classes.extend(comp_data.get('css_classes', []))

        if css_classes:
            attributes.append(f'class="{" ".join(css_classes)}"')

        # Atributos específicos por tipo
        if comp_data.get('html_tag') == 'button':
            attributes.append('type="button"')

        if comp_data.get('html_tag') == 'input':
            attributes.append('type="text"')
            if comp_data.get('content'):
                attributes.append(f'placeholder="{comp_data["content"]}"')

        # Data attributes para debugging e integração com design system
        attributes.append(f'data-figma-id="{original_id}"')
        attributes.append(f'data-figma-name="{comp_name}"')
        attributes.append(f'data-figma-type="{comp_data.get("original_type", "")}"')

        # Adicionar propriedades do design system se disponíveis
        if 'componentId' in comp_data.get('properties', {}):
            attributes.append(f'data-component-id="{comp_data["properties"]["componentId"]}"')

        return " " + " ".join(attributes) if attributes else ""

    def _sanitize_css_id(self, css_id: str) -> str:
        """
        Sanitiza um ID para ser válido em CSS.

        Args:
            css_id: ID original

        Returns:
            ID sanitizado
        """
        import re

        # Substituir caracteres inválidos por underscores
        sanitized = re.sub(r'[^a-zA-Z0-9_-]', '_', css_id)

        # Garantir que comece com letra ou underscore
        if sanitized and not re.match(r'^[a-zA-Z_]', sanitized):
            sanitized = f"figma_{sanitized}"

        # Evitar IDs vazios
        if not sanitized:
            sanitized = "figma_component"

        return sanitized

    def _sanitize_css_class(self, class_name: str) -> str:
        """
        Sanitiza um nome de classe para ser válido em CSS.

        Args:
            class_name: Nome original

        Returns:
            Nome de classe sanitizado
        """
        import re

        # Converter para lowercase e substituir espaços/caracteres especiais
        sanitized = re.sub(r'[^a-zA-Z0-9_-]', '-', class_name.lower())

        # Remover hífens/underscores consecutivos
        sanitized = re.sub(r'[-_]+', '-', sanitized)

        # Remover hífens do início e fim
        sanitized = sanitized.strip('-_')

        # Evitar nomes vazios
        if not sanitized:
            sanitized = "component"

        return sanitized
    
    def generate_css(self) -> str:
        """
        Gera CSS para todos os componentes.
        
        Returns:
            CSS gerado
        """
        logger.info("Gerando CSS...")
        
        css_rules = []
        
        # CSS reset básico
        css_rules.append("""/* Reset básico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
}

/* Classes base do Figma */
.figma-component {
    position: relative;
}""")
        
        # CSS específico para cada componente
        for comp_id, comp_data in self.components.items():
            # Usar ID sanitizado
            sanitized_id = self._sanitize_css_id(comp_id)
            css_selector = f"#{sanitized_id}"
            css_properties = comp_data.get('css_properties', {})

            if css_properties:
                css_rule = f"\n{css_selector} {{\n"
                for prop, value in css_properties.items():
                    css_rule += f"    {prop}: {value};\n"
                css_rule += "}"
                css_rules.append(css_rule)
        
        # CSS para tipos de componentes
        css_rules.append("""
/* Estilos por tipo de componente */
.figma-text {
    display: inline-block;
}

.figma-button {
    cursor: pointer;
    border: none;
    outline: none;
    transition: all 0.2s ease;
}

.figma-button:hover {
    opacity: 0.8;
}

.figma-input {
    border: 1px solid #ccc;
    padding: 8px 12px;
    border-radius: 4px;
}

.figma-container {
    display: block;
}

.figma-image {
    max-width: 100%;
    height: auto;
}""")
        
        css_content = "\n".join(css_rules)
        logger.info(f"CSS gerado: {len(css_rules)} regras")
        
        return css_content
    
    def generate_component_tree(self) -> Dict[str, Any]:
        """
        Gera uma árvore hierárquica dos componentes.
        
        Returns:
            Árvore de componentes
        """
        tree = {}
        
        # Encontrar componentes raiz
        root_components = []
        for comp_id, comp_data in self.components.items():
            if not comp_data.get('parent_id'):
                root_components.append(comp_id)
        
        # Construir árvore para cada raiz
        for root_id in root_components:
            tree[root_id] = self._build_component_tree(root_id)
        
        return tree
    
    def _build_component_tree(self, component_id: str) -> Dict[str, Any]:
        """
        Constrói árvore de um componente recursivamente.
        
        Args:
            component_id: ID do componente
            
        Returns:
            Árvore do componente
        """
        if component_id not in self.components:
            return {}
        
        comp_data = self.components[component_id]
        
        tree_node = {
            'id': component_id,
            'name': comp_data.get('name', ''),
            'type': comp_data.get('type', ''),
            'html_tag': comp_data.get('html_tag', 'div'),
            'content': comp_data.get('content', ''),
            'children': []
        }
        
        # Adicionar filhos
        for child_id in comp_data.get('children_ids', []):
            child_tree = self._build_component_tree(child_id)
            if child_tree:
                tree_node['children'].append(child_tree)
        
        return tree_node
    
    def generate_responsive_css(self) -> str:
        """
        Gera CSS responsivo básico.
        
        Returns:
            CSS responsivo
        """
        responsive_css = """
/* CSS Responsivo */
@media (max-width: 768px) {
    .figma-component {
        position: relative !important;
        left: auto !important;
        top: auto !important;
        width: 100% !important;
        max-width: 100% !important;
    }
    
    .figma-container {
        flex-direction: column !important;
        align-items: stretch !important;
    }
    
    .figma-text {
        font-size: 14px !important;
        line-height: 1.4 !important;
    }
}

@media (max-width: 480px) {
    .figma-text {
        font-size: 12px !important;
    }
    
    .figma-button {
        padding: 12px 16px !important;
        font-size: 14px !important;
    }
}"""
        
        return responsive_css
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas da geração.
        
        Returns:
            Dict com estatísticas
        """
        stats = {
            'total_components': len(self.components),
            'components_with_css': 0,
            'components_with_content': 0,
            'html_tags_used': {},
            'css_properties_count': 0
        }
        
        for comp_data in self.components.values():
            if comp_data.get('css_properties'):
                stats['components_with_css'] += 1
                stats['css_properties_count'] += len(comp_data['css_properties'])
            
            if comp_data.get('content'):
                stats['components_with_content'] += 1
            
            html_tag = comp_data.get('html_tag', 'div')
            stats['html_tags_used'][html_tag] = stats['html_tags_used'].get(html_tag, 0) + 1
        
        return stats
