"""
Design System Loader para integração com documentação do Storybook.

Este módulo fornece classes para carregar, indexar e consultar
documentação de Design Systems para geração de componentes.
"""

import requests
import sqlite3
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from bs4 import BeautifulSoup
import pandas as pd

logger = logging.getLogger(__name__)


@dataclass
class ComponentDoc:
    """Documentação de um componente do Design System."""
    id: str
    name: str
    category: str
    description: str
    props_schema: Dict[str, Any]
    examples: List[Dict[str, Any]]
    code_samples: Dict[str, str]  # framework -> code
    tags: List[str]


class DesignSystemLoader:
    """
    Carregador de documentação do Design System.
    
    Suporta múltiplas fontes: Storybook, API, arquivos locais.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Inicializa o loader com configuração.
        
        Args:
            config: Configuração do Design System
        """
        self.config = config
        self.db_path = Path("data/design_system/components.db")
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        self._init_database()
        logger.info("Design System Loader inicializado")
    
    def _init_database(self) -> None:
        """Inicializa o banco de dados SQLite."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS components (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    category TEXT,
                    description TEXT,
                    props_schema TEXT,
                    examples TEXT,
                    code_samples TEXT,
                    tags TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Índices para performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_name ON components(name)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_category ON components(category)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_tags ON components(tags)")
    
    def load_components(self) -> int:
        """
        Carrega componentes do Design System baseado na configuração.
        
        Returns:
            Número de componentes carregados
        """
        ds_type = self.config.get('type', 'storybook')
        
        if ds_type == 'storybook':
            return self._load_from_storybook()
        elif ds_type == 'api':
            return self._load_from_api()
        elif ds_type == 'local':
            return self._load_from_local()
        else:
            raise ValueError(f"Tipo de Design System não suportado: {ds_type}")
    
    def _load_from_storybook(self) -> int:
        """Carrega componentes de um Storybook."""
        storybook_url = self.config.get('source')
        if not storybook_url:
            raise ValueError("URL do Storybook não configurada")
        
        logger.info(f"Carregando componentes do Storybook: {storybook_url}")
        
        # Implementação básica - pode ser expandida
        components = []
        
        try:
            # Buscar página principal do Storybook
            response = requests.get(f"{storybook_url}/iframe.html")
            response.raise_for_status()
            
            # Parse básico - em produção, usar API do Storybook
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Exemplo de componentes mock para demonstração
            mock_components = self._get_mock_components()
            components.extend(mock_components)
            
        except Exception as e:
            logger.error(f"Erro ao carregar do Storybook: {e}")
            # Fallback para componentes mock
            components = self._get_mock_components()
        
        # Armazenar no banco
        self._store_components(components)
        
        logger.info(f"Carregados {len(components)} componentes do Storybook")
        return len(components)
    
    def _load_from_api(self) -> int:
        """Carrega componentes de uma API."""
        api_url = self.config.get('components_endpoint')
        if not api_url:
            raise ValueError("Endpoint da API não configurado")
        
        logger.info(f"Carregando componentes da API: {api_url}")
        
        try:
            response = requests.get(api_url)
            response.raise_for_status()
            
            api_data = response.json()
            components = self._parse_api_response(api_data)
            
            self._store_components(components)
            
            logger.info(f"Carregados {len(components)} componentes da API")
            return len(components)
            
        except Exception as e:
            logger.error(f"Erro ao carregar da API: {e}")
            raise
    
    def _load_from_local(self) -> int:
        """Carrega componentes de arquivos locais."""
        local_path = Path(self.config.get('local_path', 'design_system_docs/'))
        
        if not local_path.exists():
            raise FileNotFoundError(f"Diretório local não encontrado: {local_path}")
        
        logger.info(f"Carregando componentes locais de: {local_path}")
        
        components = []
        
        # Buscar arquivos JSON de componentes
        for json_file in local_path.glob("**/*.json"):
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    component_data = json.load(f)
                    component = self._parse_local_component(component_data)
                    components.append(component)
            except Exception as e:
                logger.warning(f"Erro ao processar {json_file}: {e}")
        
        self._store_components(components)
        
        logger.info(f"Carregados {len(components)} componentes locais")
        return len(components)
    
    def _get_mock_components(self) -> List[ComponentDoc]:
        """Retorna componentes mock para demonstração."""
        return [
            ComponentDoc(
                id="button",
                name="Button",
                category="Form",
                description="Componente de botão reutilizável",
                props_schema={
                    "variant": {"type": "string", "enum": ["primary", "secondary", "outline"]},
                    "size": {"type": "string", "enum": ["small", "medium", "large"]},
                    "disabled": {"type": "boolean"},
                    "children": {"type": "string"}
                },
                examples=[
                    {"name": "Primary", "props": {"variant": "primary", "children": "Click me"}},
                    {"name": "Secondary", "props": {"variant": "secondary", "children": "Cancel"}}
                ],
                code_samples={
                    "react": "<Button variant='primary' size='medium'>Click me</Button>",
                    "angular": "<app-button variant='primary' size='medium'>Click me</app-button>"
                },
                tags=["button", "form", "interactive"]
            ),
            ComponentDoc(
                id="input",
                name="TextField",
                category="Form",
                description="Campo de entrada de texto",
                props_schema={
                    "label": {"type": "string"},
                    "placeholder": {"type": "string"},
                    "type": {"type": "string", "enum": ["text", "email", "password"]},
                    "required": {"type": "boolean"},
                    "disabled": {"type": "boolean"}
                },
                examples=[
                    {"name": "Basic", "props": {"label": "Name", "placeholder": "Enter your name"}},
                    {"name": "Email", "props": {"label": "Email", "type": "email"}}
                ],
                code_samples={
                    "react": "<TextField label='Name' placeholder='Enter your name' />",
                    "angular": "<app-text-field label='Name' placeholder='Enter your name'></app-text-field>"
                },
                tags=["input", "form", "text"]
            ),
            ComponentDoc(
                id="card",
                name="Card",
                category="Layout",
                description="Container para agrupar conteúdo relacionado",
                props_schema={
                    "title": {"type": "string"},
                    "subtitle": {"type": "string"},
                    "elevation": {"type": "number", "min": 0, "max": 5},
                    "children": {"type": "node"}
                },
                examples=[
                    {"name": "Basic", "props": {"title": "Card Title"}},
                    {"name": "With Subtitle", "props": {"title": "Title", "subtitle": "Subtitle"}}
                ],
                code_samples={
                    "react": "<Card title='Card Title'><p>Content</p></Card>",
                    "angular": "<app-card title='Card Title'><p>Content</p></app-card>"
                },
                tags=["card", "layout", "container"]
            )
        ]
    
    def _store_components(self, components: List[ComponentDoc]) -> None:
        """Armazena componentes no banco de dados."""
        with sqlite3.connect(self.db_path) as conn:
            for component in components:
                conn.execute("""
                    INSERT OR REPLACE INTO components 
                    (id, name, category, description, props_schema, examples, code_samples, tags)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    component.id,
                    component.name,
                    component.category,
                    component.description,
                    json.dumps(component.props_schema),
                    json.dumps(component.examples),
                    json.dumps(component.code_samples),
                    json.dumps(component.tags)
                ))
    
    def search_components(self, query: str, category: Optional[str] = None) -> List[ComponentDoc]:
        """
        Busca componentes por nome ou tags.
        
        Args:
            query: Termo de busca
            category: Categoria opcional para filtrar
            
        Returns:
            Lista de componentes encontrados
        """
        with sqlite3.connect(self.db_path) as conn:
            sql = """
                SELECT * FROM components 
                WHERE (name LIKE ? OR tags LIKE ?)
            """
            params = [f"%{query}%", f"%{query}%"]
            
            if category:
                sql += " AND category = ?"
                params.append(category)
            
            sql += " ORDER BY name"
            
            cursor = conn.execute(sql, params)
            rows = cursor.fetchall()
            
            return [self._row_to_component(row) for row in rows]
    
    def get_component(self, component_id: str) -> Optional[ComponentDoc]:
        """
        Obtém um componente específico por ID.
        
        Args:
            component_id: ID do componente
            
        Returns:
            ComponentDoc ou None se não encontrado
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute("SELECT * FROM components WHERE id = ?", (component_id,))
            row = cursor.fetchone()
            
            return self._row_to_component(row) if row else None
    
    def _row_to_component(self, row: tuple) -> ComponentDoc:
        """Converte linha do banco para ComponentDoc."""
        return ComponentDoc(
            id=row[0],
            name=row[1],
            category=row[2],
            description=row[3],
            props_schema=json.loads(row[4]) if row[4] else {},
            examples=json.loads(row[5]) if row[5] else [],
            code_samples=json.loads(row[6]) if row[6] else {},
            tags=json.loads(row[7]) if row[7] else []
        )
    
    def get_context_for_generation(self) -> Dict[str, Any]:
        """
        Obtém contexto completo do Design System para geração de componentes.
        
        Returns:
            Dicionário com contexto do Design System
        """
        with sqlite3.connect(self.db_path) as conn:
            df = pd.read_sql("SELECT * FROM components", conn)
            
            if df.empty:
                return {"components": [], "categories": [], "total": 0}
            
            # Processar dados para contexto
            components = []
            for _, row in df.iterrows():
                components.append({
                    "id": row["id"],
                    "name": row["name"],
                    "category": row["category"],
                    "description": row["description"],
                    "props_schema": json.loads(row["props_schema"]) if row["props_schema"] else {},
                    "examples": json.loads(row["examples"]) if row["examples"] else [],
                    "code_samples": json.loads(row["code_samples"]) if row["code_samples"] else {},
                    "tags": json.loads(row["tags"]) if row["tags"] else []
                })
            
            categories = df["category"].unique().tolist()
            
            return {
                "components": components,
                "categories": categories,
                "total": len(components),
                "by_category": df.groupby("category").size().to_dict()
            }
