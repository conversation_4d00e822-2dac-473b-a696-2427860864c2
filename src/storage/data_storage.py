"""
Sistema de armazenamento de dados processados.

Este módulo é responsável por:
- Salvar dados em diferentes formatos (JSON, Parquet, CSV)
- Gerenciar estruturas de dados
- Facilitar recuperação de dados processados
- Otimizar armazenamento para análise
"""

import json
import logging
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import asdict

logger = logging.getLogger(__name__)


class DataStorage:
    """Sistema de armazenamento de dados processados."""
    
    def __init__(self, base_path: str = "data"):
        """
        Inicializa o sistema de armazenamento.
        
        Args:
            base_path: Caminho base para armazenamento
        """
        self.base_path = Path(base_path)
        self.processed_path = self.base_path / "processed"
        self.output_path = self.base_path / "output"
        
        # Criar diretórios se não existirem
        self.processed_path.mkdir(parents=True, exist_ok=True)
        self.output_path.mkdir(parents=True, exist_ok=True)
    
    def save_components_json(self, components_data: Dict[str, Any], filename: str = "components.json") -> Path:
        """
        Salva dados de componentes em formato JSON.
        
        Args:
            components_data: Dados dos componentes
            filename: Nome do arquivo
            
        Returns:
            Caminho do arquivo salvo
        """
        file_path = self.processed_path / filename
        
        logger.info(f"Salvando componentes em JSON: {file_path}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(components_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Componentes salvos com sucesso: {len(components_data.get('components', {}))} componentes")
        return file_path
    
    def save_components_parquet(self, components_data: Dict[str, Any], filename: str = "components.parquet") -> Path:
        """
        Salva dados de componentes em formato Parquet para análise eficiente.
        
        Args:
            components_data: Dados dos componentes
            filename: Nome do arquivo
            
        Returns:
            Caminho do arquivo salvo
        """
        file_path = self.processed_path / filename
        
        logger.info(f"Salvando componentes em Parquet: {file_path}")
        
        # Converter dados para DataFrame
        components = components_data.get('components', {})
        
        # Preparar dados tabulares
        rows = []
        for comp_id, comp_data in components.items():
            row = {
                'id': comp_data['id'],
                'name': comp_data['name'],
                'type': comp_data['type'],
                'original_type': comp_data['original_type'],
                'html_tag': comp_data['html_tag'],
                'content': comp_data['content'],
                'parent_id': comp_data['parent_id'],
                'has_children': len(comp_data.get('children_ids', [])) > 0,
                'children_count': len(comp_data.get('children_ids', [])),
                'css_classes_count': len(comp_data.get('css_classes', [])),
                'css_properties_count': len(comp_data.get('css_properties', {})),
                'width': comp_data.get('properties', {}).get('position', {}).get('width', 0),
                'height': comp_data.get('properties', {}).get('position', {}).get('height', 0),
                'x': comp_data.get('properties', {}).get('position', {}).get('x', 0),
                'y': comp_data.get('properties', {}).get('position', {}).get('y', 0),
                'visible': comp_data.get('properties', {}).get('visible', True),
                'opacity': comp_data.get('properties', {}).get('opacity', 1.0)
            }
            rows.append(row)
        
        df = pd.DataFrame(rows)
        df.to_parquet(file_path, index=False)
        
        logger.info(f"Componentes salvos em Parquet: {len(df)} registros")
        return file_path
    
    def save_components_csv(self, components_data: Dict[str, Any], filename: str = "components.csv") -> Path:
        """
        Salva dados de componentes em formato CSV para análise em planilhas.
        
        Args:
            components_data: Dados dos componentes
            filename: Nome do arquivo
            
        Returns:
            Caminho do arquivo salvo
        """
        file_path = self.processed_path / filename
        
        logger.info(f"Salvando componentes em CSV: {file_path}")
        
        # Usar a mesma lógica do Parquet mas salvar como CSV
        components = components_data.get('components', {})
        
        rows = []
        for comp_id, comp_data in components.items():
            row = {
                'id': comp_data['id'],
                'name': comp_data['name'],
                'type': comp_data['type'],
                'original_type': comp_data['original_type'],
                'html_tag': comp_data['html_tag'],
                'content': comp_data['content'],
                'parent_id': comp_data['parent_id'],
                'css_classes': ', '.join(comp_data.get('css_classes', [])),
                'children_count': len(comp_data.get('children_ids', [])),
                'width': comp_data.get('properties', {}).get('position', {}).get('width', 0),
                'height': comp_data.get('properties', {}).get('position', {}).get('height', 0),
                'x': comp_data.get('properties', {}).get('position', {}).get('x', 0),
                'y': comp_data.get('properties', {}).get('position', {}).get('y', 0),
                'visible': comp_data.get('properties', {}).get('visible', True)
            }
            rows.append(row)
        
        df = pd.DataFrame(rows)
        df.to_csv(file_path, index=False, encoding='utf-8')
        
        logger.info(f"Componentes salvos em CSV: {len(df)} registros")
        return file_path
    
    def load_components_json(self, filename: str = "components.json") -> Dict[str, Any]:
        """
        Carrega dados de componentes de um arquivo JSON.
        
        Args:
            filename: Nome do arquivo
            
        Returns:
            Dados dos componentes
        """
        file_path = self.processed_path / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"Arquivo não encontrado: {file_path}")
        
        logger.info(f"Carregando componentes de JSON: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        logger.info(f"Componentes carregados: {len(data.get('components', {}))}")
        return data
    
    def load_components_parquet(self, filename: str = "components.parquet") -> pd.DataFrame:
        """
        Carrega dados de componentes de um arquivo Parquet.
        
        Args:
            filename: Nome do arquivo
            
        Returns:
            DataFrame com os dados
        """
        file_path = self.processed_path / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"Arquivo não encontrado: {file_path}")
        
        logger.info(f"Carregando componentes de Parquet: {file_path}")
        
        df = pd.read_parquet(file_path)
        
        logger.info(f"Componentes carregados: {len(df)} registros")
        return df
    
    def save_generated_code(self, code: str, filename: str, format_type: str = "html") -> Path:
        """
        Salva código gerado.
        
        Args:
            code: Código gerado
            filename: Nome do arquivo
            format_type: Tipo do formato (html, css, js, etc.)
            
        Returns:
            Caminho do arquivo salvo
        """
        # Criar subdiretório por formato se necessário
        format_dir = self.output_path / format_type
        format_dir.mkdir(exist_ok=True)
        
        file_path = format_dir / filename
        
        logger.info(f"Salvando código {format_type}: {file_path}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(code)
        
        logger.info(f"Código salvo com sucesso: {len(code)} caracteres")
        return file_path
    
    def get_storage_summary(self) -> Dict[str, Any]:
        """
        Retorna um resumo dos arquivos armazenados.
        
        Returns:
            Dict com informações dos arquivos
        """
        summary = {
            'processed_files': [],
            'output_files': [],
            'total_size_mb': 0
        }
        
        # Verificar arquivos processados
        for file_path in self.processed_path.rglob('*'):
            if file_path.is_file():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                summary['processed_files'].append({
                    'name': file_path.name,
                    'path': str(file_path.relative_to(self.base_path)),
                    'size_mb': round(size_mb, 2),
                    'type': file_path.suffix
                })
                summary['total_size_mb'] += size_mb
        
        # Verificar arquivos de saída
        for file_path in self.output_path.rglob('*'):
            if file_path.is_file():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                summary['output_files'].append({
                    'name': file_path.name,
                    'path': str(file_path.relative_to(self.base_path)),
                    'size_mb': round(size_mb, 2),
                    'type': file_path.suffix
                })
                summary['total_size_mb'] += size_mb
        
        summary['total_size_mb'] = round(summary['total_size_mb'], 2)
        
        return summary
    
    def analyze_components(self, filename: str = "components.json") -> Dict[str, Any]:
        """
        Analisa dados de componentes e retorna estatísticas.
        
        Args:
            filename: Nome do arquivo de componentes
            
        Returns:
            Dict com análises dos componentes
        """
        try:
            # Tentar carregar Parquet primeiro (mais eficiente)
            parquet_file = filename.replace('.json', '.parquet')
            df = self.load_components_parquet(parquet_file)
            
            analysis = {
                'total_components': len(df),
                'components_by_type': df['type'].value_counts().to_dict(),
                'components_by_html_tag': df['html_tag'].value_counts().to_dict(),
                'average_size': {
                    'width': df['width'].mean(),
                    'height': df['height'].mean()
                },
                'size_distribution': {
                    'width': {
                        'min': df['width'].min(),
                        'max': df['width'].max(),
                        'median': df['width'].median()
                    },
                    'height': {
                        'min': df['height'].min(),
                        'max': df['height'].max(),
                        'median': df['height'].median()
                    }
                },
                'hierarchy_stats': {
                    'root_components': len(df[df['parent_id'].isna()]),
                    'components_with_children': len(df[df['children_count'] > 0]),
                    'max_children': df['children_count'].max(),
                    'average_children': df['children_count'].mean()
                }
            }
            
            return analysis
            
        except FileNotFoundError:
            # Fallback para JSON
            data = self.load_components_json(filename)
            components = data.get('components', {})
            
            return {
                'total_components': len(components),
                'note': 'Análise básica - para análise completa, gere arquivo Parquet'
            }
