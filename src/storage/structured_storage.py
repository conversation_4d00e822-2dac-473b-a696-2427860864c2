"""
Sistema de armazenamento estruturado para dados descobertos do Figma.

Este módulo fornece classes para armazenar dados em Parquet e SQLite
de forma otimizada para futura criação de componentes.
"""

import sqlite3
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from src.discovery.figma_discovery import DiscoveredNode

logger = logging.getLogger(__name__)


class StructuredStorage:
    """
    Armazenamento estruturado de dados do Figma usando SQLite.
    """

    def __init__(self, project_id: str, base_dir: str = "data/output"):
        """
        Inicializa o armazenamento estruturado.

        Args:
            project_id: ID único do projeto (geralmente file_key do Figma)
            base_dir: Diretório base para armazenamento
        """
        self.project_id = project_id
        self.base_dir = Path(base_dir)
        self.project_dir = self.base_dir / project_id
        self.project_dir.mkdir(parents=True, exist_ok=True)

        # Arquivo SQLite
        self.db_path = self.project_dir / "figma_data.db"

        logger.info(f"Armazenamento estruturado inicializado para projeto: {project_id}")
    
    def store_discovery_data(
        self,
        discovered_nodes: List[DiscoveredNode],
        file_info: Dict[str, Any],
        discovery_params: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Armazena dados de descoberta em SQLite.

        Args:
            discovered_nodes: Lista de nodes descobertos
            file_info: Informações do arquivo Figma
            discovery_params: Parâmetros usados na descoberta

        Returns:
            Dicionário com caminhos dos arquivos criados
        """
        logger.info(f"Armazenando dados de descoberta para {len(discovered_nodes)} nodes")

        # Armazenar em SQLite
        self._store_sqlite(discovered_nodes, file_info, discovery_params)

        # Gerar arquivo JSON para conferência
        json_path = self._export_to_json()

        return {
            'sqlite': str(self.db_path),
            'json_export': str(json_path),
            'project_dir': str(self.project_dir)
        }
    
    def _store_sqlite(
        self,
        discovered_nodes: List[DiscoveredNode],
        file_info: Dict[str, Any],
        discovery_params: Dict[str, Any]
    ) -> None:
        """
        Armazena dados em banco SQLite.

        Args:
            discovered_nodes: Lista de nodes descobertos
            file_info: Informações do arquivo
            discovery_params: Parâmetros de descoberta
        """
        with sqlite3.connect(self.db_path) as conn:
            # Criar tabelas
            self._create_sqlite_tables(conn)

            # Inserir dados do projeto
            self._insert_project_data(conn, file_info, discovery_params)

            # Inserir nodes
            self._insert_nodes(conn, discovered_nodes)

            # Criar índices para performance
            self._create_sqlite_indexes(conn)

        logger.info(f"Dados salvos em SQLite: {self.db_path}")
    
    def _insert_nodes(self, conn: sqlite3.Connection, nodes: List[DiscoveredNode]) -> None:
        """Insere nodes no banco SQLite."""

        for node in nodes:
            conn.execute("""
                INSERT OR REPLACE INTO nodes
                (id, name, type, node_type, level, parent_id, parent_name,
                 children_count, has_layout, has_content, estimated_complexity,
                 description, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                node.id,
                node.name,
                node.type,
                node.node_type.value,
                node.level,
                node.parent_id,
                node.parent_name,
                node.children_count,
                node.has_layout,
                node.has_content,
                node.estimated_complexity,
                node.description,
                datetime.now().isoformat()
            ))
    
    def _create_sqlite_tables(self, conn: sqlite3.Connection) -> None:
        """Cria tabelas no banco SQLite."""

        # Tabela de projetos
        conn.execute("""
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT,
                version TEXT,
                last_modified TEXT,
                discovery_date TEXT,
                discovery_params TEXT
            )
        """)

        # Tabela de nodes
        conn.execute("""
            CREATE TABLE IF NOT EXISTS nodes (
                id TEXT PRIMARY KEY,
                name TEXT,
                type TEXT,
                node_type TEXT,
                level INTEGER,
                parent_id TEXT,
                parent_name TEXT,
                children_count INTEGER,
                has_layout BOOLEAN,
                has_content BOOLEAN,
                estimated_complexity TEXT,
                description TEXT,
                created_at TEXT
            )
        """)

        # View de componentes otimizada
        conn.execute("""
            CREATE VIEW IF NOT EXISTS components AS
            SELECT
                id,
                name,
                type,
                node_type,
                level,
                parent_id,
                parent_name,
                estimated_complexity,
                description
            FROM nodes
            WHERE node_type IN ('component', 'instance', 'frame')
            AND level <= 3
            ORDER BY level, estimated_complexity DESC
        """)
    
    def _insert_project_data(
        self, 
        conn: sqlite3.Connection, 
        file_info: Dict[str, Any],
        discovery_params: Dict[str, Any]
    ) -> None:
        """Insere dados do projeto no SQLite."""
        
        conn.execute("""
            INSERT OR REPLACE INTO projects 
            (id, name, version, last_modified, discovery_date, discovery_params)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            self.project_id,
            file_info.get('name', 'Unknown'),
            file_info.get('version', 'Unknown'),
            file_info.get('lastModified', ''),
            datetime.now().isoformat(),
            json.dumps(discovery_params)
        ))
    
    def _create_sqlite_indexes(self, conn: sqlite3.Connection) -> None:
        """Cria índices para otimizar consultas."""
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_nodes_type ON nodes(node_type)",
            "CREATE INDEX IF NOT EXISTS idx_nodes_level ON nodes(level)",
            "CREATE INDEX IF NOT EXISTS idx_nodes_parent ON nodes(parent_id)",
            "CREATE INDEX IF NOT EXISTS idx_nodes_complexity ON nodes(estimated_complexity)",
            "CREATE INDEX IF NOT EXISTS idx_nodes_name ON nodes(name)"
        ]
        
        for index_sql in indexes:
            conn.execute(index_sql)
    
    def load_discovery_data(self) -> Optional[List[Dict[str, Any]]]:
        """
        Carrega dados de descoberta do armazenamento.

        Returns:
            Lista de dicionários com dados carregados ou None se não encontrado
        """
        if not self.db_path.exists():
            logger.warning("Banco SQLite não encontrado")
            return None

        logger.info(f"Carregando dados do SQLite: {self.db_path}")
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM nodes")
            return [dict(row) for row in cursor.fetchall()]

    def get_components_for_generation(self) -> Optional[List[Dict[str, Any]]]:
        """
        Obtém componentes otimizados para geração de código.

        Returns:
            Lista de dicionários com componentes filtrados e organizados
        """
        if not self.db_path.exists():
            logger.warning("Banco SQLite não encontrado")
            return None

        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            query = """
                SELECT * FROM components
                WHERE estimated_complexity IN ('medium', 'high')
                OR (estimated_complexity = 'low' AND level <= 2)
                ORDER BY level, estimated_complexity DESC, name
            """
            cursor = conn.execute(query)
            return [dict(row) for row in cursor.fetchall()]

    def _export_to_json(self) -> Path:
        """
        Exporta dados do SQLite para JSON para conferência.

        Returns:
            Caminho do arquivo JSON criado
        """
        json_path = self.project_dir / "data_export.json"

        if not self.db_path.exists():
            logger.warning("Banco SQLite não existe, criando arquivo JSON vazio")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump({"error": "No SQLite database found"}, f, indent=2)
            return json_path

        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row

            # Exportar dados das tabelas principais
            export_data = {
                "export_info": {
                    "timestamp": datetime.now().isoformat(),
                    "project_id": self.project_id,
                    "database_path": str(self.db_path)
                },
                "projects": [],
                "nodes": [],
                "components": [],
                "summary": {}
            }

            # Exportar projetos
            cursor = conn.execute("SELECT * FROM projects")
            export_data["projects"] = [dict(row) for row in cursor.fetchall()]

            # Exportar nodes
            cursor = conn.execute("SELECT * FROM nodes ORDER BY level, name")
            export_data["nodes"] = [dict(row) for row in cursor.fetchall()]

            # Exportar componentes (view)
            try:
                cursor = conn.execute("SELECT * FROM components ORDER BY level, estimated_complexity DESC")
                export_data["components"] = [dict(row) for row in cursor.fetchall()]
            except Exception as e:
                logger.warning(f"Erro ao exportar componentes: {e}")
                export_data["components"] = []

            # Gerar resumo
            export_data["summary"] = {
                "total_nodes": len(export_data["nodes"]),
                "total_components": len(export_data["components"]),
                "nodes_by_type": {},
                "nodes_by_level": {},
                "components_by_complexity": {}
            }

            # Estatísticas por tipo
            for node in export_data["nodes"]:
                node_type = node.get("node_type", "unknown")
                export_data["summary"]["nodes_by_type"][node_type] = \
                    export_data["summary"]["nodes_by_type"].get(node_type, 0) + 1

                level = node.get("level", 0)
                export_data["summary"]["nodes_by_level"][f"level_{level}"] = \
                    export_data["summary"]["nodes_by_level"].get(f"level_{level}", 0) + 1

            # Estatísticas por complexidade
            for comp in export_data["components"]:
                complexity = comp.get("estimated_complexity", "unknown")
                export_data["summary"]["components_by_complexity"][complexity] = \
                    export_data["summary"]["components_by_complexity"].get(complexity, 0) + 1

        # Salvar arquivo JSON
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        logger.info(f"Dados exportados para JSON: {json_path}")
        return json_path
