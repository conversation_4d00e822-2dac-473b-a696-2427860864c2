"""
Sistema de armazenamento estruturado para dados descobertos do Figma.

Este módulo fornece classes para armazenar dados em Parquet e SQLite
de forma otimizada para futura criação de componentes.
"""

import sqlite3
import pandas as pd
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from src.discovery.figma_discovery import DiscoveredNode

logger = logging.getLogger(__name__)


class StructuredStorage:
    """
    Classe base para armazenamento estruturado de dados do Figma.
    """
    
    def __init__(self, project_id: str, base_dir: str = "data/output"):
        """
        Inicializa o armazenamento estruturado.
        
        Args:
            project_id: ID único do projeto (geralmente file_key do Figma)
            base_dir: Diretório base para armazenamento
        """
        self.project_id = project_id
        self.base_dir = Path(base_dir)
        self.project_dir = self.base_dir / project_id
        self.project_dir.mkdir(parents=True, exist_ok=True)
        
        # Diretórios específicos
        self.parquet_dir = self.project_dir / "parquet"
        self.sqlite_dir = self.project_dir / "sqlite"
        self.parquet_dir.mkdir(exist_ok=True)
        self.sqlite_dir.mkdir(exist_ok=True)
        
        logger.info(f"Armazenamento estruturado inicializado para projeto: {project_id}")
    
    def store_discovery_data(
        self, 
        discovered_nodes: List[DiscoveredNode],
        file_info: Dict[str, Any],
        discovery_params: Dict[str, Any]
    ) -> Dict[str, str]:
        """
        Armazena dados de descoberta em formatos estruturados.
        
        Args:
            discovered_nodes: Lista de nodes descobertos
            file_info: Informações do arquivo Figma
            discovery_params: Parâmetros usados na descoberta
            
        Returns:
            Dicionário com caminhos dos arquivos criados
        """
        logger.info(f"Armazenando dados de descoberta para {len(discovered_nodes)} nodes")
        
        # Converter nodes para DataFrame
        nodes_df = self._nodes_to_dataframe(discovered_nodes)
        
        # Armazenar em Parquet
        parquet_path = self._store_parquet(nodes_df, file_info, discovery_params)
        
        # Armazenar em SQLite
        sqlite_path = self._store_sqlite(nodes_df, file_info, discovery_params)
        
        return {
            'parquet': str(parquet_path),
            'sqlite': str(sqlite_path),
            'project_dir': str(self.project_dir)
        }
    
    def _nodes_to_dataframe(self, nodes: List[DiscoveredNode]) -> pd.DataFrame:
        """
        Converte lista de nodes para DataFrame pandas.
        
        Args:
            nodes: Lista de nodes descobertos
            
        Returns:
            DataFrame com dados dos nodes
        """
        data = []
        
        for node in nodes:
            data.append({
                'id': node.id,
                'name': node.name,
                'type': node.type,
                'node_type': node.node_type.value,
                'level': node.level,
                'parent_id': node.parent_id,
                'parent_name': node.parent_name,
                'children_count': node.children_count,
                'has_layout': node.has_layout,
                'has_content': node.has_content,
                'estimated_complexity': node.estimated_complexity,
                'description': node.description,
                'created_at': datetime.now().isoformat()
            })
        
        return pd.DataFrame(data)
    
    def _store_parquet(
        self, 
        nodes_df: pd.DataFrame, 
        file_info: Dict[str, Any],
        discovery_params: Dict[str, Any]
    ) -> Path:
        """
        Armazena dados em formato Parquet.
        
        Args:
            nodes_df: DataFrame com dados dos nodes
            file_info: Informações do arquivo
            discovery_params: Parâmetros de descoberta
            
        Returns:
            Caminho do arquivo Parquet criado
        """
        # Adicionar metadados
        nodes_df['file_name'] = file_info.get('name', 'Unknown')
        nodes_df['file_version'] = file_info.get('version', 'Unknown')
        nodes_df['discovery_date'] = datetime.now().isoformat()
        
        # Salvar arquivo principal
        parquet_path = self.parquet_dir / "discovered_nodes.parquet"
        nodes_df.to_parquet(parquet_path, index=False)
        
        # Salvar metadados separadamente
        metadata = {
            'file_info': file_info,
            'discovery_params': discovery_params,
            'total_nodes': len(nodes_df),
            'created_at': datetime.now().isoformat()
        }
        
        metadata_path = self.parquet_dir / "metadata.json"
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Dados salvos em Parquet: {parquet_path}")
        return parquet_path
    
    def _store_sqlite(
        self, 
        nodes_df: pd.DataFrame, 
        file_info: Dict[str, Any],
        discovery_params: Dict[str, Any]
    ) -> Path:
        """
        Armazena dados em banco SQLite.
        
        Args:
            nodes_df: DataFrame com dados dos nodes
            file_info: Informações do arquivo
            discovery_params: Parâmetros de descoberta
            
        Returns:
            Caminho do arquivo SQLite criado
        """
        sqlite_path = self.sqlite_dir / "figma_data.db"
        
        with sqlite3.connect(sqlite_path) as conn:
            # Criar tabelas
            self._create_sqlite_tables(conn)
            
            # Inserir dados do projeto
            self._insert_project_data(conn, file_info, discovery_params)
            
            # Inserir nodes
            nodes_df.to_sql('nodes', conn, if_exists='replace', index=False)
            
            # Criar índices para performance
            self._create_sqlite_indexes(conn)
        
        logger.info(f"Dados salvos em SQLite: {sqlite_path}")
        return sqlite_path
    
    def _create_sqlite_tables(self, conn: sqlite3.Connection) -> None:
        """Cria tabelas no banco SQLite."""
        
        # Tabela de projetos
        conn.execute("""
            CREATE TABLE IF NOT EXISTS projects (
                id TEXT PRIMARY KEY,
                name TEXT,
                version TEXT,
                last_modified TEXT,
                discovery_date TEXT,
                discovery_params TEXT
            )
        """)
        
        # Tabela de componentes (view otimizada)
        conn.execute("""
            CREATE VIEW IF NOT EXISTS components AS
            SELECT 
                id,
                name,
                type,
                node_type,
                level,
                parent_id,
                parent_name,
                estimated_complexity,
                description
            FROM nodes
            WHERE node_type IN ('component', 'instance', 'frame')
            AND level <= 3
            ORDER BY level, estimated_complexity DESC
        """)
    
    def _insert_project_data(
        self, 
        conn: sqlite3.Connection, 
        file_info: Dict[str, Any],
        discovery_params: Dict[str, Any]
    ) -> None:
        """Insere dados do projeto no SQLite."""
        
        conn.execute("""
            INSERT OR REPLACE INTO projects 
            (id, name, version, last_modified, discovery_date, discovery_params)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (
            self.project_id,
            file_info.get('name', 'Unknown'),
            file_info.get('version', 'Unknown'),
            file_info.get('lastModified', ''),
            datetime.now().isoformat(),
            json.dumps(discovery_params)
        ))
    
    def _create_sqlite_indexes(self, conn: sqlite3.Connection) -> None:
        """Cria índices para otimizar consultas."""
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_nodes_type ON nodes(node_type)",
            "CREATE INDEX IF NOT EXISTS idx_nodes_level ON nodes(level)",
            "CREATE INDEX IF NOT EXISTS idx_nodes_parent ON nodes(parent_id)",
            "CREATE INDEX IF NOT EXISTS idx_nodes_complexity ON nodes(estimated_complexity)",
            "CREATE INDEX IF NOT EXISTS idx_nodes_name ON nodes(name)"
        ]
        
        for index_sql in indexes:
            conn.execute(index_sql)
    
    def load_discovery_data(self) -> Optional[pd.DataFrame]:
        """
        Carrega dados de descoberta do armazenamento.
        
        Returns:
            DataFrame com dados carregados ou None se não encontrado
        """
        parquet_path = self.parquet_dir / "discovered_nodes.parquet"
        
        if parquet_path.exists():
            logger.info(f"Carregando dados do Parquet: {parquet_path}")
            return pd.read_parquet(parquet_path)
        
        sqlite_path = self.sqlite_dir / "figma_data.db"
        if sqlite_path.exists():
            logger.info(f"Carregando dados do SQLite: {sqlite_path}")
            with sqlite3.connect(sqlite_path) as conn:
                return pd.read_sql("SELECT * FROM nodes", conn)
        
        logger.warning("Nenhum dado de descoberta encontrado")
        return None
    
    def get_components_for_generation(self) -> Optional[pd.DataFrame]:
        """
        Obtém componentes otimizados para geração de código.
        
        Returns:
            DataFrame com componentes filtrados e organizados
        """
        sqlite_path = self.sqlite_dir / "figma_data.db"
        
        if not sqlite_path.exists():
            logger.warning("Banco SQLite não encontrado")
            return None
        
        with sqlite3.connect(sqlite_path) as conn:
            query = """
                SELECT * FROM components
                WHERE estimated_complexity IN ('medium', 'high')
                OR (estimated_complexity = 'low' AND level <= 2)
                ORDER BY level, estimated_complexity DESC, name
            """
            return pd.read_sql(query, conn)
