"""
Descobridor inteligente de nodes do Figma.

Este módulo implementa estratégias para descobrir nodes em arquivos Figma
sem baixar o arquivo completo, baseado na estratégia do MCP server.
"""

import logging
import requests
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class NodeType(Enum):
    """Tipos de nodes descobertos."""
    PAGE = "page"
    FRAME = "frame"
    COMPONENT = "component"
    INSTANCE = "instance"
    TEXT = "text"
    SHAPE = "shape"
    GROUP = "group"
    OTHER = "other"


@dataclass
class DiscoveredNode:
    """Representa um node descoberto."""
    id: str
    name: str
    type: str
    node_type: NodeType
    level: int
    parent_id: Optional[str] = None
    parent_name: Optional[str] = None
    children_count: int = 0
    has_layout: bool = False
    has_content: bool = False
    estimated_complexity: str = "low"  # low, medium, high
    description: str = ""


class FigmaDiscovery:
    """
    Descobridor inteligente de nodes do Figma.
    
    Implementa estratégias otimizadas para descobrir nodes específicos
    sem baixar arquivos completos de 50MB+.
    """
    
    def __init__(self, api_token: str):
        """
        Inicializa o descobridor.
        
        Args:
            api_token: Token da API do Figma
        """
        self.api_token = api_token
        self.base_url = "https://api.figma.com/v1"
        self.headers = {
            "X-Figma-Token": api_token,
            "Content-Type": "application/json"
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def discover_file_structure(
        self, 
        file_key: str, 
        max_depth: int = 3,
        include_components_only: bool = False
    ) -> List[DiscoveredNode]:
        """
        Descobre a estrutura de um arquivo Figma de forma otimizada.
        
        Args:
            file_key: Chave do arquivo Figma
            max_depth: Profundidade máxima de descoberta
            include_components_only: Se deve incluir apenas componentes
            
        Returns:
            Lista de nodes descobertos
        """
        logger.info(f"Descobrindo estrutura do arquivo: {file_key}")
        
        discovered_nodes = []
        
        try:
            # Etapa 1: Descobrir páginas (depth=1)
            pages = self._discover_pages(file_key)
            discovered_nodes.extend(pages)
            
            # Etapa 2: Para cada página, descobrir elementos principais
            for page in pages:
                if page.node_type == NodeType.PAGE:
                    elements = self._discover_page_elements(
                        file_key, 
                        page.id, 
                        page.name,
                        max_depth - 1,
                        include_components_only
                    )
                    discovered_nodes.extend(elements)
            
            # Etapa 3: Analisar e classificar nodes
            self._analyze_nodes(discovered_nodes)
            
            logger.info(f"Descobertos {len(discovered_nodes)} nodes")
            return discovered_nodes
            
        except Exception as e:
            logger.error(f"Erro na descoberta: {e}")
            raise

    def discover_page_nodes(
        self,
        file_key: str,
        page_id: str,
        max_depth: int = 3
    ) -> List[DiscoveredNode]:
        """
        Descobre nodes em uma página específica.

        Args:
            file_key: Chave do arquivo Figma
            page_id: ID da página específica
            max_depth: Profundidade máxima de descoberta

        Returns:
            Lista de nodes descobertos na página
        """
        logger.info(f"Descobrindo nodes na página: {page_id}")

        try:
            # Descobrir elementos da página específica
            elements = self._discover_page_elements(
                file_key,
                page_id,
                f"Page_{page_id}",
                max_depth,
                include_components_only=False
            )

            # Analisar e classificar nodes
            self._analyze_nodes(elements)

            logger.info(f"Descobertos {len(elements)} nodes na página")
            return elements

        except Exception as e:
            logger.error(f"Erro na descoberta da página: {e}")
            raise
    
    def _discover_pages(self, file_key: str) -> List[DiscoveredNode]:
        """Descobre páginas do arquivo."""
        logger.info("Descobrindo páginas...")
        
        url = f"{self.base_url}/files/{file_key}?depth=1"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            pages = []
            
            if 'document' in data and 'children' in data['document']:
                for i, page_data in enumerate(data['document']['children']):
                    page = DiscoveredNode(
                        id=page_data.get('id', ''),
                        name=page_data.get('name', f'Página {i+1}'),
                        type=page_data.get('type', 'PAGE'),
                        node_type=NodeType.PAGE,
                        level=0,
                        children_count=len(page_data.get('children', [])),
                        description=f"Página com {len(page_data.get('children', []))} elementos"
                    )
                    pages.append(page)
            
            logger.info(f"Encontradas {len(pages)} páginas")
            return pages
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao descobrir páginas: {e}")
            raise
    
    def _discover_page_elements(
        self, 
        file_key: str, 
        page_id: str, 
        page_name: str,
        max_depth: int,
        include_components_only: bool
    ) -> List[DiscoveredNode]:
        """Descobre elementos de uma página específica."""
        logger.info(f"Descobrindo elementos da página: {page_name}")
        
        if max_depth <= 0:
            return []
        
        url = f"{self.base_url}/files/{file_key}/nodes?ids={page_id}"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            elements = []
            
            if 'nodes' in data and page_id in data['nodes']:
                node_data = data['nodes'][page_id]
                if 'document' in node_data and 'children' in node_data['document']:
                    elements = self._process_children(
                        node_data['document']['children'],
                        level=1,
                        parent_id=page_id,
                        parent_name=page_name,
                        max_depth=max_depth,
                        include_components_only=include_components_only
                    )
            
            logger.info(f"Encontrados {len(elements)} elementos na página {page_name}")
            return elements
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao descobrir elementos da página {page_name}: {e}")
            return []
    
    def _process_children(
        self,
        children: List[Dict[str, Any]],
        level: int,
        parent_id: str,
        parent_name: str,
        max_depth: int,
        include_components_only: bool
    ) -> List[DiscoveredNode]:
        """Processa children de um node recursivamente."""
        elements = []
        
        for child_data in children:
            node_type = self._classify_node_type(child_data)
            
            # Filtrar apenas componentes se solicitado
            if include_components_only and node_type not in [NodeType.COMPONENT, NodeType.INSTANCE, NodeType.FRAME]:
                continue
            
            # Analisar complexidade
            complexity = self._estimate_complexity(child_data)
            
            element = DiscoveredNode(
                id=child_data.get('id', ''),
                name=child_data.get('name', 'Sem nome'),
                type=child_data.get('type', 'UNKNOWN'),
                node_type=node_type,
                level=level,
                parent_id=parent_id,
                parent_name=parent_name,
                children_count=len(child_data.get('children', [])),
                has_layout=self._has_layout_properties(child_data),
                has_content=self._has_content(child_data),
                estimated_complexity=complexity,
                description=self._generate_description(child_data, node_type)
            )
            
            elements.append(element)
            
            # Processar children recursivamente se não atingiu profundidade máxima
            if (max_depth > 1 and 'children' in child_data and 
                child_data['children'] and len(child_data['children']) <= 10):  # Limite para evitar explosão
                
                grandchildren = self._process_children(
                    child_data['children'],
                    level + 1,
                    element.id,
                    element.name,
                    max_depth - 1,
                    include_components_only
                )
                elements.extend(grandchildren)
        
        return elements
    
    def _classify_node_type(self, node_data: Dict[str, Any]) -> NodeType:
        """Classifica o tipo de um node."""
        node_type = node_data.get('type', '').upper()
        
        type_mapping = {
            'COMPONENT': NodeType.COMPONENT,
            'INSTANCE': NodeType.INSTANCE,
            'FRAME': NodeType.FRAME,
            'TEXT': NodeType.TEXT,
            'RECTANGLE': NodeType.SHAPE,
            'ELLIPSE': NodeType.SHAPE,
            'POLYGON': NodeType.SHAPE,
            'STAR': NodeType.SHAPE,
            'VECTOR': NodeType.SHAPE,
            'GROUP': NodeType.GROUP,
        }
        
        return type_mapping.get(node_type, NodeType.OTHER)
    
    def _has_layout_properties(self, node_data: Dict[str, Any]) -> bool:
        """Verifica se um node tem propriedades de layout."""
        layout_props = [
            'layoutMode', 'primaryAxisAlignItems', 'counterAxisAlignItems',
            'layoutSizingHorizontal', 'layoutSizingVertical'
        ]
        return any(prop in node_data for prop in layout_props)
    
    def _has_content(self, node_data: Dict[str, Any]) -> bool:
        """Verifica se um node tem conteúdo relevante."""
        return any([
            'characters' in node_data,  # Texto
            'fills' in node_data and node_data['fills'],  # Preenchimentos
            'strokes' in node_data and node_data['strokes'],  # Bordas
            'effects' in node_data and node_data['effects'],  # Efeitos
        ])
    
    def _estimate_complexity(self, node_data: Dict[str, Any]) -> str:
        """Estima a complexidade de um node."""
        complexity_score = 0
        
        # Pontos por propriedades
        if self._has_layout_properties(node_data):
            complexity_score += 2
        
        if self._has_content(node_data):
            complexity_score += 1
        
        children_count = len(node_data.get('children', []))
        if children_count > 10:
            complexity_score += 3
        elif children_count > 5:
            complexity_score += 2
        elif children_count > 0:
            complexity_score += 1
        
        # Classificar complexidade
        if complexity_score >= 5:
            return "high"
        elif complexity_score >= 3:
            return "medium"
        else:
            return "low"
    
    def _generate_description(self, node_data: Dict[str, Any], node_type: NodeType) -> str:
        """Gera descrição de um node."""
        parts = []
        
        # Tipo base
        type_descriptions = {
            NodeType.COMPONENT: "Componente reutilizável",
            NodeType.INSTANCE: "Instância de componente",
            NodeType.FRAME: "Container/Frame",
            NodeType.TEXT: "Elemento de texto",
            NodeType.SHAPE: "Forma/Shape",
            NodeType.GROUP: "Grupo de elementos",
            NodeType.OTHER: "Elemento"
        }
        parts.append(type_descriptions.get(node_type, "Elemento"))
        
        # Children
        children_count = len(node_data.get('children', []))
        if children_count > 0:
            parts.append(f"{children_count} filhos")
        
        # Layout
        if self._has_layout_properties(node_data):
            layout_mode = node_data.get('layoutMode', '')
            if layout_mode:
                parts.append(f"layout {layout_mode.lower()}")
        
        # Conteúdo
        if 'characters' in node_data:
            text_preview = node_data['characters'][:30]
            if len(node_data['characters']) > 30:
                text_preview += "..."
            parts.append(f'texto: "{text_preview}"')
        
        return " | ".join(parts)
    
    def _analyze_nodes(self, nodes: List[DiscoveredNode]) -> None:
        """Analisa e enriquece informações dos nodes descobertos."""
        logger.info("Analisando nodes descobertos...")
        
        # Estatísticas
        by_type = {}
        by_complexity = {"low": 0, "medium": 0, "high": 0}
        
        for node in nodes:
            # Contar por tipo
            type_name = node.node_type.value
            by_type[type_name] = by_type.get(type_name, 0) + 1
            
            # Contar por complexidade
            by_complexity[node.estimated_complexity] += 1
        
        logger.info("Estatísticas dos nodes:")
        for node_type, count in by_type.items():
            logger.info(f"  {node_type}: {count}")
        
        logger.info("Complexidade:")
        for complexity, count in by_complexity.items():
            logger.info(f"  {complexity}: {count}")
    
    def get_file_info(self, file_key: str) -> Dict[str, Any]:
        """Obtém informações básicas do arquivo."""
        url = f"{self.base_url}/files/{file_key}?depth=1"
        
        try:
            response = self.session.get(url)
            response.raise_for_status()
            data = response.json()
            
            return {
                'name': data.get('name', 'Arquivo sem nome'),
                'lastModified': data.get('lastModified', ''),
                'thumbnailUrl': data.get('thumbnailUrl', ''),
                'version': data.get('version', ''),
                'role': data.get('role', ''),
                'editorType': data.get('editorType', '')
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao obter informações do arquivo: {e}")
            raise

    def get_node_details(self, file_key: str, node_id: str) -> Dict[str, Any]:
        """
        Obtém detalhes completos de um node específico.

        Args:
            file_key: Chave do arquivo Figma
            node_id: ID do node

        Returns:
            Dados completos do node
        """
        url = f"{self.base_url}/files/{file_key}/nodes?ids={node_id}"

        try:
            response = self.session.get(url)
            response.raise_for_status()
            return response.json()

        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao obter detalhes do node {node_id}: {e}")
            raise
