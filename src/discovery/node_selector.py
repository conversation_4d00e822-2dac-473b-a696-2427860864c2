"""
Seletor inteligente de nodes do Figma.

Este módulo fornece interfaces para seleção de nodes descobertos,
incluindo seleção automática e interativa.
"""

import logging
from typing import List, Dict, Any, Optional, Set
from .figma_discovery import DiscoveredNode, NodeType

logger = logging.getLogger(__name__)


class NodeSelector:
    """
    Seletor inteligente de nodes.
    
    Fornece diferentes estratégias para seleção de nodes baseadas
    em critérios específicos ou interação do usuário.
    """
    
    def __init__(self):
        """Inicializa o seletor."""
        pass
    
    def auto_select_components(
        self, 
        nodes: List[DiscoveredNode],
        max_nodes: int = 10,
        prefer_high_complexity: bool = True
    ) -> List[DiscoveredNode]:
        """
        Seleção automática de componentes relevantes.
        
        Args:
            nodes: Lista de nodes descobertos
            max_nodes: Número máximo de nodes a selecionar
            prefer_high_complexity: Se deve preferir nodes complexos
            
        Returns:
            Lista de nodes selecionados
        """
        logger.info("Executando seleção automática de componentes...")
        
        # Filtrar nodes relevantes
        relevant_nodes = []
        
        for node in nodes:
            # Priorizar componentes, instâncias e frames com conteúdo
            if node.node_type in [NodeType.COMPONENT, NodeType.INSTANCE]:
                relevant_nodes.append(node)
            elif (node.node_type == NodeType.FRAME and 
                  (node.has_layout or node.has_content or node.children_count > 0)):
                relevant_nodes.append(node)
            elif node.node_type == NodeType.TEXT and node.has_content:
                relevant_nodes.append(node)
        
        # Ordenar por relevância
        relevant_nodes.sort(key=self._calculate_relevance_score, reverse=True)
        
        # Aplicar filtros adicionais
        if prefer_high_complexity:
            # Preferir nodes de alta complexidade
            high_complexity = [n for n in relevant_nodes if n.estimated_complexity == "high"]
            medium_complexity = [n for n in relevant_nodes if n.estimated_complexity == "medium"]
            low_complexity = [n for n in relevant_nodes if n.estimated_complexity == "low"]
            
            # Combinar priorizando alta complexidade
            ordered_nodes = high_complexity + medium_complexity + low_complexity
        else:
            ordered_nodes = relevant_nodes
        
        # Limitar número de nodes
        selected = ordered_nodes[:max_nodes]
        
        logger.info(f"Selecionados {len(selected)} nodes automaticamente")
        return selected
    
    def interactive_select(self, nodes: List[DiscoveredNode]) -> List[DiscoveredNode]:
        """
        Seleção interativa de nodes.
        
        Args:
            nodes: Lista de nodes descobertos
            
        Returns:
            Lista de nodes selecionados pelo usuário
        """
        print("\n" + "="*80)
        print("🎯 SELEÇÃO INTERATIVA DE NODES")
        print("="*80)
        
        # Agrupar nodes por página
        by_page = {}
        for node in nodes:
            if node.level == 0:  # Página
                by_page[node.name] = []
            elif node.parent_name:
                if node.parent_name not in by_page:
                    by_page[node.parent_name] = []
                by_page[node.parent_name].append(node)
        
        selected_nodes = []
        
        # Mostrar opções por página
        for page_name, page_nodes in by_page.items():
            if not page_nodes:  # Pular páginas vazias
                continue
                
            print(f"\n📄 PÁGINA: {page_name}")
            print("-" * 60)
            
            # Mostrar nodes da página
            for i, node in enumerate(page_nodes, 1):
                complexity_icon = {
                    "low": "🟢",
                    "medium": "🟡", 
                    "high": "🔴"
                }.get(node.estimated_complexity, "⚪")
                
                type_icon = {
                    NodeType.COMPONENT: "🧩",
                    NodeType.INSTANCE: "📋",
                    NodeType.FRAME: "📦",
                    NodeType.TEXT: "📝",
                    NodeType.SHAPE: "🔷",
                    NodeType.GROUP: "📁"
                }.get(node.node_type, "❓")
                
                print(f"  {i:2d}. {type_icon} {complexity_icon} {node.name}")
                print(f"      {node.description}")
                if node.children_count > 0:
                    print(f"      └─ {node.children_count} elementos filhos")
            
            # Solicitar seleção
            print(f"\n💡 Selecione nodes da página '{page_name}':")
            print("   • Digite números separados por vírgula (ex: 1,3,5)")
            print("   • Digite 'all' para selecionar todos")
            print("   • Digite 'auto' para seleção automática")
            print("   • Digite 'skip' para pular esta página")
            
            while True:
                try:
                    selection = input(f"\n🎯 Seleção para '{page_name}': ").strip().lower()
                    
                    if selection == 'skip':
                        break
                    elif selection == 'all':
                        selected_nodes.extend(page_nodes)
                        print(f"✅ Selecionados todos os {len(page_nodes)} nodes")
                        break
                    elif selection == 'auto':
                        auto_selected = self.auto_select_components(page_nodes, max_nodes=5)
                        selected_nodes.extend(auto_selected)
                        print(f"✅ Selecionados {len(auto_selected)} nodes automaticamente")
                        break
                    else:
                        # Parsear números
                        indices = [int(x.strip()) for x in selection.split(',') if x.strip().isdigit()]
                        valid_indices = [i for i in indices if 1 <= i <= len(page_nodes)]
                        
                        if valid_indices:
                            page_selected = [page_nodes[i-1] for i in valid_indices]
                            selected_nodes.extend(page_selected)
                            print(f"✅ Selecionados {len(page_selected)} nodes")
                            break
                        else:
                            print("❌ Seleção inválida. Tente novamente.")
                
                except (ValueError, IndexError):
                    print("❌ Formato inválido. Use números separados por vírgula.")
        
        print(f"\n🎉 SELEÇÃO CONCLUÍDA: {len(selected_nodes)} nodes selecionados")
        return selected_nodes
    
    def select_by_criteria(
        self,
        nodes: List[DiscoveredNode],
        node_types: Optional[List[NodeType]] = None,
        complexity_levels: Optional[List[str]] = None,
        has_layout: Optional[bool] = None,
        has_content: Optional[bool] = None,
        min_children: Optional[int] = None,
        max_children: Optional[int] = None
    ) -> List[DiscoveredNode]:
        """
        Seleciona nodes baseado em critérios específicos.
        
        Args:
            nodes: Lista de nodes descobertos
            node_types: Tipos de nodes a incluir
            complexity_levels: Níveis de complexidade a incluir
            has_layout: Se deve ter propriedades de layout
            has_content: Se deve ter conteúdo
            min_children: Número mínimo de filhos
            max_children: Número máximo de filhos
            
        Returns:
            Lista de nodes que atendem aos critérios
        """
        filtered_nodes = []
        
        for node in nodes:
            # Filtrar por tipo
            if node_types and node.node_type not in node_types:
                continue
            
            # Filtrar por complexidade
            if complexity_levels and node.estimated_complexity not in complexity_levels:
                continue
            
            # Filtrar por layout
            if has_layout is not None and node.has_layout != has_layout:
                continue
            
            # Filtrar por conteúdo
            if has_content is not None and node.has_content != has_content:
                continue
            
            # Filtrar por número de filhos
            if min_children is not None and node.children_count < min_children:
                continue
            
            if max_children is not None and node.children_count > max_children:
                continue
            
            filtered_nodes.append(node)
        
        logger.info(f"Filtrados {len(filtered_nodes)} nodes pelos critérios")
        return filtered_nodes
    
    def _calculate_relevance_score(self, node: DiscoveredNode) -> float:
        """Calcula score de relevância de um node."""
        score = 0.0
        
        # Pontos por tipo
        type_scores = {
            NodeType.COMPONENT: 10.0,
            NodeType.INSTANCE: 8.0,
            NodeType.FRAME: 6.0,
            NodeType.TEXT: 4.0,
            NodeType.SHAPE: 2.0,
            NodeType.GROUP: 3.0,
            NodeType.OTHER: 1.0
        }
        score += type_scores.get(node.node_type, 1.0)
        
        # Pontos por complexidade
        complexity_scores = {
            "high": 5.0,
            "medium": 3.0,
            "low": 1.0
        }
        score += complexity_scores.get(node.estimated_complexity, 1.0)
        
        # Pontos por propriedades
        if node.has_layout:
            score += 3.0
        
        if node.has_content:
            score += 2.0
        
        # Pontos por número de filhos (mas não muito)
        if node.children_count > 0:
            score += min(node.children_count * 0.5, 3.0)
        
        # Penalizar nodes muito profundos
        if node.level > 2:
            score -= (node.level - 2) * 0.5
        
        return score
    
    def export_selection(self, selected_nodes: List[DiscoveredNode]) -> Dict[str, Any]:
        """
        Exporta seleção para formato serializável.
        
        Args:
            selected_nodes: Nodes selecionados
            
        Returns:
            Dados da seleção em formato dict
        """
        return {
            'selected_count': len(selected_nodes),
            'nodes': [
                {
                    'id': node.id,
                    'name': node.name,
                    'type': node.type,
                    'node_type': node.node_type.value,
                    'level': node.level,
                    'parent_id': node.parent_id,
                    'parent_name': node.parent_name,
                    'children_count': node.children_count,
                    'has_layout': node.has_layout,
                    'has_content': node.has_content,
                    'estimated_complexity': node.estimated_complexity,
                    'description': node.description
                }
                for node in selected_nodes
            ],
            'summary': {
                'by_type': self._count_by_attribute(selected_nodes, 'node_type'),
                'by_complexity': self._count_by_attribute(selected_nodes, 'estimated_complexity'),
                'by_level': self._count_by_attribute(selected_nodes, 'level')
            }
        }
    
    def _count_by_attribute(self, nodes: List[DiscoveredNode], attribute: str) -> Dict[str, int]:
        """Conta nodes por atributo."""
        counts = {}
        for node in nodes:
            value = getattr(node, attribute)
            if hasattr(value, 'value'):  # Enum
                value = value.value
            counts[str(value)] = counts.get(str(value), 0) + 1
        return counts
