"""
Parser principal para arquivos JSON da API do Figma.

Este módulo é responsável por:
- Carregar e validar arquivos JSON da API do Figma
- Extrair informações básicas do projeto
- Navegar pela hierarquia de nodes
- Identificar componentes e suas propriedades
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class FigmaProject:
    """Representa um projeto do Figma com suas informações básicas."""
    name: str
    last_modified: str
    version: str
    thumbnail_url: str
    role: str
    editor_type: str
    nodes: Dict[str, Any] = field(default_factory=dict)
    components: Dict[str, Any] = field(default_factory=dict)
    component_sets: Dict[str, Any] = field(default_factory=dict)
    styles: Dict[str, Any] = field(default_factory=dict)


@dataclass
class FigmaNode:
    """Representa um node individual do Figma."""
    id: str
    name: str
    type: str
    children: List['FigmaNode'] = field(default_factory=list)
    properties: Dict[str, Any] = field(default_factory=dict)
    parent_id: Optional[str] = None


class FigmaParser:
    """Parser principal para arquivos JSON da API do Figma."""
    
    def __init__(self):
        self.project: Optional[FigmaProject] = None
        self.nodes_map: Dict[str, FigmaNode] = {}
        
    def load_file(self, file_path: str) -> FigmaProject:
        """
        Carrega um arquivo JSON da API do Figma.
        
        Args:
            file_path: Caminho para o arquivo JSON
            
        Returns:
            FigmaProject: Objeto com os dados do projeto
            
        Raises:
            FileNotFoundError: Se o arquivo não for encontrado
            json.JSONDecodeError: Se o JSON for inválido
        """
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"Arquivo não encontrado: {file_path}")
            
        logger.info(f"Carregando arquivo: {file_path}")
        
        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        return self.parse_data(data)
    
    def parse_data(self, data: Dict[str, Any]) -> FigmaProject:
        """
        Converte os dados JSON em um objeto FigmaProject.
        Funciona tanto com dados de arquivo completo quanto de nodes específicos.

        Args:
            data: Dados JSON da API do Figma

        Returns:
            FigmaProject: Projeto estruturado
        """
        logger.info(f"Processando dados do Figma...")

        # Verificar se é resposta de arquivo completo ou nodes específicos
        if 'document' in data:
            # Resposta de arquivo completo
            return self._parse_full_file_data(data)
        elif 'nodes' in data:
            # Resposta de nodes específicos
            return self._parse_nodes_data(data)
        else:
            raise ValueError("Formato de dados não reconhecido")

    def parse_with_new_converter(self, data: Dict[str, Any]):
        """
        Usa o novo sistema de conversão baseado no MCP server.

        Args:
            data: Dados JSON da API do Figma

        Returns:
            SimplifiedDesign: Design simplificado otimizado
        """
        from converters.figma_converter import FigmaConverter

        converter = FigmaConverter()
        return converter.parse_figma_response(data)

    def _parse_full_file_data(self, data: Dict[str, Any]) -> FigmaProject:
        """
        Processa dados de arquivo completo do Figma.

        Args:
            data: Dados JSON do arquivo completo

        Returns:
            FigmaProject: Projeto estruturado
        """
        logger.info(f"Processando arquivo completo: {data.get('name', 'Sem nome')}")

        project = FigmaProject(
            name=data.get('name', ''),
            last_modified=data.get('lastModified', ''),
            version=data.get('version', ''),
            thumbnail_url=data.get('thumbnailUrl', ''),
            role=data.get('role', ''),
            editor_type=data.get('editorType', '')
        )

        # Processar documento principal
        if 'document' in data:
            project.nodes = {'root': {'document': data['document']}}
            self._build_nodes_hierarchy(project.nodes)

        # Processar componentes, componentSets e styles
        project.components = data.get('components', {})
        project.component_sets = data.get('componentSets', {})
        project.styles = data.get('styles', {})

        self.project = project
        return project

    def _parse_nodes_data(self, data: Dict[str, Any]) -> FigmaProject:
        """
        Processa dados de nodes específicos do Figma.

        Args:
            data: Dados JSON de nodes específicos

        Returns:
            FigmaProject: Projeto estruturado
        """
        logger.info("Processando nodes específicos...")

        # Criar projeto básico para nodes específicos
        project = FigmaProject(
            name=data.get('name', 'Nodes Específicos'),
            last_modified=data.get('lastModified', ''),
            version=data.get('version', ''),
            thumbnail_url=data.get('thumbnailUrl', ''),
            role=data.get('role', ''),
            editor_type=data.get('editorType', '')
        )

        # Processar nodes
        if 'nodes' in data:
            project.nodes = data['nodes']
            self._build_nodes_hierarchy(data['nodes'])

        # Processar componentes, componentSets e styles se existirem
        for node_data in data.get('nodes', {}).values():
            if 'components' in node_data:
                project.components.update(node_data['components'])
            if 'componentSets' in node_data:
                project.component_sets.update(node_data['componentSets'])
            if 'styles' in node_data:
                project.styles.update(node_data['styles'])

        self.project = project
        return project
    
    def _build_nodes_hierarchy(self, nodes_data: Dict[str, Any]) -> None:
        """
        Constrói a hierarquia de nodes do projeto.
        
        Args:
            nodes_data: Dados dos nodes do JSON
        """
        logger.info("Construindo hierarquia de nodes...")
        
        # Primeiro passo: criar todos os nodes
        for node_id, node_data in nodes_data.items():
            if 'document' in node_data:
                self._create_node_recursive(node_data['document'])
                
        logger.info(f"Total de nodes processados: {len(self.nodes_map)}")
    
    def _create_node_recursive(self, node_data: Dict[str, Any], parent_id: Optional[str] = None) -> FigmaNode:
        """
        Cria um node e seus filhos recursivamente.
        
        Args:
            node_data: Dados do node
            parent_id: ID do node pai
            
        Returns:
            FigmaNode: Node criado
        """
        node = FigmaNode(
            id=node_data.get('id', ''),
            name=node_data.get('name', ''),
            type=node_data.get('type', ''),
            parent_id=parent_id,
            properties=self._extract_node_properties(node_data)
        )
        
        # Processar filhos se existirem
        if 'children' in node_data:
            for child_data in node_data['children']:
                child_node = self._create_node_recursive(child_data, node.id)
                node.children.append(child_node)
        
        self.nodes_map[node.id] = node
        return node
    
    def _extract_node_properties(self, node_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extrai propriedades relevantes de um node.
        
        Args:
            node_data: Dados brutos do node
            
        Returns:
            Dict com propriedades estruturadas
        """
        properties = {}
        
        # Propriedades de posicionamento
        if 'absoluteBoundingBox' in node_data:
            bbox = node_data['absoluteBoundingBox']
            properties['position'] = {
                'x': bbox.get('x', 0),
                'y': bbox.get('y', 0),
                'width': bbox.get('width', 0),
                'height': bbox.get('height', 0)
            }
        
        # Propriedades visuais
        if 'fills' in node_data:
            properties['fills'] = node_data['fills']
            
        if 'strokes' in node_data:
            properties['strokes'] = node_data['strokes']
            
        if 'effects' in node_data:
            properties['effects'] = node_data['effects']
            
        # Propriedades de texto
        if node_data.get('type') == 'TEXT':
            if 'characters' in node_data:
                properties['text_content'] = node_data['characters']
            if 'style' in node_data:
                properties['text_style'] = node_data['style']
                
        # Propriedades de componente
        if 'componentId' in node_data:
            properties['component_id'] = node_data['componentId']
            
        if 'componentProperties' in node_data:
            properties['component_properties'] = node_data['componentProperties']
            
        # Variáveis vinculadas
        if 'boundVariables' in node_data:
            properties['bound_variables'] = node_data['boundVariables']
            
        return properties
    
    def get_nodes_by_type(self, node_type: str) -> List[FigmaNode]:
        """
        Retorna todos os nodes de um tipo específico.
        
        Args:
            node_type: Tipo do node (FRAME, TEXT, RECTANGLE, etc.)
            
        Returns:
            Lista de nodes do tipo especificado
        """
        return [node for node in self.nodes_map.values() if node.type == node_type]
    
    def get_node_by_id(self, node_id: str) -> Optional[FigmaNode]:
        """
        Retorna um node específico pelo ID.
        
        Args:
            node_id: ID do node
            
        Returns:
            FigmaNode ou None se não encontrado
        """
        return self.nodes_map.get(node_id)
    
    def get_project_summary(self) -> Dict[str, Any]:
        """
        Retorna um resumo do projeto analisado.
        
        Returns:
            Dict com estatísticas do projeto
        """
        if not self.project:
            return {}
            
        node_types = {}
        for node in self.nodes_map.values():
            node_types[node.type] = node_types.get(node.type, 0) + 1
            
        return {
            'project_name': self.project.name,
            'total_nodes': len(self.nodes_map),
            'node_types': node_types,
            'total_components': len(self.project.components),
            'total_component_sets': len(self.project.component_sets),
            'total_styles': len(self.project.styles)
        }


if __name__ == "__main__":
    # Exemplo de uso
    parser = FigmaParser()
    
    try:
        project = parser.load_file("../../data/raw/figma_api_response.json")
        summary = parser.get_project_summary()
        
        print("=== RESUMO DO PROJETO ===")
        for key, value in summary.items():
            print(f"{key}: {value}")
            
        print("\n=== TIPOS DE NODES ENCONTRADOS ===")
        frames = parser.get_nodes_by_type('FRAME')
        texts = parser.get_nodes_by_type('TEXT')
        
        print(f"Frames encontrados: {len(frames)}")
        print(f"Textos encontrados: {len(texts)}")
        
    except Exception as e:
        logger.error(f"Erro ao processar arquivo: {e}")
