#!/usr/bin/env python3
"""
Script integrado para descoberta e armazenamento estruturado de dados do Figma.

Este script combina a descoberta otimizada de páginas/nodes com o armazenamento
estruturado em Parquet e SQLite para futura geração de componentes.
"""

import sys
import json
from pathlib import Path
from typing import List, Dict, Any
from collections import Counter

from src.discovery.figma_discovery import FigmaDiscovery, DiscoveredNode
from src.storage.structured_storage import StructuredStorage
from src.utils.config_loader import ConfigLoader
from src.utils.logging_config import setup_logging, get_logger, LoggingContext

# Configurar logging
setup_logging(log_level="INFO", log_to_file=True)
logger = get_logger(__name__)


def main():
    """Função principal."""
    print("🔍 Figma Discovery & Structured Storage")
    print("=" * 50)

    with LoggingContext("Figma Discovery & Structured Storage"):
        try:
            # Carregar configurações
            config_loader = ConfigLoader()
            figma_config = config_loader.get_figma_config()
            processing_config = config_loader.get_processing_config()
            output_config = config_loader.get_output_config()
        
            file_key = figma_config['file_key']
            api_token = figma_config['token']
            max_depth = processing_config.get('max_depth', 3)

            print(f"📁 Arquivo: {file_key}")
            print(f"🔧 Profundidade máxima: {max_depth}")

            # Criar descobridor
            discovery = FigmaDiscovery(api_token)
        
            # Obter informações do arquivo
            print("\n📋 Obtendo informações do arquivo...")
            file_info = discovery.get_file_info(file_key)
            print(f"   Nome: {file_info['name']}")
            print(f"   Última modificação: {file_info['lastModified']}")
            
            # Etapa 1: Descobrir páginas
            print("\n📄 Descobrindo páginas disponíveis...")
            pages = discovery.discover_file_structure(
                file_key,
                max_depth=1,  # Apenas páginas
                include_components_only=False
            )
            
            # Filtrar apenas páginas (CANVAS)
            pages = [node for node in pages if node.type == 'CANVAS']
            
            if not pages:
                print("❌ Nenhuma página encontrada no arquivo")
                return 1
            
            print(f"\n📋 Páginas encontradas ({len(pages)}):")
            for i, page in enumerate(pages, 1):
                print(f"   {i}. {page.name}")
            
            # Seleção de páginas
            selected_pages = select_pages(pages)
            
            if not selected_pages:
                print("❌ Nenhuma página selecionada")
                return 1
            
            print(f"\n✅ Páginas selecionadas ({len(selected_pages)}):")
            for page in selected_pages:
                print(f"   • {page.name}")
            
            # Etapa 2: Descobrir nodes nas páginas selecionadas
            print(f"\n🔍 Descobrindo nodes nas páginas selecionadas (depth={max_depth})...")
            
            all_discovered_nodes = []
            
            for page in selected_pages:
                print(f"\n   📄 Processando página: {page.name}")
                
                # Descobrir nodes na página específica
                page_nodes = discovery.discover_page_nodes(
                    file_key,
                    page.id,
                    max_depth=max_depth
                )
                
                print(f"      Encontrados {len(page_nodes)} nodes")
                all_discovered_nodes.extend(page_nodes)
            
            print(f"\n✅ Total de nodes descobertos: {len(all_discovered_nodes)}")

            # Etapa 2.5: Seleção de componentes específicos
            print("\n🎯 Seleção de componentes específicos...")
            selected_components = select_components(all_discovered_nodes)

            if not selected_components:
                print("❌ Nenhum componente selecionado")
                return 1

            print(f"\n✅ Componentes selecionados ({len(selected_components)}):")
            for comp in selected_components[:10]:  # Mostrar apenas os primeiros 10
                print(f"   • {comp.name} ({comp.node_type.value}, nível {comp.level})")
            if len(selected_components) > 10:
                print(f"   ... e mais {len(selected_components) - 10} componentes")

            # Etapa 3: Armazenamento estruturado
            print("\n💾 Iniciando armazenamento estruturado...")
            
            storage = StructuredStorage(
                project_id=file_key,
                base_dir=output_config['base_dir']
            )
            
            discovery_params = {
                'file_key': file_key,
                'max_depth': max_depth,
                'selected_pages': [{'id': p.id, 'name': p.name} for p in selected_pages],
                'total_pages_processed': len(selected_pages)
            }
            
            storage_paths = storage.store_discovery_data(
                discovered_nodes=selected_components,
                file_info=file_info,
                discovery_params=discovery_params
            )
            
            print(f"\n✅ Dados armazenados:")
            print(f"   🗄️  SQLite: {storage_paths['sqlite']}")
            print(f"   📁 Projeto: {storage_paths['project_dir']}")
            
            # Etapa 4: Análise dos dados armazenados
            print("\n📊 Análise dos dados armazenados...")
            
            # Carregar componentes otimizados
            components_list = storage.get_components_for_generation()
            
            if components_list is not None and len(components_list) > 0:
                print(f"\n🎯 Componentes identificados para geração ({len(components_list)}):")

                # Agrupar por complexidade usando Counter
                complexities = [comp.get('estimated_complexity', 'unknown') for comp in components_list]
                by_complexity = Counter(complexities)
                icon_map = {"low": "🟢", "medium": "🟡", "high": "🔴"}

                for complexity, count in by_complexity.items():
                    icon = icon_map.get(complexity, "❓")
                    print(f"   {icon} {complexity}: {count} componentes")

                # Mostrar top 10 componentes mais relevantes
                print(f"\n🏆 Top 10 componentes mais relevantes:")
                top_components = components_list[:10]  # Primeiros 10 (já ordenados pela query)
                for comp in top_components:
                    complexity = comp.get('estimated_complexity', 'unknown')
                    complexity_icon = icon_map.get(complexity, "❓")
                    name = comp.get('name', 'Unknown')
                    node_type = comp.get('node_type', 'unknown')
                    level = comp.get('level', 0)
                    print(f"   {complexity_icon} {name} ({node_type}, nível {level})")
            
            # Salvar descoberta tradicional também
            save_traditional_discovery(file_info, file_key, selected_pages, selected_components, max_depth)
            
            print("\n🎉 Processamento e armazenamento concluídos com sucesso!")
            print("\n💡 Próximos passos:")
            print("   1. Integrar com design system (Storybook)")
            print("   2. Usar IA para gerar componentes React/Angular")
            print("   3. Aplicar estilos do design system")
            
            return 0

        except FileNotFoundError as e:
            print(f"❌ {e}")
            print("\n💡 Crie um arquivo project_config.yaml baseado no exemplo:")
            print("   cp project_config.example.yaml project_config.yaml")
            return 1

        except Exception as e:
            logger.error(f"Erro durante processamento: {e}")
            import traceback
            traceback.print_exc()
            return 1


def select_components(nodes: List[DiscoveredNode]) -> List[DiscoveredNode]:
    """
    Permite ao usuário selecionar componentes específicos para processamento.

    Args:
        nodes: Lista de nodes descobertos

    Returns:
        Lista de componentes selecionados
    """
    # Filtrar apenas componentes relevantes (não páginas)
    components = [node for node in nodes if node.node_type.value != 'page']

    if not components:
        print("❌ Nenhum componente encontrado")
        return []

    # Agrupar por complexidade para melhor visualização
    by_complexity = {}
    for comp in components:
        complexity = comp.estimated_complexity
        if complexity not in by_complexity:
            by_complexity[complexity] = []
        by_complexity[complexity].append(comp)

    print(f"\n📋 Componentes encontrados ({len(components)}):")
    print("=" * 50)

    # Mostrar resumo por complexidade
    for complexity in ['high', 'medium', 'low']:
        if complexity in by_complexity:
            count = len(by_complexity[complexity])
            icon = {"low": "🟢", "medium": "🟡", "high": "🔴"}[complexity]
            print(f"{icon} {complexity.capitalize()}: {count} componentes")

    print("\n🎯 Opções de seleção:")
    print("   1. Automática (componentes de alta/média complexidade)")
    print("   2. Por complexidade (escolher nível)")
    print("   3. Interativa (escolher individualmente)")
    print("   4. Todos os componentes")

    while True:
        try:
            choice = input("\n👉 Sua escolha (1-4): ").strip()

            if choice == '1':
                # Seleção automática: alta e média complexidade
                selected = []
                for complexity in ['high', 'medium']:
                    if complexity in by_complexity:
                        selected.extend(by_complexity[complexity])

                if not selected and 'low' in by_complexity:
                    # Se não há alta/média, pegar algumas de baixa complexidade
                    selected = by_complexity['low'][:10]

                return selected

            elif choice == '2':
                # Seleção por complexidade
                print("\nEscolha a complexidade:")
                print("   h. Alta (high)")
                print("   m. Média (medium)")
                print("   l. Baixa (low)")

                complexity_choice = input("👉 Complexidade (h/m/l): ").strip().lower()
                complexity_map = {'h': 'high', 'm': 'medium', 'l': 'low'}

                if complexity_choice in complexity_map:
                    complexity = complexity_map[complexity_choice]
                    if complexity in by_complexity:
                        return by_complexity[complexity]
                    else:
                        print(f"❌ Nenhum componente de complexidade {complexity}")
                        continue
                else:
                    print("❌ Opção inválida")
                    continue

            elif choice == '3':
                # Seleção interativa
                return select_components_interactive(components)

            elif choice == '4':
                # Todos os componentes
                return components

            else:
                print("❌ Opção inválida. Escolha 1-4")
                continue

        except KeyboardInterrupt:
            print("\n❌ Operação cancelada")
            return []


def select_components_interactive(components: List[DiscoveredNode]) -> List[DiscoveredNode]:
    """
    Seleção interativa de componentes.

    Args:
        components: Lista de componentes disponíveis

    Returns:
        Lista de componentes selecionados
    """
    print(f"\n📋 Componentes disponíveis ({len(components)}):")
    print("=" * 60)

    # Mostrar componentes numerados
    for i, comp in enumerate(components, 1):
        complexity_icon = {"low": "🟢", "medium": "🟡", "high": "🔴"}[comp.estimated_complexity]
        print(f"   {i:2d}. {comp.name[:40]:<40} {complexity_icon} {comp.node_type.value} (nível {comp.level})")

    print("\n🎯 Seleção interativa:")
    print("   Digite os números dos componentes separados por vírgula")
    print("   Exemplos: 1,3,5 ou 1-10 ou 'all' para todos")

    while True:
        try:
            selection = input("\n👉 Sua seleção: ").strip()

            if selection.lower() == 'all':
                return components

            if not selection:
                continue

            # Parsear seleção
            indices = []
            for part in selection.split(','):
                part = part.strip()
                if '-' in part:
                    # Range (ex: 1-10)
                    start, end = map(int, part.split('-'))
                    indices.extend(range(start, end + 1))
                else:
                    # Número único
                    indices.append(int(part))

            # Validar e converter para componentes
            selected_components = []
            for idx in indices:
                if 1 <= idx <= len(components):
                    selected_components.append(components[idx - 1])
                else:
                    print(f"⚠️  Índice inválido: {idx}")
                    continue

            if selected_components:
                return selected_components
            else:
                print("❌ Nenhum componente válido selecionado")

        except ValueError:
            print("❌ Formato inválido. Use números separados por vírgula")
        except KeyboardInterrupt:
            print("\n❌ Operação cancelada")
            return []


def select_pages(pages: List[DiscoveredNode]) -> List[DiscoveredNode]:
    """
    Permite ao usuário selecionar páginas para processamento.
    
    Args:
        pages: Lista de páginas descobertas
        
    Returns:
        Lista de páginas selecionadas
    """
    print("\n🎯 Seleção de páginas:")
    print("   Digite os números das páginas que deseja processar")
    print("   (separados por vírgula, ex: 1,3,5 ou 'all' para todas)")
    
    while True:
        try:
            selection = input("\n👉 Sua seleção: ").strip()
            
            if selection.lower() == 'all':
                return pages
            
            if not selection:
                continue
                
            # Parsear seleção
            indices = []
            for part in selection.split(','):
                part = part.strip()
                if '-' in part:
                    # Range (ex: 1-3)
                    start, end = map(int, part.split('-'))
                    indices.extend(range(start, end + 1))
                else:
                    # Número único
                    indices.append(int(part))
            
            # Validar e converter para páginas
            selected_pages = []
            for idx in indices:
                if 1 <= idx <= len(pages):
                    selected_pages.append(pages[idx - 1])
                else:
                    print(f"⚠️  Índice inválido: {idx}")
                    continue
            
            if selected_pages:
                return selected_pages
            else:
                print("❌ Nenhuma página válida selecionada")
                
        except ValueError:
            print("❌ Formato inválido. Use números separados por vírgula")
        except KeyboardInterrupt:
            print("\n❌ Operação cancelada")
            return []


def save_traditional_discovery(file_info: Dict[str, Any], file_key: str, 
                              selected_pages: List[DiscoveredNode], 
                              discovered_nodes: List[DiscoveredNode], 
                              max_depth: int) -> None:
    """Salva os resultados da descoberta no formato tradicional."""
    
    # Preparar dados
    discovery_data = {
        'file_info': file_info,
        'discovery_params': {
            'file_key': file_key,
            'max_depth': max_depth,
            'selected_pages': [{'id': p.id, 'name': p.name} for p in selected_pages]
        },
        'discovered_nodes': [
            {
                'id': node.id,
                'name': node.name,
                'type': node.type,
                'node_type': node.node_type.value,
                'level': node.level,
                'parent_id': node.parent_id,
                'parent_name': node.parent_name,
                'children_count': node.children_count,
                'has_layout': node.has_layout,
                'has_content': node.has_content,
                'estimated_complexity': node.estimated_complexity,
                'description': node.description
            }
            for node in discovered_nodes
        ]
    }
    
    # Salvar descoberta
    output_path = Path('data/discovery/discovered_nodes.json')
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(discovery_data, f, indent=2, ensure_ascii=False)
    
    print(f"💾 Descoberta tradicional salva em: {output_path}")


if __name__ == "__main__":
    sys.exit(main())