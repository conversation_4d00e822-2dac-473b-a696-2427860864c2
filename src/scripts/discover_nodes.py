#!/usr/bin/env python3
"""
Script principal para descoberta de nodes do Figma.

Este script implementa a estratégia otimizada para descobrir nodes específicos
em arquivos Figma sem baixar o arquivo completo.
"""

import sys
import json
import logging
import argparse
import os
from pathlib import Path
from typing import List

from src.discovery.figma_discovery import FigmaDiscovery, DiscoveredNode
from src.discovery.node_selector import NodeSelector
from src.utils.logging_config import setup_logging, get_logger

# Configurar logging
setup_logging(log_level="INFO", log_to_file=True)
logger = get_logger(__name__)


def main():
    """Função principal."""
    parser = argparse.ArgumentParser(
        description="Descoberta inteligente de nodes do Figma",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:

  # Descobrir estrutura básica
  python discover_nodes.py --file-key ABC123 --output discovered_nodes.json

  # Descoberta com seleção automática
  python discover_nodes.py --file-key ABC123 --auto-select --max-nodes 5

  # Descoberta interativa
  python discover_nodes.py --file-key ABC123 --interactive

  # Descobrir apenas componentes
  python discover_nodes.py --file-key ABC123 --components-only --max-depth 2

  # Usar token de arquivo .env
  python discover_nodes.py --file-key ABC123 --env-file .env
        """
    )
    
    # Argumentos obrigatórios
    parser.add_argument(
        '--file-key',
        required=True,
        help='Chave do arquivo Figma (extraída da URL)'
    )
    
    # Argumentos de configuração
    parser.add_argument(
        '--token',
        help='Token da API do Figma (ou use FIGMA_API_TOKEN)'
    )
    
    parser.add_argument(
        '--env-file',
        help='Arquivo .env para carregar token'
    )
    
    # Argumentos de descoberta
    parser.add_argument(
        '--max-depth',
        type=int,
        default=3,
        help='Profundidade máxima de descoberta (padrão: 3)'
    )
    
    parser.add_argument(
        '--components-only',
        action='store_true',
        help='Descobrir apenas componentes e instâncias'
    )
    
    # Argumentos de seleção
    parser.add_argument(
        '--auto-select',
        action='store_true',
        help='Seleção automática de nodes relevantes'
    )
    
    parser.add_argument(
        '--interactive',
        action='store_true',
        help='Seleção interativa de nodes'
    )
    
    parser.add_argument(
        '--max-nodes',
        type=int,
        default=10,
        help='Número máximo de nodes para seleção automática (padrão: 10)'
    )
    
    # Argumentos de saída
    parser.add_argument(
        '--output',
        default='data/discovery/discovered_nodes.json',
        help='Arquivo de saída (padrão: data/discovery/discovered_nodes.json)'
    )
    
    parser.add_argument(
        '--save-selection',
        help='Arquivo para salvar nodes selecionados'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Saída detalhada'
    )
    
    args = parser.parse_args()
    
    # Configurar logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Obter token
    api_token = get_api_token(args)
    if not api_token:
        print("❌ Token da API do Figma não encontrado")
        print("   Use --token, FIGMA_API_TOKEN ou --env-file")
        return 1
    
    print("🔍 Figma Node Discovery")
    print("=" * 50)
    print(f"📁 Arquivo: {args.file_key}")
    print(f"🔧 Profundidade máxima: {args.max_depth}")
    print(f"🎯 Apenas componentes: {'Sim' if args.components_only else 'Não'}")
    
    try:
        # Criar descobridor
        discovery = FigmaDiscovery(api_token)
        
        # Obter informações do arquivo
        print("\n📋 Obtendo informações do arquivo...")
        file_info = discovery.get_file_info(args.file_key)
        print(f"   Nome: {file_info['name']}")
        print(f"   Última modificação: {file_info['lastModified']}")
        
        # Descobrir estrutura
        print("\n🔍 Descobrindo estrutura do arquivo...")
        discovered_nodes = discovery.discover_file_structure(
            args.file_key,
            max_depth=args.max_depth,
            include_components_only=args.components_only
        )
        
        print(f"\n✅ Descobertos {len(discovered_nodes)} nodes")
        
        # Mostrar resumo
        show_discovery_summary(discovered_nodes)
        
        # Salvar descoberta completa
        output_path = Path(args.output)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        discovery_data = {
            'file_info': file_info,
            'discovery_params': {
                'file_key': args.file_key,
                'max_depth': args.max_depth,
                'components_only': args.components_only
            },
            'discovered_nodes': [
                {
                    'id': node.id,
                    'name': node.name,
                    'type': node.type,
                    'node_type': node.node_type.value,
                    'level': node.level,
                    'parent_id': node.parent_id,
                    'parent_name': node.parent_name,
                    'children_count': node.children_count,
                    'has_layout': node.has_layout,
                    'has_content': node.has_content,
                    'estimated_complexity': node.estimated_complexity,
                    'description': node.description
                }
                for node in discovered_nodes
            ]
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(discovery_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Descoberta salva em: {output_path}")
        
        # Seleção de nodes
        selected_nodes = None
        
        if args.auto_select or args.interactive:
            selector = NodeSelector()
            
            if args.interactive:
                print("\n🎯 Iniciando seleção interativa...")
                selected_nodes = selector.interactive_select(discovered_nodes)
            
            elif args.auto_select:
                print(f"\n🤖 Executando seleção automática (máx: {args.max_nodes})...")
                selected_nodes = selector.auto_select_components(
                    discovered_nodes,
                    max_nodes=args.max_nodes
                )
            
            if selected_nodes:
                print(f"\n✅ Selecionados {len(selected_nodes)} nodes:")
                for node in selected_nodes:
                    print(f"   • {node.name} ({node.node_type.value})")
                
                # Salvar seleção
                if args.save_selection:
                    selection_path = Path(args.save_selection)
                else:
                    selection_path = output_path.parent / "selected_nodes.json"
                
                selection_data = selector.export_selection(selected_nodes)
                selection_data['file_key'] = args.file_key
                
                with open(selection_path, 'w', encoding='utf-8') as f:
                    json.dump(selection_data, f, indent=2, ensure_ascii=False)
                
                print(f"💾 Seleção salva em: {selection_path}")
        
        print("\n🎉 Descoberta concluída com sucesso!")
        return 0
        
    except Exception as e:
        logger.error(f"Erro durante descoberta: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


def get_api_token(args) -> str:
    """Obtém token da API do Figma."""
    # 1. Argumento direto
    if args.token:
        return args.token
    
    # 2. Variável de ambiente
    token = os.getenv('FIGMA_API_TOKEN')
    if token:
        return token
    
    # 3. Arquivo .env
    if args.env_file:
        try:
            from dotenv import load_dotenv
            load_dotenv(args.env_file)
            token = os.getenv('FIGMA_API_TOKEN')
            if token:
                return token
        except ImportError:
            print("⚠️  python-dotenv não instalado. Use: pip install python-dotenv")
        except Exception as e:
            print(f"⚠️  Erro ao carregar {args.env_file}: {e}")
    
    return ""


def show_discovery_summary(nodes: List[DiscoveredNode]) -> None:
    """Mostra resumo da descoberta."""
    print("\n📊 RESUMO DA DESCOBERTA")
    print("-" * 30)
    
    # Contar por tipo
    by_type = {}
    by_complexity = {"low": 0, "medium": 0, "high": 0}
    by_level = {}
    
    for node in nodes:
        # Por tipo
        type_name = node.node_type.value
        by_type[type_name] = by_type.get(type_name, 0) + 1
        
        # Por complexidade
        by_complexity[node.estimated_complexity] += 1
        
        # Por nível
        by_level[node.level] = by_level.get(node.level, 0) + 1
    
    print("Por tipo:")
    for node_type, count in sorted(by_type.items()):
        print(f"  {node_type}: {count}")
    
    print("\nPor complexidade:")
    for complexity, count in by_complexity.items():
        icon = {"low": "🟢", "medium": "🟡", "high": "🔴"}[complexity]
        print(f"  {icon} {complexity}: {count}")
    
    print("\nPor nível:")
    for level, count in sorted(by_level.items()):
        print(f"  Nível {level}: {count}")


if __name__ == "__main__":
    sys.exit(main())
