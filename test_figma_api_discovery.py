#!/usr/bin/env python3
"""
Script para testar descoberta de nodes na API do Figma.

Este script testa diferentes estratégias para descobrir nodes em um arquivo Figma
sem baixar o arquivo completo de 50MB+.
"""

import sys
import json
import logging
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FigmaApiDiscovery:
    """Classe para descobrir nodes na API do Figma."""
    
    def __init__(self, api_token: str):
        """
        Inicializa o descobridor.
        
        Args:
            api_token: Token da API do Figma
        """
        self.api_token = api_token
        self.base_url = "https://api.figma.com/v1"
        self.headers = {
            "X-Figma-Token": api_token,
            "Content-Type": "application/json"
        }
    
    def get_file_metadata(self, file_key: str, depth: int = 1) -> Dict[str, Any]:
        """
        Obtém metadados do arquivo com profundidade limitada.
        
        Args:
            file_key: Chave do arquivo Figma
            depth: Profundidade máxima (1 = apenas páginas)
            
        Returns:
            Metadados do arquivo
        """
        url = f"{self.base_url}/files/{file_key}?depth={depth}"
        
        logger.info(f"Buscando metadados do arquivo: {file_key} (depth={depth})")
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            data = response.json()
            
            # Analisar estrutura
            logger.info(f"Nome do arquivo: {data.get('name', 'N/A')}")
            logger.info(f"Última modificação: {data.get('lastModified', 'N/A')}")
            
            if 'document' in data:
                document = data['document']
                logger.info(f"Tipo do documento: {document.get('type', 'N/A')}")
                
                if 'children' in document:
                    children = document['children']
                    logger.info(f"Número de páginas/children: {len(children)}")
                    
                    for i, child in enumerate(children):
                        logger.info(f"  Página {i+1}: {child.get('name', 'N/A')} (ID: {child.get('id', 'N/A')})")
                        
                        # Se tem children, mostrar alguns
                        if 'children' in child and child['children']:
                            logger.info(f"    Tem {len(child['children'])} elementos")
                            for j, grandchild in enumerate(child['children'][:3]):  # Apenas 3 primeiros
                                logger.info(f"      {j+1}. {grandchild.get('name', 'N/A')} (ID: {grandchild.get('id', 'N/A')}, Tipo: {grandchild.get('type', 'N/A')})")
                            
                            if len(child['children']) > 3:
                                logger.info(f"      ... e mais {len(child['children']) - 3} elementos")
            
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao buscar metadados: {e}")
            raise
    
    def discover_nodes_strategy_1(self, file_key: str) -> List[Dict[str, str]]:
        """
        Estratégia 1: Usar depth=1 para descobrir páginas, depois depth=2 para elementos principais.
        
        Args:
            file_key: Chave do arquivo Figma
            
        Returns:
            Lista de nodes descobertos
        """
        logger.info("=== ESTRATÉGIA 1: Descoberta por Profundidade ===")
        
        # Passo 1: Obter páginas (depth=1)
        metadata = self.get_file_metadata(file_key, depth=1)
        
        discovered_nodes = []
        
        if 'document' in metadata and 'children' in metadata['document']:
            pages = metadata['document']['children']
            
            for page in pages:
                page_info = {
                    'id': page.get('id', ''),
                    'name': page.get('name', ''),
                    'type': page.get('type', ''),
                    'level': 'page'
                }
                discovered_nodes.append(page_info)
                
                # Passo 2: Para cada página, obter elementos principais (depth=2)
                if page_info['id']:
                    try:
                        page_details = self.get_node_details(file_key, page_info['id'])
                        
                        if 'nodes' in page_details and page_info['id'] in page_details['nodes']:
                            node_data = page_details['nodes'][page_info['id']]
                            if 'document' in node_data and 'children' in node_data['document']:
                                for child in node_data['document']['children']:
                                    child_info = {
                                        'id': child.get('id', ''),
                                        'name': child.get('name', ''),
                                        'type': child.get('type', ''),
                                        'level': 'main_element',
                                        'parent_page': page_info['name']
                                    }
                                    discovered_nodes.append(child_info)
                    
                    except Exception as e:
                        logger.warning(f"Erro ao obter detalhes da página {page_info['name']}: {e}")
        
        logger.info(f"Descobertos {len(discovered_nodes)} nodes")
        return discovered_nodes
    
    def get_node_details(self, file_key: str, node_id: str) -> Dict[str, Any]:
        """
        Obtém detalhes de um node específico.
        
        Args:
            file_key: Chave do arquivo Figma
            node_id: ID do node
            
        Returns:
            Detalhes do node
        """
        url = f"{self.base_url}/files/{file_key}/nodes?ids={node_id}"
        
        try:
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Erro ao buscar node {node_id}: {e}")
            raise
    
    def test_node_processing(self, file_key: str, node_id: str) -> Dict[str, Any]:
        """
        Testa processamento de um node específico.
        
        Args:
            file_key: Chave do arquivo Figma
            node_id: ID do node
            
        Returns:
            Dados processados do node
        """
        logger.info(f"=== TESTANDO PROCESSAMENTO DO NODE: {node_id} ===")
        
        # Obter dados do node
        node_data = self.get_node_details(file_key, node_id)
        
        # Analisar tamanho
        json_size = len(json.dumps(node_data))
        logger.info(f"Tamanho do JSON do node: {json_size:,} bytes ({json_size/1024:.1f} KB)")
        
        # Analisar estrutura
        if 'nodes' in node_data and node_id in node_data['nodes']:
            node_info = node_data['nodes'][node_id]
            if 'document' in node_info:
                doc = node_info['document']
                logger.info(f"Nome: {doc.get('name', 'N/A')}")
                logger.info(f"Tipo: {doc.get('type', 'N/A')}")
                
                if 'children' in doc:
                    logger.info(f"Número de children: {len(doc['children'])}")
                
                # Verificar se tem propriedades de layout
                layout_props = ['layoutMode', 'primaryAxisAlignItems', 'counterAxisAlignItems']
                has_layout = any(prop in doc for prop in layout_props)
                logger.info(f"Tem propriedades de layout: {has_layout}")
                
                # Verificar se tem fills
                has_fills = 'fills' in doc and doc['fills']
                logger.info(f"Tem fills: {has_fills}")
                
                # Verificar se tem texto
                has_text = 'characters' in doc
                logger.info(f"Tem texto: {has_text}")
        
        return node_data


def main():
    """Função principal."""
    print("🔍 Figma API Discovery Tool")
    print("=" * 50)
    
    # Verificar se há token
    import os
    api_token = os.getenv('FIGMA_API_TOKEN')
    if not api_token:
        print("❌ FIGMA_API_TOKEN não encontrado nas variáveis de ambiente")
        print("   Configure com: export FIGMA_API_TOKEN=seu_token_aqui")
        return
    
    # Usar file_key do arquivo atual (extrair do JSON existente)
    figma_file = Path("data/raw/figma_api_response.json")
    if figma_file.exists():
        try:
            with open(figma_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Tentar extrair file_key dos dados
            # Normalmente está na URL ou pode ser inferido
            print("📁 Usando dados do arquivo existente para descobrir file_key...")
            
            # Se não conseguir extrair, usar um exemplo
            file_key = "exemplo_file_key"  # Substitua pelo file_key real
            
        except Exception as e:
            print(f"❌ Erro ao ler arquivo existente: {e}")
            return
    else:
        print("❌ Arquivo figma_api_response.json não encontrado")
        print("   Por favor, forneça um file_key para teste")
        return
    
    # Criar descobridor
    discovery = FigmaApiDiscovery(api_token)
    
    try:
        # Testar descoberta de metadados
        print("\n🔍 Testando descoberta de metadados...")
        metadata = discovery.get_file_metadata(file_key, depth=1)
        
        # Testar estratégia de descoberta
        print("\n🎯 Testando estratégia de descoberta...")
        nodes = discovery.discover_nodes_strategy_1(file_key)
        
        # Salvar resultados
        output_dir = Path("data/discovery")
        output_dir.mkdir(exist_ok=True)
        
        with open(output_dir / "discovered_nodes.json", 'w', encoding='utf-8') as f:
            json.dump(nodes, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Descoberta concluída!")
        print(f"   Nodes descobertos: {len(nodes)}")
        print(f"   Resultados salvos em: {output_dir / 'discovered_nodes.json'}")
        
        # Testar processamento de um node específico
        if nodes:
            test_node = nodes[0]  # Primeiro node descoberto
            print(f"\n🧪 Testando processamento do node: {test_node['name']}")
            node_data = discovery.test_node_processing(file_key, test_node['id'])
            
            with open(output_dir / f"node_{test_node['id']}.json", 'w', encoding='utf-8') as f:
                json.dump(node_data, f, indent=2, ensure_ascii=False)
        
    except Exception as e:
        print(f"❌ Erro durante descoberta: {e}")


if __name__ == "__main__":
    main()
