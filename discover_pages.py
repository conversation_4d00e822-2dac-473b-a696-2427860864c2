#!/usr/bin/env python3
"""
Script otimizado para descoberta de páginas e nodes do Figma.

Este script implementa uma abordagem em duas etapas:
1. Lista páginas disponíveis para seleção
2. Descobre nodes até depth=3 nas páginas selecionadas
"""

import sys
import json
import logging
import os
from pathlib import Path
from typing import List, Dict, Any

from src.discovery.figma_discovery import FigmaDiscovery, DiscoveredNode
from src.discovery.node_selector import NodeSelector
from src.utils.config_loader import ConfigLoader

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Função principal."""
    print("🔍 Figma Pages & Nodes Discovery")
    print("=" * 50)
    
    try:
        # Carregar configurações
        config_loader = ConfigLoader()
        figma_config = config_loader.get_figma_config()
        processing_config = config_loader.get_processing_config()
        
        file_key = figma_config['file_key']
        api_token = figma_config['token']
        max_depth = processing_config.get('max_depth', 3)
        
        print(f"📁 Arquivo: {file_key}")
        print(f"🔧 Profundidade máxima: {max_depth}")
        
        # Criar descobridor
        discovery = FigmaDiscovery(api_token)
        
        # Obter informações do arquivo
        print("\n📋 Obtendo informações do arquivo...")
        file_info = discovery.get_file_info(file_key)
        print(f"   Nome: {file_info['name']}")
        print(f"   Última modificação: {file_info['lastModified']}")
        
        # Etapa 1: Descobrir páginas
        print("\n📄 Descobrindo páginas disponíveis...")
        pages = discovery.discover_file_structure(
            file_key,
            max_depth=1,  # Apenas páginas (nível 1)
            include_components_only=False
        )
        
        # Filtrar apenas páginas (CANVAS)
        pages = [node for node in pages if node.type == 'CANVAS']
        
        if not pages:
            print("❌ Nenhuma página encontrada no arquivo")
            return 1
        
        print(f"\n📋 Páginas encontradas ({len(pages)}):")
        for i, page in enumerate(pages, 1):
            print(f"   {i}. {page.name}")
        
        # Seleção de páginas
        selected_pages = select_pages(pages)
        
        if not selected_pages:
            print("❌ Nenhuma página selecionada")
            return 1
        
        print(f"\n✅ Páginas selecionadas ({len(selected_pages)}):")
        for page in selected_pages:
            print(f"   • {page.name}")
        
        # Etapa 2: Descobrir nodes nas páginas selecionadas
        print(f"\n🔍 Descobrindo nodes nas páginas selecionadas (depth={max_depth})...")
        
        all_discovered_nodes = []
        
        for page in selected_pages:
            print(f"\n   📄 Processando página: {page.name}")
            
            # Descobrir nodes na página específica
            page_nodes = discovery.discover_page_nodes(
                file_key,
                page.id,
                max_depth=max_depth
            )
            
            print(f"      Encontrados {len(page_nodes)} nodes")
            all_discovered_nodes.extend(page_nodes)
        
        print(f"\n✅ Total de nodes descobertos: {len(all_discovered_nodes)}")
        
        # Mostrar resumo
        show_discovery_summary(all_discovered_nodes)
        
        # Salvar descoberta
        save_discovery_results(file_info, file_key, selected_pages, all_discovered_nodes, max_depth)
        
        print("\n🎉 Descoberta concluída com sucesso!")
        return 0
        
    except FileNotFoundError as e:
        print(f"❌ {e}")
        print("\n💡 Crie um arquivo project_config.yaml baseado no exemplo:")
        print("   cp project_config.example.yaml project_config.yaml")
        return 1
        
    except Exception as e:
        logger.error(f"Erro durante descoberta: {e}")
        import traceback
        traceback.print_exc()
        return 1


def select_pages(pages: List[DiscoveredNode]) -> List[DiscoveredNode]:
    """
    Permite ao usuário selecionar páginas para processamento.
    
    Args:
        pages: Lista de páginas descobertas
        
    Returns:
        Lista de páginas selecionadas
    """
    print("\n🎯 Seleção de páginas:")
    print("   Digite os números das páginas que deseja processar")
    print("   (separados por vírgula, ex: 1,3,5 ou 'all' para todas)")
    
    while True:
        try:
            selection = input("\n👉 Sua seleção: ").strip()
            
            if selection.lower() == 'all':
                return pages
            
            if not selection:
                continue
                
            # Parsear seleção
            indices = []
            for part in selection.split(','):
                part = part.strip()
                if '-' in part:
                    # Range (ex: 1-3)
                    start, end = map(int, part.split('-'))
                    indices.extend(range(start, end + 1))
                else:
                    # Número único
                    indices.append(int(part))
            
            # Validar e converter para páginas
            selected_pages = []
            for idx in indices:
                if 1 <= idx <= len(pages):
                    selected_pages.append(pages[idx - 1])
                else:
                    print(f"⚠️  Índice inválido: {idx}")
                    continue
            
            if selected_pages:
                return selected_pages
            else:
                print("❌ Nenhuma página válida selecionada")
                
        except ValueError:
            print("❌ Formato inválido. Use números separados por vírgula")
        except KeyboardInterrupt:
            print("\n❌ Operação cancelada")
            return []


def show_discovery_summary(nodes: List[DiscoveredNode]) -> None:
    """Mostra resumo da descoberta."""
    print("\n📊 RESUMO DA DESCOBERTA")
    print("-" * 30)
    
    # Contar por tipo
    by_type = {}
    by_complexity = {"low": 0, "medium": 0, "high": 0}
    by_level = {}
    
    for node in nodes:
        # Por tipo
        type_name = node.node_type.value
        by_type[type_name] = by_type.get(type_name, 0) + 1
        
        # Por complexidade
        by_complexity[node.estimated_complexity] += 1
        
        # Por nível
        by_level[node.level] = by_level.get(node.level, 0) + 1
    
    print("Por tipo:")
    for node_type, count in sorted(by_type.items()):
        print(f"  {node_type}: {count}")
    
    print("\nPor complexidade:")
    for complexity, count in by_complexity.items():
        icon = {"low": "🟢", "medium": "🟡", "high": "🔴"}[complexity]
        print(f"  {icon} {complexity}: {count}")
    
    print("\nPor nível:")
    for level, count in sorted(by_level.items()):
        print(f"  Nível {level}: {count}")


def save_discovery_results(file_info: Dict[str, Any], file_key: str, 
                          selected_pages: List[DiscoveredNode], 
                          discovered_nodes: List[DiscoveredNode], 
                          max_depth: int) -> None:
    """Salva os resultados da descoberta."""
    
    # Preparar dados
    discovery_data = {
        'file_info': file_info,
        'discovery_params': {
            'file_key': file_key,
            'max_depth': max_depth,
            'selected_pages': [{'id': p.id, 'name': p.name} for p in selected_pages]
        },
        'discovered_nodes': [
            {
                'id': node.id,
                'name': node.name,
                'type': node.type,
                'node_type': node.node_type.value,
                'level': node.level,
                'parent_id': node.parent_id,
                'parent_name': node.parent_name,
                'children_count': node.children_count,
                'has_layout': node.has_layout,
                'has_content': node.has_content,
                'estimated_complexity': node.estimated_complexity,
                'description': node.description
            }
            for node in discovered_nodes
        ]
    }
    
    # Salvar descoberta
    output_path = Path('data/discovery/discovered_nodes.json')
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(discovery_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Descoberta salva em: {output_path}")


if __name__ == "__main__":
    sys.exit(main())
