#!/usr/bin/env python3
"""
Script de teste para o novo sistema de conversão aprimorado.

Este script testa o novo sistema de conversão baseado no MCP server
e compara os resultados com o sistema antigo.
"""

import sys
import json
import logging
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from converters.figma_converter import FigmaConverter
from generators.enhanced_html_generator import EnhancedHTMLGenerator

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_enhanced_conversion():
    """Testa o novo sistema de conversão."""
    logger.info("=== TESTE DO SISTEMA DE CONVERSÃO APRIMORADO ===")
    
    # Carregar dados do Figma
    figma_file = Path("data/raw/figma_api_response.json")
    if not figma_file.exists():
        logger.error(f"Arquivo não encontrado: {figma_file}")
        return False
    
    try:
        # Carregar dados JSON
        with open(figma_file, 'r', encoding='utf-8') as f:
            figma_data = json.load(f)
        
        logger.info(f"Dados carregados: {figma_file}")
        
        # Testar o novo conversor
        logger.info("Testando novo conversor...")
        converter = FigmaConverter()
        simplified_design = converter.parse_figma_response(figma_data)
        
        # Exibir estatísticas
        logger.info("=== ESTATÍSTICAS DO NOVO SISTEMA ===")
        logger.info(f"Nome do projeto: {simplified_design.name}")
        logger.info(f"Nodes processados: {len(simplified_design.nodes)}")
        logger.info(f"Components: {len(simplified_design.components)}")
        logger.info(f"Component sets: {len(simplified_design.component_sets)}")
        logger.info(f"Design tokens criados: {len(simplified_design.global_vars.styles)}")
        
        # Analisar tipos de design tokens
        token_types = {}
        for token_id in simplified_design.global_vars.styles.keys():
            prefix = token_id.split('_')[0]
            token_types[prefix] = token_types.get(prefix, 0) + 1
        
        logger.info("Design tokens por tipo:")
        for token_type, count in token_types.items():
            logger.info(f"  {token_type}: {count}")
        
        # Testar gerador HTML aprimorado
        logger.info("Testando gerador HTML aprimorado...")
        html_generator = EnhancedHTMLGenerator(simplified_design)
        html_content = html_generator.generate_complete_html()
        
        # Salvar resultado
        output_dir = Path("data/output")
        output_dir.mkdir(exist_ok=True)
        
        output_file = output_dir / "test_enhanced_output.html"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"HTML gerado salvo em: {output_file}")
        logger.info(f"Tamanho do HTML: {len(html_content)} caracteres")
        
        # Analisar qualidade do HTML
        analyze_html_quality(html_content)
        
        return True
        
    except Exception as e:
        logger.error(f"Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_html_quality(html_content: str):
    """Analisa a qualidade do HTML gerado."""
    logger.info("=== ANÁLISE DE QUALIDADE DO HTML ===")
    
    # Contar elementos
    element_counts = {
        'divs': html_content.count('<div'),
        'spans': html_content.count('<span'),
        'buttons': html_content.count('<button'),
        'inputs': html_content.count('<input'),
        'headers': html_content.count('<h1') + html_content.count('<h2') + html_content.count('<h3'),
        'semantic_elements': (
            html_content.count('<nav') + html_content.count('<header') + 
            html_content.count('<main') + html_content.count('<section') + 
            html_content.count('<footer')
        )
    }
    
    logger.info("Elementos HTML encontrados:")
    for element, count in element_counts.items():
        logger.info(f"  {element}: {count}")
    
    # Verificar CSS
    css_start = html_content.find('<style>')
    css_end = html_content.find('</style>')
    
    if css_start != -1 and css_end != -1:
        css_content = html_content[css_start:css_end]
        
        css_features = {
            'design_tokens': css_content.count('--'),
            'flexbox_usage': css_content.count('display: flex'),
            'grid_usage': css_content.count('display: grid'),
            'custom_properties': css_content.count('var('),
            'box_shadows': css_content.count('box-shadow'),
            'border_radius': css_content.count('border-radius')
        }
        
        logger.info("Recursos CSS encontrados:")
        for feature, count in css_features.items():
            logger.info(f"  {feature}: {count}")
    
    # Verificar estrutura semântica
    semantic_score = calculate_semantic_score(html_content)
    logger.info(f"Score semântico: {semantic_score}/10")


def calculate_semantic_score(html_content: str) -> int:
    """Calcula um score de qualidade semântica do HTML."""
    score = 0
    
    # Pontos por uso de elementos semânticos
    if '<nav' in html_content:
        score += 1
    if '<header' in html_content:
        score += 1
    if '<main' in html_content:
        score += 1
    if '<section' in html_content:
        score += 1
    if '<footer' in html_content:
        score += 1
    
    # Pontos por estrutura de headings
    if '<h1' in html_content or '<h2' in html_content:
        score += 1
    
    # Pontos por uso de botões apropriados
    if '<button' in html_content:
        score += 1
    
    # Pontos por uso de CSS moderno
    if 'display: flex' in html_content:
        score += 1
    if '--' in html_content:  # CSS custom properties
        score += 1
    
    # Penalizar uso excessivo de divs
    div_count = html_content.count('<div')
    total_elements = (
        html_content.count('<div') + html_content.count('<span') + 
        html_content.count('<button') + html_content.count('<nav') +
        html_content.count('<header') + html_content.count('<main')
    )
    
    if total_elements > 0:
        div_ratio = div_count / total_elements
        if div_ratio < 0.7:  # Menos de 70% divs é bom
            score += 1
    
    return min(score, 10)


def compare_with_legacy():
    """Compara resultados com o sistema legado."""
    logger.info("=== COMPARAÇÃO COM SISTEMA LEGADO ===")
    
    # Verificar se existe saída do sistema legado
    legacy_file = Path("data/output/index.html")
    enhanced_file = Path("data/output/test_enhanced_output.html")
    
    if not legacy_file.exists():
        logger.warning("Arquivo do sistema legado não encontrado")
        return
    
    if not enhanced_file.exists():
        logger.warning("Arquivo do sistema aprimorado não encontrado")
        return
    
    try:
        with open(legacy_file, 'r', encoding='utf-8') as f:
            legacy_content = f.read()
        
        with open(enhanced_file, 'r', encoding='utf-8') as f:
            enhanced_content = f.read()
        
        logger.info("Comparação de tamanhos:")
        logger.info(f"  Sistema legado: {len(legacy_content)} caracteres")
        logger.info(f"  Sistema aprimorado: {len(enhanced_content)} caracteres")
        
        # Comparar scores semânticos
        legacy_score = calculate_semantic_score(legacy_content)
        enhanced_score = calculate_semantic_score(enhanced_content)
        
        logger.info("Comparação de qualidade semântica:")
        logger.info(f"  Sistema legado: {legacy_score}/10")
        logger.info(f"  Sistema aprimorado: {enhanced_score}/10")
        
        if enhanced_score > legacy_score:
            logger.info("✅ Sistema aprimorado tem melhor qualidade semântica")
        elif enhanced_score == legacy_score:
            logger.info("➖ Qualidade semântica equivalente")
        else:
            logger.info("❌ Sistema legado tem melhor qualidade semântica")
        
    except Exception as e:
        logger.error(f"Erro na comparação: {e}")


if __name__ == "__main__":
    print("🚀 Testando Sistema de Conversão Aprimorado")
    print("=" * 50)
    
    success = test_enhanced_conversion()
    
    if success:
        print("\n✅ Teste concluído com sucesso!")
        compare_with_legacy()
    else:
        print("\n❌ Teste falhou!")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("Teste finalizado. Verifique os logs para detalhes.")
