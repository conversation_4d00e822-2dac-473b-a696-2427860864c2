#!/usr/bin/env python3
"""
Exemplo de uso do sistema Figma to Code Converter.

Este exemplo demonstra como usar o sistema programaticamente
para processar designs do Figma e gerar código.
"""

import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from parsers.figma_api_client import FigmaApiClient, FigmaApiConfig
from parsers.figma_parser import FigmaParser
from extractors.component_extractor import ComponentExtractor
from generators.html_generator import HTMLGenerator
from generators.css_generator import CSSGenerator
from storage.data_storage import DataStorage


def example_api_usage():
    """Exemplo de uso com a API do Figma."""
    print("=== Exemplo: Uso da API do Figma ===")
    
    # Configurar cliente da API
    token = "seu_token_aqui"  # Substitua pelo seu token
    config = FigmaApiConfig(token=token)
    client = FigmaApiClient(config)
    
    # Baixar dados de um arquivo específico
    file_key = "EWVeXlNAwQlwOsej7XstyO"
    node_ids = ["1:15527"]
    
    try:
        # Baixar nodes específicos
        data = client.get_file_nodes(file_key, node_ids)
        
        # Salvar dados brutos
        client.save_data_to_file(data, "example_figma_data.json")
        
        print("✅ Dados baixados da API com sucesso!")
        return data
        
    except Exception as e:
        print(f"❌ Erro ao baixar dados da API: {e}")
        return None


def example_file_processing():
    """Exemplo de processamento de arquivo local."""
    print("\n=== Exemplo: Processamento de Arquivo Local ===")
    
    # Caminho para o arquivo JSON do Figma
    file_path = "../data/raw/figma_api_response.json"
    
    if not Path(file_path).exists():
        print(f"❌ Arquivo não encontrado: {file_path}")
        return None
    
    try:
        # Carregar e processar arquivo
        parser = FigmaParser()
        project = parser.load_file(file_path)
        
        # Exibir resumo
        summary = parser.get_project_summary()
        print(f"✅ Projeto processado: {summary['project_name']}")
        print(f"   Total de nodes: {summary['total_nodes']}")
        print(f"   Tipos de nodes: {len(summary['node_types'])}")
        
        return project
        
    except Exception as e:
        print(f"❌ Erro ao processar arquivo: {e}")
        return None


def example_component_extraction(project):
    """Exemplo de extração de componentes."""
    print("\n=== Exemplo: Extração de Componentes ===")
    
    if not project:
        print("❌ Projeto não disponível para extração")
        return None
    
    try:
        # Extrair componentes
        extractor = ComponentExtractor(project)
        components = extractor.extract_all_components()
        
        # Exibir estatísticas
        stats = extractor.get_extraction_summary()
        print(f"✅ Componentes extraídos: {stats['total_components']}")
        
        # Mostrar tipos de componentes
        print("   Tipos encontrados:")
        for comp_type, count in stats['components_by_type'].items():
            print(f"     {comp_type}: {count}")
        
        return components
        
    except Exception as e:
        print(f"❌ Erro na extração de componentes: {e}")
        return None


def example_html_generation(components_data):
    """Exemplo de geração de HTML."""
    print("\n=== Exemplo: Geração de HTML ===")
    
    if not components_data:
        print("❌ Dados de componentes não disponíveis")
        return
    
    try:
        # Gerar HTML
        generator = HTMLGenerator(components_data)
        html_content = generator.generate_html()
        
        # Salvar arquivo
        output_path = Path("example_output.html")
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # Exibir estatísticas
        stats = generator.get_generation_stats()
        print(f"✅ HTML gerado: {output_path}")
        print(f"   Componentes com CSS: {stats['components_with_css']}")
        print(f"   Componentes com conteúdo: {stats['components_with_content']}")
        print(f"   Tags HTML usadas: {len(stats['html_tags_used'])}")
        
    except Exception as e:
        print(f"❌ Erro na geração de HTML: {e}")


def example_css_generation(components_data):
    """Exemplo de geração de CSS avançado."""
    print("\n=== Exemplo: Geração de CSS Avançado ===")
    
    if not components_data:
        print("❌ Dados de componentes não disponíveis")
        return
    
    try:
        # Gerar CSS
        css_generator = CSSGenerator(components_data)
        
        # Extrair design tokens
        tokens = css_generator.extract_design_tokens()
        print(f"✅ Design tokens extraídos:")
        for category, items in tokens.items():
            if items:
                print(f"   {category}: {len(items)} itens")
        
        # Gerar CSS completo
        css_content = css_generator.generate_complete_css()
        
        # Salvar arquivo
        output_path = Path("example_styles.css")
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(css_content)
        
        # Exibir estatísticas
        stats = css_generator.get_css_stats()
        print(f"✅ CSS gerado: {output_path}")
        print(f"   Total de linhas: {stats['total_lines']}")
        print(f"   Total de regras: {stats['total_rules']}")
        print(f"   Variáveis CSS: {stats['css_variables']}")
        
    except Exception as e:
        print(f"❌ Erro na geração de CSS: {e}")


def example_data_storage(components_data):
    """Exemplo de armazenamento de dados."""
    print("\n=== Exemplo: Armazenamento de Dados ===")
    
    if not components_data:
        print("❌ Dados de componentes não disponíveis")
        return
    
    try:
        # Criar sistema de storage
        storage = DataStorage("example_data")
        
        # Salvar em diferentes formatos
        storage.save_components_json(components_data, "example_components.json")
        storage.save_components_parquet(components_data, "example_components.parquet")
        storage.save_components_csv(components_data, "example_components.csv")
        
        # Exibir resumo do storage
        summary = storage.get_storage_summary()
        print(f"✅ Dados salvos em {len(summary['processed_files'])} arquivos")
        print(f"   Tamanho total: {summary['total_size_mb']} MB")
        
        # Análise dos componentes
        analysis = storage.analyze_components("example_components.json")
        print(f"   Análise: {analysis.get('total_components', 0)} componentes analisados")
        
    except Exception as e:
        print(f"❌ Erro no armazenamento: {e}")


def main():
    """Função principal do exemplo."""
    print("🚀 Figma to Code Converter - Exemplos de Uso")
    print("=" * 50)
    
    # Exemplo 1: Processar arquivo local
    project = example_file_processing()
    
    if project:
        # Exemplo 2: Extrair componentes
        components = example_component_extraction(project)
        
        if components:
            # Preparar dados para os geradores
            parser = FigmaParser()
            parser.project = project
            project_summary = parser.get_project_summary()
            
            extractor = ComponentExtractor(project)
            extraction_summary = extractor.get_extraction_summary()
            
            components_data = {
                'project_summary': project_summary,
                'extraction_summary': extraction_summary,
                'components': {
                    comp_id: {
                        'id': comp.id,
                        'name': comp.name,
                        'type': comp.type.value,
                        'original_type': comp.original_type,
                        'html_tag': comp.html_tag,
                        'css_classes': comp.css_classes,
                        'css_properties': comp.css_properties,
                        'content': comp.content,
                        'properties': comp.properties,
                        'parent_id': comp.parent_id,
                        'children_ids': [child.id for child in comp.children]
                    }
                    for comp_id, comp in components.items()
                }
            }
            
            # Exemplo 3: Gerar HTML
            example_html_generation(components_data)
            
            # Exemplo 4: Gerar CSS avançado
            example_css_generation(components_data)
            
            # Exemplo 5: Armazenar dados
            example_data_storage(components_data)
    
    print("\n" + "=" * 50)
    print("✅ Exemplos concluídos! Verifique os arquivos gerados.")
    
    # Exemplo 6: Uso da API (comentado por precisar de token)
    # print("\n💡 Para usar a API do Figma, descomente e configure o token:")
    # print("   example_api_usage()")


if __name__ == "__main__":
    main()
