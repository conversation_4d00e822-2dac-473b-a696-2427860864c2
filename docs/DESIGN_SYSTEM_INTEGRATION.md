# Integração com Design System

Este documento descreve a estratégia para integração do projeto com documentação do Design System (Storybook) e APIs de IA para geração de componentes.

## Visão Geral

O objetivo é conectar os dados descobertos do Figma com a documentação do Design System para gerar componentes React/Angular que seguem os padrões estabelecidos.

## Fluxo de Integração

```mermaid
graph TD
    A[Figma Discovery] --> B[Structured Storage]
    B --> C[Design System Loader]
    C --> D[AI Component Generator]
    D --> E[React/Angular Components]
    
    F[Storybook Documentation] --> C
    G[Design System API] --> C
```

## Componentes da Integração

### 1. Design System Loader

Responsável por carregar e indexar a documentação do Design System:

- **Fonte**: Páginas de documentação do Storybook
- **Formato**: HTML, Markdown, JSON
- **Indexação**: Por tipo de componente, propriedades, exemplos

#### Opções de Implementação:

**Opção A: API de Busca**
- Endpoint para buscar componentes por tipo/nome
- Retorna documentação, exemplos e código
- Vantagem: Flexível, tempo real
- Desvantagem: Requer infraestrutura de API

**Opção B: Base de Dados Local**
- Script para extrair e armazenar documentação
- SQLite/Parquet com índices otimizados
- Vantagem: Rápido, offline
- Desvantagem: Requer sincronização manual

### 2. AI Component Generator

Utiliza APIs de IA para gerar componentes baseados em:

- **Contexto do Figma**: Dados descobertos e estruturados
- **Design System**: Documentação e padrões
- **Framework**: React ou Angular

#### Prompt Strategy:

```
Contexto:
- Elemento Figma: {figma_element_data}
- Design System: {design_system_docs}
- Framework: {target_framework}

Tarefa:
Gerar um componente {framework} que:
1. Reproduza a estrutura visual do Figma
2. Use componentes do Design System
3. Siga as convenções de nomenclatura
4. Inclua propriedades configuráveis
```

### 3. Component Mapping

Sistema para mapear elementos do Figma para componentes do Design System:

```yaml
mappings:
  figma_button:
    design_system: "Button"
    props_mapping:
      text: "children"
      variant: "variant"
      size: "size"
  
  figma_input:
    design_system: "TextField"
    props_mapping:
      placeholder: "placeholder"
      label: "label"
```

## Implementação Sugerida

### Fase 1: Design System Loader

1. **Scraper do Storybook**
   ```python
   class StorybookScraper:
       def extract_components(self, storybook_url: str) -> Dict[str, Any]
       def parse_component_docs(self, component_html: str) -> ComponentDoc
       def store_in_database(self, components: List[ComponentDoc]) -> None
   ```

2. **Base de Dados de Componentes**
   ```sql
   CREATE TABLE design_system_components (
       id TEXT PRIMARY KEY,
       name TEXT,
       category TEXT,
       description TEXT,
       props_schema TEXT,
       examples TEXT,
       code_samples TEXT
   );
   ```

### Fase 2: AI Integration

1. **Component Generator**
   ```python
   class AIComponentGenerator:
       def generate_component(
           self, 
           figma_element: DiscoveredNode,
           design_system_context: Dict[str, Any],
           framework: str = "react"
       ) -> GeneratedComponent
   ```

2. **Template System**
   - Templates base para React/Angular
   - Substituição de variáveis
   - Validação de código gerado

### Fase 3: Integration Pipeline

1. **Workflow Completo**
   ```python
   def generate_page_components(project_id: str) -> List[GeneratedComponent]:
       # 1. Carregar dados do Figma
       storage = StructuredStorage(project_id)
       components_df = storage.get_components_for_generation()
       
       # 2. Carregar Design System
       ds_loader = DesignSystemLoader()
       ds_context = ds_loader.get_context()
       
       # 3. Gerar componentes
       generator = AIComponentGenerator()
       generated = []
       
       for _, component in components_df.iterrows():
           generated_comp = generator.generate_component(
               component, ds_context, "react"
           )
           generated.append(generated_comp)
       
       return generated
   ```

## Configuração

### Design System Config

```yaml
design_system:
  type: "storybook"  # storybook, api, local
  source: "https://storybook.company.com"
  components_endpoint: "/api/components"
  
  # Para scraping local
  local_path: "design_system_docs/"
  
  # Mapeamentos personalizados
  mappings_file: "design_system_mappings.yaml"

ai:
  provider: "openai"  # openai, anthropic, local
  model: "gpt-4"
  max_tokens: 2000
  temperature: 0.1
```

## Próximos Passos

1. **Implementar Design System Loader**
   - Escolher entre API ou scraping
   - Criar estrutura de dados
   - Implementar indexação

2. **Configurar AI Integration**
   - Definir prompts otimizados
   - Implementar geração de componentes
   - Criar sistema de validação

3. **Desenvolver Templates**
   - Templates React/Angular
   - Sistema de substituição
   - Validação de código

4. **Testes e Refinamento**
   - Testar com componentes reais
   - Refinar prompts e mapeamentos
   - Otimizar performance

## Considerações Técnicas

### Performance
- Cache de documentação do Design System
- Processamento em lote de componentes
- Otimização de chamadas de IA

### Qualidade
- Validação de código gerado
- Testes automatizados
- Review manual de componentes críticos

### Manutenibilidade
- Versionamento de templates
- Logs detalhados de geração
- Rollback de componentes problemáticos
