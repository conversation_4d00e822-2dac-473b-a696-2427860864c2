# Exemplo de configuração de projeto
# Copie este arquivo para project_config.yaml e preencha com seus dados

# Configurações do projeto Figma
figma:
  file_key: ""  # Chave do seu arquivo Figma (figma.com/design/{FILE_KEY})
  token: ""  # Seu token da API Figma
  
# Configurações de processamento
processing:
  max_depth: 3  # Profundidade máxima para descoberta de nodes
  components_only: false  # Se deve processar apenas componentes
  
# Configurações de saída
output:
  base_dir: "data/output"  # Diretório base para saída
  format: "html"  # Formato de saída (html, json, etc.)
