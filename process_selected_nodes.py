#!/usr/bin/env python3
"""
Script para processar nodes selecionados do Figma.

Este script processa nodes específicos descobertos anteriormente,
integrando com o sistema atual de conversão.
"""

import sys
import json
import logging
import argparse
import os
import re
from pathlib import Path
from typing import List, Dict, Any

from src.parsers.figma_api_client import FigmaApiClient, FigmaApiConfig
from src.parsers.figma_parser import FigmaParser
from src.extractors.component_extractor import ComponentExtractor
from src.generators.html_generator import HTMLGenerator
from src.storage.data_storage import DataStorage

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def sanitize_filename(filename: str) -> str:
    """
    Sanitiza nome de arquivo removendo caracteres especiais.

    Args:
        filename: Nome original do arquivo

    Returns:
        Nome sanitizado
    """
    # Remover extensão se existir
    name, ext = os.path.splitext(filename)

    # Substituir caracteres especiais por underscore ou remover
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', name)  # Caracteres inválidos no Windows
    sanitized = re.sub(r'[,\s]+', '_', sanitized)   # Vírgulas e espaços
    sanitized = re.sub(r'_+', '_', sanitized)       # Múltiplos underscores
    sanitized = sanitized.strip('_')                # Remove underscores das bordas

    # Garantir que não está vazio
    if not sanitized:
        sanitized = 'component'

    # Limitar tamanho
    if len(sanitized) > 50:
        sanitized = sanitized[:50]

    return sanitized + (ext if ext else '')


class SelectedNodeProcessor:
    """
    Processador de nodes selecionados.
    
    Integra a descoberta de nodes com o sistema atual de conversão,
    processando apenas nodes específicos ao invés do arquivo completo.
    """
    
    def __init__(self, api_token: str):
        """
        Inicializa o processador.
        
        Args:
            api_token: Token da API do Figma
        """
        self.api_token = api_token
        self.config = FigmaApiConfig(token=api_token)
        self.client = FigmaApiClient(self.config)
        self.parser = FigmaParser()
    
    def process_selected_nodes(
        self,
        file_key: str,
        selected_nodes: List[Dict[str, Any]],
        output_dir: str = "data/processed"
    ) -> Dict[str, Any]:
        """
        Processa nodes selecionados.
        
        Args:
            file_key: Chave do arquivo Figma
            selected_nodes: Lista de nodes selecionados
            output_dir: Diretório de saída
            
        Returns:
            Dados processados
        """
        logger.info(f"Processando {len(selected_nodes)} nodes selecionados")
        
        # Extrair IDs dos nodes
        node_ids = [node['id'] for node in selected_nodes]
        
        # Baixar dados dos nodes específicos
        logger.info("Baixando dados dos nodes da API...")
        node_data = self.client.get_file_nodes(file_key, node_ids)
        
        # Salvar dados brutos
        raw_path = Path(output_dir) / "raw"
        raw_path.mkdir(parents=True, exist_ok=True)
        
        raw_file = raw_path / f"selected_nodes_{file_key}.json"
        self.client.save_data_to_file(node_data, str(raw_file))
        logger.info(f"Dados brutos salvos em: {raw_file}")
        
        # Processar com o parser atual
        logger.info("Processando dados com parser...")

        # Criar estrutura compatível com o parser atual
        compatible_data = self._create_compatible_structure(node_data, selected_nodes)

        # Processar com parser
        project = self.parser.parse_data(compatible_data)

        # Extrair componentes
        logger.info("Extraindo componentes...")
        extractor = ComponentExtractor(project)
        extracted_components = extractor.extract_all_components()

        # Converter para formato compatível
        components = {
            'components': {
                comp_id: {
                    'id': comp.id,
                    'name': comp.name,
                    'type': comp.type.value,
                    'original_type': comp.original_type,
                    'properties': comp.properties,
                    'html_tag': comp.html_tag,
                    'css_classes': comp.css_classes,
                    'css_properties': comp.css_properties,
                    'content': comp.content
                }
                for comp_id, comp in extracted_components.items()
            }
        }

        # Salvar componentes processados
        processed_path = Path(output_dir) / "processed"
        processed_path.mkdir(parents=True, exist_ok=True)

        # Criar DataStorage com o output_dir correto
        storage = DataStorage(output_dir)
        filename = f"components_{file_key}.json"
        components_file = storage.save_components_json(components, filename)
        logger.info(f"Componentes salvos em: {components_file}")

        # Gerar HTML
        logger.info("Gerando HTML...")
        html_generator = HTMLGenerator(components)
        html_results = []

        total_components = len(components.get('components', {}))
        logger.info(f"Gerando HTML para {total_components} componentes...")

        # Gerar CSS uma vez para todos os componentes
        try:
            logger.info("Gerando CSS global...")
            global_css = html_generator.generate_css()
            logger.info(f"CSS global gerado: {len(global_css)} caracteres")
        except Exception as e:
            logger.error(f"Erro ao gerar CSS global: {e}")
            global_css = "/* Erro na geração de CSS */"

        for i, (component_id, component_data) in enumerate(components.get('components', {}).items(), 1):
            try:
                logger.info(f"Processando componente {i}/{total_components}: {component_id}")

                # Sanitizar nome do componente para uso como nome de arquivo
                raw_name = component_data.get('name', f'Component_{component_id}')
                safe_name = sanitize_filename(raw_name)

                logger.info(f"Nome sanitizado: '{raw_name}' -> '{safe_name}'")

                # Gerar HTML simples para o componente (sem CSS repetido)
                html_content = self._generate_simple_component_html(
                    component_id, component_data, global_css
                )

                html_results.append({
                    'component_id': component_id,
                    'component_name': safe_name,
                    'original_name': raw_name,
                    'html_content': html_content,
                    'node_info': self._find_node_info(component_id, selected_nodes)
                })

                logger.info(f"HTML gerado com sucesso para: {safe_name}")

            except Exception as e:
                logger.error(f"Erro ao gerar HTML para componente {component_id} ({raw_name}): {e}")
                # Continuar com próximo componente em caso de erro
        
        # Salvar HTML
        output_path = Path(output_dir) / "output"
        output_path.mkdir(parents=True, exist_ok=True)
        
        for result in html_results:
            try:
                safe_filename = f"{result['component_name']}.html"
                html_file = output_path / safe_filename

                # Verificar se o nome do arquivo é válido
                if not safe_filename or safe_filename == '.html':
                    safe_filename = f"component_{result['component_id']}.html"
                    html_file = output_path / safe_filename
                    logger.warning(f"Nome de arquivo inválido, usando: {safe_filename}")

                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(result['html_content'])

                logger.info(f"HTML salvo: {html_file}")

            except Exception as e:
                logger.error(f"Erro ao salvar HTML para {result.get('original_name', 'componente')}: {e}")
                # Tentar com nome alternativo
                try:
                    fallback_name = f"component_{result['component_id']}.html"
                    fallback_file = output_path / fallback_name
                    with open(fallback_file, 'w', encoding='utf-8') as f:
                        f.write(result['html_content'])
                    logger.info(f"HTML salvo com nome alternativo: {fallback_file}")
                except Exception as e2:
                    logger.error(f"Falha completa ao salvar HTML: {e2}")
        
        # Criar relatório
        report = {
            'file_key': file_key,
            'processed_nodes': len(selected_nodes),
            'generated_components': len(html_results),
            'files': {
                'raw_data': str(raw_file),
                'processed_components': str(components_file),
                'html_files': [str(output_path / f"{r['component_name']}.html") for r in html_results]
            },
            'results': html_results
        }
        
        # Salvar relatório
        report_file = Path(output_dir) / f"processing_report_{file_key}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Relatório salvo em: {report_file}")
        
        return report
    
    def _create_compatible_structure(
        self,
        node_data: Dict[str, Any],
        selected_nodes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Cria estrutura compatível com o parser atual.
        
        Args:
            node_data: Dados dos nodes da API
            selected_nodes: Informações dos nodes selecionados
            
        Returns:
            Estrutura compatível
        """
        # Criar estrutura básica de documento
        compatible = {
            'name': f"Selected Nodes ({len(selected_nodes)} nodes)",
            'lastModified': node_data.get('lastModified', ''),
            'thumbnailUrl': '',
            'version': node_data.get('version', ''),
            'role': 'viewer',
            'editorType': 'figma',
            'linkAccess': 'view',
            'document': {
                'id': 'root',
                'name': 'Document',
                'type': 'DOCUMENT',
                'children': []
            },
            'components': node_data.get('components', {}),
            'componentSets': node_data.get('componentSets', {}),
            'schemaVersion': node_data.get('schemaVersion', 0),
            'styles': node_data.get('styles', {})
        }
        
        # Adicionar nodes como páginas/children
        if 'nodes' in node_data:
            for node_id, node_info in node_data['nodes'].items():
                if 'document' in node_info:
                    # Encontrar informações do node selecionado
                    selected_info = self._find_node_info(node_id, selected_nodes)
                    
                    # Adicionar como child do documento
                    child_node = node_info['document'].copy()
                    child_node['selected_info'] = selected_info
                    
                    compatible['document']['children'].append(child_node)
        
        return compatible
    
    def _find_node_info(self, node_id: str, selected_nodes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Encontra informações de um node na lista de selecionados."""
        for node in selected_nodes:
            if node.get('id') == node_id:
                return node
        return {}

    def _generate_simple_component_html(
        self,
        component_id: str,
        component_data: Dict[str, Any],
        global_css: str
    ) -> str:
        """
        Gera HTML simples para um componente sem recursão complexa.

        Args:
            component_id: ID do componente
            component_data: Dados do componente
            global_css: CSS global

        Returns:
            HTML completo do componente
        """
        # Dados básicos do componente
        name = component_data.get('name', 'Component')
        html_tag = component_data.get('html_tag', 'div')
        css_classes = component_data.get('css_classes', [])
        content = component_data.get('content', '')

        # Gerar classes CSS
        class_attr = f' class="{" ".join(css_classes)}"' if css_classes else ''

        # Gerar conteúdo HTML simples
        if content:
            component_html = f'<{html_tag}{class_attr}>{content}</{html_tag}>'
        else:
            component_html = f'<{html_tag}{class_attr}><!-- {name} --></{html_tag}>'

        # Template HTML completo
        html_template = f"""<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{name}</title>
    <style>
{global_css}
    </style>
</head>
<body>
    {component_html}
</body>
</html>"""

        return html_template
    
    def generate_comparison_report(
        self,
        processing_report: Dict[str, Any],
        output_file: str = "data/reports/comparison_report.html"
    ) -> str:
        """
        Gera relatório de comparação com imagens.
        
        Args:
            processing_report: Relatório de processamento
            output_file: Arquivo de saída do relatório
            
        Returns:
            Caminho do arquivo gerado
        """
        logger.info("Gerando relatório de comparação...")
        
        html_content = """
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Comparação - Figma to Code</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .component { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 8px; }
        .component h3 { margin-top: 0; color: #333; }
        .comparison { display: flex; gap: 20px; margin: 20px 0; }
        .figma-design, .generated-html { flex: 1; }
        .figma-design img { max-width: 100%; border: 1px solid #ccc; }
        .generated-html iframe { width: 100%; height: 300px; border: 1px solid #ccc; }
        .code-preview { background: #f8f8f8; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto; }
        .stats { background: #e8f4fd; padding: 15px; border-radius: 8px; margin: 20px 0; }
        .node-info { background: #fff3cd; padding: 10px; border-radius: 4px; margin: 10px 0; font-size: 14px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Relatório de Comparação - Figma to Code</h1>
        <p><strong>Arquivo:</strong> {file_key}</p>
        <p><strong>Nodes processados:</strong> {processed_nodes}</p>
        <p><strong>Componentes gerados:</strong> {generated_components}</p>
        <p><strong>Data:</strong> {timestamp}</p>
    </div>
    
    <div class="stats">
        <h2>📊 Estatísticas</h2>
        <ul>
            <li>Taxa de sucesso: {success_rate}%</li>
            <li>Componentes com layout: {layout_count}</li>
            <li>Componentes com conteúdo: {content_count}</li>
        </ul>
    </div>
    
    {components_html}
    
    <div class="footer">
        <p><em>Relatório gerado automaticamente pelo sistema Figma to Code</em></p>
    </div>
</body>
</html>
        """.strip()
        
        # Gerar HTML dos componentes
        components_html = ""
        success_count = 0
        layout_count = 0
        content_count = 0
        
        for result in processing_report.get('results', []):
            node_info = result.get('node_info', {})
            
            if result.get('html_content'):
                success_count += 1
            
            if node_info.get('has_layout'):
                layout_count += 1
            
            if node_info.get('has_content'):
                content_count += 1
            
            component_html = f"""
    <div class="component">
        <h3>🧩 {result['component_name']}</h3>
        
        <div class="node-info">
            <strong>Tipo:</strong> {node_info.get('node_type', 'N/A')} | 
            <strong>Complexidade:</strong> {node_info.get('estimated_complexity', 'N/A')} | 
            <strong>Filhos:</strong> {node_info.get('children_count', 0)} | 
            <strong>Layout:</strong> {'Sim' if node_info.get('has_layout') else 'Não'} | 
            <strong>Conteúdo:</strong> {'Sim' if node_info.get('has_content') else 'Não'}
            <br><strong>Descrição:</strong> {node_info.get('description', 'N/A')}
        </div>
        
        <div class="comparison">
            <div class="figma-design">
                <h4>🎨 Design Original (Figma)</h4>
                <p><em>Imagem não disponível - use Figma API para obter preview</em></p>
            </div>
            
            <div class="generated-html">
                <h4>🔧 HTML Gerado</h4>
                <div class="code-preview">{result.get('html_content', 'Erro na geração')[:500]}...</div>
            </div>
        </div>
    </div>
            """.strip()
            
            components_html += component_html
        
        # Calcular estatísticas
        total_components = len(processing_report.get('results', []))
        success_rate = (success_count / total_components * 100) if total_components > 0 else 0
        
        # Substituir placeholders
        from datetime import datetime
        final_html = html_content.format(
            file_key=processing_report['file_key'],
            processed_nodes=processing_report['processed_nodes'],
            generated_components=processing_report['generated_components'],
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            success_rate=f"{success_rate:.1f}",
            layout_count=layout_count,
            content_count=content_count,
            components_html=components_html
        )
        
        # Salvar arquivo
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(final_html)
        
        logger.info(f"Relatório de comparação salvo em: {output_path}")
        return str(output_path)


def main():
    """Função principal."""
    parser = argparse.ArgumentParser(
        description="Processamento de nodes selecionados do Figma"
    )
    
    parser.add_argument(
        '--file-key',
        required=True,
        help='Chave do arquivo Figma'
    )
    
    parser.add_argument(
        '--selection-file',
        required=True,
        help='Arquivo JSON com nodes selecionados'
    )
    
    parser.add_argument(
        '--token',
        help='Token da API do Figma (ou use FIGMA_API_TOKEN)'
    )
    
    parser.add_argument(
        '--output-dir',
        default='data/processed',
        help='Diretório de saída (padrão: data/processed)'
    )
    
    parser.add_argument(
        '--generate-report',
        action='store_true',
        help='Gerar relatório de comparação HTML'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Saída detalhada'
    )
    
    args = parser.parse_args()
    
    # Configurar logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Obter token
    api_token = args.token or os.getenv('FIGMA_API_TOKEN')
    if not api_token:
        print("❌ Token da API do Figma não encontrado")
        print("   Use --token ou FIGMA_API_TOKEN")
        return 1
    
    # Carregar seleção
    try:
        with open(args.selection_file, 'r', encoding='utf-8') as f:
            selection_data = json.load(f)
        
        selected_nodes = selection_data.get('nodes', [])
        if not selected_nodes:
            print("❌ Nenhum node encontrado no arquivo de seleção")
            return 1
        
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo de seleção: {e}")
        return 1
    
    print("🔧 Processamento de Nodes Selecionados")
    print("=" * 50)
    print(f"📁 Arquivo: {args.file_key}")
    print(f"🎯 Nodes selecionados: {len(selected_nodes)}")
    
    try:
        # Processar nodes
        processor = SelectedNodeProcessor(api_token)
        report = processor.process_selected_nodes(
            args.file_key,
            selected_nodes,
            args.output_dir
        )
        
        print(f"\n✅ Processamento concluído!")
        print(f"   Componentes gerados: {report['generated_components']}")
        
        # Gerar relatório de comparação
        if args.generate_report:
            report_file = processor.generate_comparison_report(report)
            print(f"📊 Relatório de comparação: {report_file}")
        
        return 0
        
    except Exception as e:
        logger.error(f"Erro durante processamento: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
