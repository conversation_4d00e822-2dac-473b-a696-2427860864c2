#!/usr/bin/env python3
"""
Teste específico da geração de HTML com IDs sanitizados.
"""

from pathlib import Path

from src.generators.html_generator import HTMLGenerator
from src.storage.data_storage import DataStorage

def test_html_generation():
    """Testa a geração de HTML com IDs sanitizados."""
    
    print("=== TESTE DE GERAÇÃO HTML ===")
    
    # Carregar dados processados
    storage = DataStorage()
    try:
        components_data = storage.load_components_json("components.json")
        print(f"✅ Dados carregados: {len(components_data.get('components', {}))} componentes")
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {e}")
        return
    
    # Criar gerador
    generator = HTMLGenerator(components_data)
    
    # Testar alguns IDs específicos
    test_components = list(components_data['components'].items())[:3]
    
    print("\n=== TESTE DE IDs SANITIZADOS ===")
    for comp_id, comp_data in test_components:
        original_id = comp_data.get('id', '')
        sanitized_id = generator._sanitize_css_id(original_id)
        
        print(f"Componente: {comp_data.get('name', 'Sem nome')}")
        print(f"  ID Original: {original_id}")
        print(f"  ID Sanitizado: {sanitized_id}")
        print(f"  Válido: {is_valid_css_id(sanitized_id)}")
        print()
    
    # Gerar HTML
    print("=== GERANDO HTML ===")
    try:
        html_content = generator.generate_html()
        
        # Salvar em arquivo de teste
        test_file = Path("test_output.html")
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ HTML gerado: {len(html_content)} caracteres")
        print(f"✅ Salvo em: {test_file}")
        
        # Verificar se os IDs no HTML estão sanitizados
        print("\n=== VERIFICANDO IDs NO HTML ===")
        check_html_ids(html_content)
        
    except Exception as e:
        print(f"❌ Erro na geração: {e}")
        import traceback
        traceback.print_exc()

def check_html_ids(html_content):
    """Verifica se os IDs no HTML estão sanitizados."""
    import re
    
    # Encontrar todos os IDs no HTML
    id_pattern = r'id="([^"]*)"'
    ids_found = re.findall(id_pattern, html_content)
    
    print(f"Total de IDs encontrados: {len(ids_found)}")
    
    # Verificar alguns IDs
    problematic_ids = []
    valid_ids = []
    
    for html_id in ids_found[:10]:  # Verificar apenas os primeiros 10
        if is_valid_css_id(html_id):
            valid_ids.append(html_id)
        else:
            problematic_ids.append(html_id)
    
    print(f"IDs válidos (amostra): {len(valid_ids)}")
    for valid_id in valid_ids[:5]:
        print(f"  ✅ {valid_id}")
    
    if problematic_ids:
        print(f"IDs problemáticos: {len(problematic_ids)}")
        for prob_id in problematic_ids[:5]:
            print(f"  ❌ {prob_id}")
    else:
        print("✅ Todos os IDs verificados são válidos!")

def is_valid_css_id(css_id):
    """Verifica se um ID CSS é válido."""
    import re
    
    # ID CSS deve começar com letra ou underscore
    # e conter apenas letras, números, hífens e underscores
    pattern = r'^[a-zA-Z_][a-zA-Z0-9_-]*$'
    return bool(re.match(pattern, css_id))

if __name__ == "__main__":
    test_html_generation()
