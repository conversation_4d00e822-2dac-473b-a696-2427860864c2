#!/usr/bin/env python3
"""
Teste da função de sanitização de IDs CSS.
"""

import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from generators.html_generator import HTMLGenerator

def test_sanitization():
    """Testa a função de sanitização."""
    
    # Dados de teste
    test_data = {
        'components': {
            '1:15527': {'id': '1:15527', 'name': 'Box filtros'},
            'I20:12642;12258:2410;2051:95': {'id': 'I20:12642;12258:2410;2051:95', 'name': 'component-search-path'},
            '20:12637': {'id': '20:12637', 'name': 'Label'}
        }
    }
    
    generator = HTMLGenerator(test_data)
    
    # Testar IDs problemáticos
    test_ids = [
        '1:15527',
        'I20:12642;12258:2410;2051:95',
        '20:12637'
    ]
    
    print("=== TESTE DE SANITIZAÇÃO DE IDs ===")
    for test_id in test_ids:
        sanitized = generator._sanitize_css_id(test_id)
        print(f"Original: {test_id}")
        print(f"Sanitizado: {sanitized}")
        print(f"Válido CSS: {is_valid_css_id(sanitized)}")
        print("-" * 40)

def is_valid_css_id(css_id):
    """Verifica se um ID CSS é válido."""
    import re
    
    # ID CSS deve começar com letra ou underscore
    # e conter apenas letras, números, hífens e underscores
    pattern = r'^[a-zA-Z_][a-zA-Z0-9_-]*$'
    return bool(re.match(pattern, css_id))

if __name__ == "__main__":
    test_sanitization()
