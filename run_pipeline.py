#!/usr/bin/env python3
"""
Pipeline completo para conversão Figma to Code.

Este script executa todo o pipeline de conversão:
1. Download/carregamento dos dados do Figma
2. Parsing e extração de componentes
3. Geração de código HTML/CSS
4. Salvamento em múltiplos formatos
"""

import argparse
import logging
import os
import sys
from pathlib import Path
import json
from typing import Dict, Any

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from parsers.figma_api_client import FigmaApiClient, FigmaApiConfig
from parsers.figma_parser import FigmaParser
from extractors.component_extractor import ComponentExtractor
from generators.html_generator import HTMLGenerator
from generators.css_generator import CSSGenerator
from storage.data_storage import DataStorage
from utils.design_system_mapper import DesignSystemMapper

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class FigmaToCodePipeline:
    """Pipeline completo para conversão Figma to Code."""
    
    def __init__(self, config: Dict[str, Any]):
        """
        Inicializa o pipeline.
        
        Args:
            config: Configuração do pipeline
        """
        self.config = config
        self.storage = DataStorage(config.get('data_path', 'data'))
        self.project = None
        self.components = None
        self.components_data = None
        
    def run_complete_pipeline(self) -> bool:
        """
        Executa o pipeline completo.
        
        Returns:
            True se bem-sucedido, False caso contrário
        """
        try:
            logger.info("=== INICIANDO PIPELINE FIGMA TO CODE ===")
            
            # Etapa 1: Obter dados do Figma
            if not self._load_figma_data():
                return False
            
            # Etapa 2: Processar e extrair componentes
            if not self._process_components():
                return False
            
            # Etapa 3: Salvar dados processados
            if not self._save_processed_data():
                return False
            
            # Etapa 4: Gerar código
            if not self._generate_code():
                return False
            
            # Etapa 5: Relatório final
            self._generate_report()
            
            logger.info("=== PIPELINE CONCLUÍDO COM SUCESSO ===")
            return True
            
        except Exception as e:
            logger.error(f"Erro no pipeline: {e}")
            return False
    
    def _load_figma_data(self) -> bool:
        """Carrega dados do Figma (API ou arquivo local)."""
        logger.info("Etapa 1: Carregando dados do Figma...")
        
        if self.config.get('use_api', False):
            return self._download_from_api()
        else:
            return self._load_from_file()
    
    def _download_from_api(self) -> bool:
        """Baixa dados da API do Figma."""
        token = self.config.get('figma_token') or os.getenv('FIGMA_TOKEN')
        if not token:
            logger.error("Token do Figma não fornecido")
            return False
        
        file_key = self.config.get('file_key')
        if not file_key:
            logger.error("File key não fornecido")
            return False
        
        try:
            # Extrair file_key da URL se necessário
            if file_key.startswith('http'):
                file_key = FigmaApiClient.extract_file_key_from_url(file_key)
            
            # Criar cliente e baixar dados
            api_config = FigmaApiConfig(token=token)
            client = FigmaApiClient(api_config)
            
            if self.config.get('node_ids'):
                data = client.get_file_nodes(file_key, self.config['node_ids'])
                filename = f"figma_nodes_{file_key}.json"
            else:
                data = client.get_file(file_key)
                filename = f"figma_file_{file_key}.json"
            
            # Salvar dados brutos
            raw_path = self.storage.base_path / "raw" / filename
            raw_path.parent.mkdir(parents=True, exist_ok=True)
            client.save_data_to_file(data, str(raw_path))
            
            # Processar dados
            parser = FigmaParser()
            self.project = parser.parse_data(data)
            
            logger.info(f"Dados baixados e processados: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao baixar dados da API: {e}")
            return False
    
    def _load_from_file(self) -> bool:
        """Carrega dados de arquivo local."""
        input_file = self.config.get('input_file')
        if not input_file or not Path(input_file).exists():
            logger.error(f"Arquivo não encontrado: {input_file}")
            return False
        
        try:
            parser = FigmaParser()
            self.project = parser.load_file(input_file)
            
            logger.info(f"Dados carregados do arquivo: {input_file}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao carregar arquivo: {e}")
            return False
    
    def _process_components(self) -> bool:
        """Processa e extrai componentes usando o novo sistema de conversão."""
        logger.info("Etapa 2: Processando componentes (NOVO SISTEMA)...")

        try:
            # Tentar usar o novo sistema de conversão
            raw_data = None

            if hasattr(self, 'input_file') and self.input_file:
                # Carregar dados brutos do arquivo
                with open(self.input_file, 'r', encoding='utf-8') as f:
                    import json
                    raw_data = json.load(f)
            elif hasattr(self, 'project') and self.project:
                # Reconstruir dados brutos do projeto
                raw_data = {
                    'name': self.project.name,
                    'lastModified': self.project.last_modified,
                    'version': self.project.version,
                    'thumbnailUrl': self.project.thumbnail_url,
                    'nodes': self.project.nodes
                }

            if raw_data:
                # Usar o novo sistema de conversão
                parser = FigmaParser()
                self.simplified_design = parser.parse_with_new_converter(raw_data)

                if self.simplified_design and self.simplified_design.nodes:
                    # Criar dados de compatibilidade
                    self._create_enhanced_compatibility_data()
                    logger.info(f"Nodes processados (novo sistema): {len(self.simplified_design.nodes)}")
                    logger.info(f"Design tokens criados: {len(self.simplified_design.global_vars.styles)}")
                    return True
                else:
                    logger.warning("Novo sistema não produziu resultados, usando fallback...")

            # Fallback para o sistema antigo
            return self._process_components_legacy()

        except Exception as e:
            logger.error(f"Erro no novo sistema: {e}")
            logger.info("Usando fallback para o sistema antigo...")
            return self._process_components_legacy()

    def _create_enhanced_compatibility_data(self) -> None:
        """Cria dados de compatibilidade com o sistema antigo."""
        self.components_data = {
            'project_summary': {
                'project_name': self.simplified_design.name,
                'total_nodes': len(self.simplified_design.nodes),
                'total_components': len(self.simplified_design.components),
                'total_component_sets': len(self.simplified_design.component_sets),
                'design_tokens': len(self.simplified_design.global_vars.styles)
            },
            'extraction_summary': {
                'total_components': len(self.simplified_design.nodes),
                'design_tokens_created': len(self.simplified_design.global_vars.styles),
                'conversion_method': 'enhanced_mcp_based'
            }
            # Não incluir simplified_design aqui para evitar problemas de serialização
        }

    def _process_components_legacy(self) -> bool:
        """Fallback para o sistema antigo de processamento."""
        try:
            # Extrair componentes
            extractor = ComponentExtractor(self.project)
            self.components = extractor.extract_all_components()

            # Preparar dados para salvamento
            components_data = {}
            for comp_id, comp in self.components.items():
                components_data[comp_id] = {
                    'id': comp.id,
                    'name': comp.name,
                    'type': comp.type.value,
                    'original_type': comp.original_type,
                    'html_tag': comp.html_tag,
                    'css_classes': comp.css_classes,
                    'css_properties': comp.css_properties,
                    'content': comp.content,
                    'properties': comp.properties,
                    'parent_id': comp.parent_id,
                    'children_ids': [child.id for child in comp.children]
                }

            # Obter resumos
            parser = FigmaParser()
            parser.project = self.project
            project_summary = parser.get_project_summary()
            extraction_summary = extractor.get_extraction_summary()

            self.components_data = {
                'project_summary': project_summary,
                'extraction_summary': extraction_summary,
                'components': components_data
            }

            # Criar mapeamentos para design system
            mapper = DesignSystemMapper(self.components_data)
            mappings = mapper.create_mappings()
            design_system_report = mapper.generate_design_system_report()

            # Adicionar mapeamentos aos dados
            self.components_data['design_system_mappings'] = mapper.export_mappings()
            self.components_data['design_system_report'] = design_system_report

            logger.info(f"Componentes processados (sistema legado): {len(self.components)}")
            return True

        except Exception as e:
            logger.error(f"Erro ao processar componentes (sistema legado): {e}")
            return False
    
    def _save_processed_data(self) -> bool:
        """Salva dados processados em múltiplos formatos."""
        logger.info("Etapa 3: Salvando dados processados...")

        try:
            # Verificar se temos o novo sistema
            if hasattr(self, 'simplified_design') and self.simplified_design:
                # Converter SimplifiedDesign para formato serializável
                serializable_data = self._convert_simplified_design_to_dict()

                # Salvar dados do novo sistema
                import json
                from pathlib import Path

                output_file = Path("data/processed/simplified_design.json")
                output_file.parent.mkdir(parents=True, exist_ok=True)

                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(serializable_data, f, indent=2, ensure_ascii=False)

                logger.info(f"Simplified design salvo em: {output_file}")

            # Salvar dados de compatibilidade
            if hasattr(self, 'components_data') and self.components_data:
                # Salvar em JSON
                self.storage.save_components_json(self.components_data)

                # Salvar em Parquet (apenas se temos componentes no formato antigo)
                if 'components' in self.components_data:
                    if self.config.get('save_parquet', True):
                        self.storage.save_components_parquet(self.components_data)

                    if self.config.get('save_csv', True):
                        self.storage.save_components_csv(self.components_data)

            logger.info("Dados processados salvos em múltiplos formatos")
            return True

        except Exception as e:
            logger.error(f"Erro ao salvar dados processados: {e}")
            return False

    def _convert_simplified_design_to_dict(self) -> dict:
        """Converte SimplifiedDesign para dict serializável."""
        import dataclasses

        def convert_dataclass(obj):
            if dataclasses.is_dataclass(obj):
                return dataclasses.asdict(obj)
            elif isinstance(obj, list):
                return [convert_dataclass(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: convert_dataclass(value) for key, value in obj.items()}
            else:
                return obj

        return convert_dataclass(self.simplified_design)
    
    def _generate_code(self) -> bool:
        """Gera código HTML/CSS usando o sistema apropriado."""
        logger.info("Etapa 4: Gerando código...")

        try:
            # Verificar se temos o novo sistema de conversão
            if hasattr(self, 'simplified_design') and self.simplified_design:
                return self._generate_code_enhanced()
            else:
                return self._generate_code_legacy()

        except Exception as e:
            logger.error(f"Erro ao gerar código: {e}")
            return False

    def _generate_code_enhanced(self) -> bool:
        """Gera código usando o novo sistema aprimorado."""
        try:
            from generators.enhanced_html_generator import EnhancedHTMLGenerator

            # Gerar HTML com o novo sistema
            enhanced_generator = EnhancedHTMLGenerator(self.simplified_design)
            html_content = enhanced_generator.generate_complete_html()

            # Salvar arquivo HTML
            self.storage.save_generated_code(html_content, "index_enhanced.html", "html")

            # Gerar CSS separado (extrair do HTML)
            css_start = html_content.find('<style>') + 7
            css_end = html_content.find('</style>')
            if css_start > 6 and css_end > css_start:
                css_content = html_content[css_start:css_end].strip()
                self.storage.save_generated_code(css_content, "styles_enhanced.css", "css")

                # Criar versão com CSS externo
                html_with_external_css = html_content.replace(
                    html_content[css_start-7:css_end+8],
                    '<link rel="stylesheet" href="styles_enhanced.css">'
                )
                self.storage.save_generated_code(html_with_external_css, "index_enhanced_external.html", "html")

            logger.info("Código HTML/CSS gerado com sucesso (sistema aprimorado)")
            return True

        except Exception as e:
            logger.error(f"Erro no sistema aprimorado: {e}")
            logger.info("Tentando fallback para sistema legado...")
            return self._generate_code_legacy()

    def _generate_code_legacy(self) -> bool:
        """Gera código usando o sistema legado."""
        try:
            # Gerar HTML
            html_generator = HTMLGenerator(self.components_data)
            html_content = html_generator.generate_html()

            # Gerar CSS avançado
            css_generator = CSSGenerator(self.components_data)
            css_content = css_generator.generate_complete_css()

            # Salvar arquivos
            self.storage.save_generated_code(html_content, "index.html", "html")
            self.storage.save_generated_code(css_content, "styles.css", "css")

            # Gerar versão com CSS separado
            html_with_external_css = html_content.replace(
                '<style>\n' + html_generator.generate_css() + '\n    </style>',
                '<link rel="stylesheet" href="styles.css">'
            )
            self.storage.save_generated_code(html_with_external_css, "index_external_css.html", "html")

            logger.info("Código HTML/CSS gerado com sucesso (sistema legado)")
            return True

        except Exception as e:
            logger.error(f"Erro no sistema legado: {e}")
            return False
    
    def _generate_report(self) -> None:
        """Gera relatório final do pipeline."""
        logger.info("Etapa 5: Gerando relatório...")
        
        # Estatísticas do storage
        storage_summary = self.storage.get_storage_summary()
        
        # Estatísticas dos geradores
        html_generator = HTMLGenerator(self.components_data)
        html_stats = html_generator.get_generation_stats()
        
        css_generator = CSSGenerator(self.components_data)
        css_stats = css_generator.get_css_stats()
        
        # Análise de componentes
        component_analysis = self.storage.analyze_components()
        
        # Montar relatório
        report = {
            'pipeline_config': self.config,
            'project_summary': self.components_data['project_summary'],
            'extraction_summary': self.components_data['extraction_summary'],
            'html_generation_stats': html_stats,
            'css_generation_stats': css_stats,
            'component_analysis': component_analysis,
            'storage_summary': storage_summary
        }
        
        # Salvar relatório
        report_path = self.storage.base_path / "reports" / "pipeline_report.json"
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        # Exibir resumo no console
        print("\n" + "="*60)
        print("RELATÓRIO FINAL DO PIPELINE")
        print("="*60)
        
        print(f"Projeto: {self.components_data['project_summary']['project_name']}")
        print(f"Total de componentes: {self.components_data['extraction_summary']['total_components']}")
        print(f"Arquivos gerados: {len(storage_summary['output_files'])}")
        print(f"Tamanho total: {storage_summary['total_size_mb']} MB")
        
        print(f"\nRelatório completo salvo em: {report_path}")


def main():
    """Função principal."""
    parser = argparse.ArgumentParser(
        description="Pipeline completo Figma to Code",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    # Configurações de entrada
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument('--file', help='Arquivo JSON local do Figma')
    input_group.add_argument('--api', action='store_true', help='Usar API do Figma')
    
    # Configurações da API
    parser.add_argument('--file-key', help='Chave do arquivo ou URL do Figma')
    parser.add_argument('--token', help='Token da API do Figma')
    parser.add_argument('--node-ids', help='IDs dos nodes (separados por vírgula)')
    
    # Configurações de saída
    parser.add_argument('--output-dir', default='data', help='Diretório de saída')
    parser.add_argument('--no-parquet', action='store_true', help='Não salvar em Parquet')
    parser.add_argument('--no-csv', action='store_true', help='Não salvar em CSV')
    
    args = parser.parse_args()
    
    # Montar configuração
    config = {
        'data_path': args.output_dir,
        'save_parquet': not args.no_parquet,
        'save_csv': not args.no_csv
    }
    
    if args.api:
        config.update({
            'use_api': True,
            'file_key': args.file_key,
            'figma_token': args.token,
            'node_ids': args.node_ids.split(',') if args.node_ids else None
        })
    else:
        config.update({
            'use_api': False,
            'input_file': args.file
        })
    
    # Executar pipeline
    pipeline = FigmaToCodePipeline(config)
    success = pipeline.run_complete_pipeline()
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
