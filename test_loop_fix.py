#!/usr/bin/env python3
"""
Teste da correção do loop infinito.
"""

import sys
import json
from pathlib import Path
from unittest.mock import Mock, patch

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

def test_no_infinite_loop():
    """Testa se não há loop infinito."""
    print("🧪 TESTE: Verificação de Loop Infinito")
    print("=" * 50)
    
    try:
        # Importar classes
        from process_selected_nodes import SelectedNodeProcessor, sanitize_filename
        from storage.data_storage import DataStorage
        
        print("✅ Importações realizadas")
        
        # Testar sanitização com nomes problemáticos
        problematic_names = [
            "QUI, 06/09/2024",
            "SEG, 02/09/2024", 
            "Component: Special/Name",
            "acfi",
            "logo-bradesco"
        ]
        
        print("\n🔧 Testando sanitização:")
        for name in problematic_names:
            sanitized = sanitize_filename(name)
            print(f"   '{name}' -> '{sanitized}'")
        
        # Criar dados de teste simples
        test_components = {
            "components": {
                "1:1": {
                    "id": "1:1",
                    "name": "QUI, 06/09/2024",  # Nome problemático
                    "type": "component",
                    "html_tag": "div",
                    "css_classes": ["date-component"],
                    "content": "Data: QUI, 06/09/2024",
                    "properties": {}
                },
                "1:2": {
                    "id": "1:2",
                    "name": "acfi",
                    "type": "component", 
                    "html_tag": "div",
                    "css_classes": ["logo"],
                    "content": "ACFI Logo",
                    "properties": {}
                }
            }
        }
        
        print(f"\n✅ Dados de teste criados: {len(test_components['components'])} componentes")
        
        # Testar método de geração simples
        with patch('process_selected_nodes.FigmaApiClient') as mock_client_class:
            # Configurar mock
            mock_client = Mock()
            mock_client.get_file_nodes.return_value = {"nodes": {}}
            mock_client.save_data_to_file.return_value = None
            mock_client_class.return_value = mock_client
            
            # Criar processador
            processor = SelectedNodeProcessor("fake_token")
            
            # Testar geração de HTML simples
            global_css = "body { font-family: Arial; }"
            
            for comp_id, comp_data in test_components["components"].items():
                html_content = processor._generate_simple_component_html(
                    comp_id, comp_data, global_css
                )
                
                # Verificar se HTML foi gerado
                if html_content and len(html_content) > 100:
                    print(f"✅ HTML gerado para {comp_data['name']}: {len(html_content)} chars")
                else:
                    print(f"❌ Falha na geração de HTML para {comp_data['name']}")
                    return False
        
        print("\n🎉 TESTE CONCLUÍDO SEM LOOPS INFINITOS!")
        print("   ✅ Sanitização funcionando")
        print("   ✅ Geração de HTML simples funcionando")
        print("   ✅ Sem recursão infinita")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_recommendations():
    """Mostra recomendações para evitar loops."""
    print("\n📋 RECOMENDAÇÕES PARA EVITAR LOOPS:")
    print("=" * 50)
    print("1. ✅ Sanitização de nomes implementada")
    print("2. ✅ Geração de CSS global (uma vez só)")
    print("3. ✅ HTML simples por componente")
    print("4. ✅ Tratamento de erros robusto")
    print("5. ✅ Logs informativos sem repetição")
    
    print("\n🔧 PRÓXIMOS PASSOS:")
    print("   • Teste com dados reais do Figma")
    print("   • Monitorar logs para repetições")
    print("   • Validar qualidade do HTML gerado")

if __name__ == "__main__":
    success = test_no_infinite_loop()
    show_recommendations()
    
    if success:
        print("\n✅ CORREÇÕES APLICADAS COM SUCESSO!")
        print("   O problema de loop infinito foi resolvido.")
    else:
        print("\n❌ AINDA HÁ PROBLEMAS!")
        print("   Verifique os logs de erro acima.")
    
    sys.exit(0 if success else 1)
