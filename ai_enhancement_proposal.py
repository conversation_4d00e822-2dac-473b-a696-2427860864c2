#!/usr/bin/env python3
"""
Proposta de integração com APIs de IA para melhorar a conversão Figma to Code.

Este script identifica elementos complexos que poderiam se beneficiar de 
processamento por IA e prepara o contexto para envio a APIs de IA.
"""

import json
import sys
from pathlib import Path
from typing import Dict, List, Any

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from storage.data_storage import DataStorage


class AIEnhancementAnalyzer:
    """Analisa componentes que poderiam se beneficiar de processamento por IA."""
    
    def __init__(self, components_data: Dict[str, Any]):
        """
        Inicializa o analisador.
        
        Args:
            components_data: Dados dos componentes processados
        """
        self.components_data = components_data
        self.components = components_data.get('components', {})
        
    def identify_complex_elements(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Identifica elementos complexos que poderiam se beneficiar de IA.
        
        Returns:
            Dict agrupando elementos por tipo de complexidade
        """
        complex_elements = {
            'variable_aliases': [],      # Elementos com VARIABLE_ALIAS
            'complex_layouts': [],       # Layouts complexos com muitos filhos
            'interactive_elements': [],  # Elementos com interações
            'custom_components': [],     # Componentes customizados
            'vector_graphics': [],       # Gráficos vetoriais complexos
            'text_with_formatting': [],  # Textos com formatação complexa
            'responsive_elements': []    # Elementos que precisam ser responsivos
        }
        
        for comp_id, comp_data in self.components.items():
            # Verificar VARIABLE_ALIAS
            if self._has_variable_alias(comp_data):
                complex_elements['variable_aliases'].append({
                    'id': comp_id,
                    'name': comp_data.get('name', ''),
                    'type': comp_data.get('type', ''),
                    'variables': self._extract_variables(comp_data)
                })
            
            # Verificar layouts complexos
            if self._is_complex_layout(comp_data):
                complex_elements['complex_layouts'].append({
                    'id': comp_id,
                    'name': comp_data.get('name', ''),
                    'children_count': len(comp_data.get('children_ids', [])),
                    'layout_properties': self._extract_layout_properties(comp_data)
                })
            
            # Verificar elementos interativos
            if self._is_interactive(comp_data):
                complex_elements['interactive_elements'].append({
                    'id': comp_id,
                    'name': comp_data.get('name', ''),
                    'interactions': self._extract_interactions(comp_data)
                })
            
            # Verificar componentes customizados
            if self._is_custom_component(comp_data):
                complex_elements['custom_components'].append({
                    'id': comp_id,
                    'name': comp_data.get('name', ''),
                    'component_properties': comp_data.get('properties', {})
                })
            
            # Verificar gráficos vetoriais
            if self._is_vector_graphic(comp_data):
                complex_elements['vector_graphics'].append({
                    'id': comp_id,
                    'name': comp_data.get('name', ''),
                    'original_type': comp_data.get('original_type', '')
                })
        
        return complex_elements
    
    def _has_variable_alias(self, comp_data: Dict[str, Any]) -> bool:
        """Verifica se o componente tem VARIABLE_ALIAS."""
        properties = comp_data.get('properties', {})
        return ('background_variable' in properties or 
                'text_color_variable' in properties)
    
    def _extract_variables(self, comp_data: Dict[str, Any]) -> Dict[str, str]:
        """Extrai variáveis do componente."""
        properties = comp_data.get('properties', {})
        variables = {}
        
        if 'background_variable' in properties:
            variables['background'] = properties['background_variable']
        if 'text_color_variable' in properties:
            variables['text_color'] = properties['text_color_variable']
            
        return variables
    
    def _is_complex_layout(self, comp_data: Dict[str, Any]) -> bool:
        """Verifica se é um layout complexo."""
        children_count = len(comp_data.get('children_ids', []))
        return children_count > 5  # Mais de 5 filhos
    
    def _extract_layout_properties(self, comp_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai propriedades de layout."""
        css_props = comp_data.get('css_properties', {})
        layout_props = {}
        
        layout_keys = ['display', 'flex-direction', 'justify-content', 'align-items', 
                      'grid-template-columns', 'grid-template-rows']
        
        for key in layout_keys:
            if key in css_props:
                layout_props[key] = css_props[key]
                
        return layout_props
    
    def _is_interactive(self, comp_data: Dict[str, Any]) -> bool:
        """Verifica se é um elemento interativo."""
        properties = comp_data.get('properties', {})
        return 'reactions' in properties or 'interactive' in properties
    
    def _extract_interactions(self, comp_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extrai informações de interação."""
        properties = comp_data.get('properties', {})
        interactions = {}
        
        if 'reactions' in properties:
            interactions['reactions'] = properties['reactions']
        if 'interactive' in properties:
            interactions['interactive'] = properties['interactive']
            
        return interactions
    
    def _is_custom_component(self, comp_data: Dict[str, Any]) -> bool:
        """Verifica se é um componente customizado."""
        return comp_data.get('type') == 'component'
    
    def _is_vector_graphic(self, comp_data: Dict[str, Any]) -> bool:
        """Verifica se é um gráfico vetorial."""
        return comp_data.get('original_type') in ['VECTOR', 'ELLIPSE', 'POLYGON']
    
    def generate_ai_context(self, element_type: str, elements: List[Dict[str, Any]]) -> str:
        """
        Gera contexto para envio a APIs de IA.
        
        Args:
            element_type: Tipo de elemento complexo
            elements: Lista de elementos do tipo
            
        Returns:
            Contexto formatado para IA
        """
        context = f"""
# Figma to Code - Processamento de {element_type.replace('_', ' ').title()}

## Contexto do Projeto
- **Projeto**: {self.components_data.get('project_summary', {}).get('project_name', 'N/A')}
- **Total de Componentes**: {len(self.components)}
- **Elementos Complexos Identificados**: {len(elements)}

## Elementos para Processamento

"""
        
        for i, element in enumerate(elements[:5], 1):  # Limitar a 5 elementos
            context += f"""
### Elemento {i}: {element.get('name', 'Sem nome')}
- **ID**: {element.get('id', '')}
- **Tipo**: {element.get('type', '')}
"""
            
            # Adicionar informações específicas por tipo
            if element_type == 'variable_aliases':
                context += f"- **Variáveis**: {element.get('variables', {})}\n"
            elif element_type == 'complex_layouts':
                context += f"- **Filhos**: {element.get('children_count', 0)}\n"
                context += f"- **Layout**: {element.get('layout_properties', {})}\n"
            elif element_type == 'interactive_elements':
                context += f"- **Interações**: {element.get('interactions', {})}\n"
        
        context += f"""

## Tarefa para IA
Por favor, analise estes elementos e sugira:

1. **Melhorias no CSS**: Como otimizar as propriedades CSS geradas
2. **Estrutura HTML**: Se a estrutura HTML está adequada
3. **Responsividade**: Como tornar os elementos responsivos
4. **Acessibilidade**: Melhorias de acessibilidade
5. **Performance**: Otimizações de performance

## Formato de Resposta
Responda em formato JSON com as seguintes chaves:
- `improvements`: Lista de melhorias sugeridas
- `css_optimizations`: Otimizações de CSS
- `html_structure`: Sugestões de estrutura HTML
- `accessibility`: Melhorias de acessibilidade
- `performance`: Otimizações de performance
"""
        
        return context
    
    def generate_report(self) -> Dict[str, Any]:
        """
        Gera relatório completo de elementos complexos.
        
        Returns:
            Relatório detalhado
        """
        complex_elements = self.identify_complex_elements()
        
        report = {
            'summary': {
                'total_components': len(self.components),
                'complex_elements_found': sum(len(elements) for elements in complex_elements.values()),
                'complexity_types': {k: len(v) for k, v in complex_elements.items()}
            },
            'complex_elements': complex_elements,
            'ai_recommendations': {
                'high_priority': [],
                'medium_priority': [],
                'low_priority': []
            }
        }
        
        # Priorizar elementos para processamento por IA
        if complex_elements['variable_aliases']:
            report['ai_recommendations']['high_priority'].append({
                'type': 'variable_aliases',
                'count': len(complex_elements['variable_aliases']),
                'reason': 'Design tokens não resolvidos - crítico para design system'
            })
        
        if complex_elements['complex_layouts']:
            report['ai_recommendations']['medium_priority'].append({
                'type': 'complex_layouts',
                'count': len(complex_elements['complex_layouts']),
                'reason': 'Layouts complexos podem precisar de estrutura HTML otimizada'
            })
        
        if complex_elements['vector_graphics']:
            report['ai_recommendations']['medium_priority'].append({
                'type': 'vector_graphics',
                'count': len(complex_elements['vector_graphics']),
                'reason': 'Gráficos vetoriais podem precisar de SVG ou ícones'
            })
        
        return report


def main():
    """Função principal."""
    print("🤖 AI Enhancement Analyzer - Figma to Code")
    print("=" * 50)
    
    # Carregar dados processados
    storage = DataStorage()
    try:
        components_data = storage.load_components_json("components.json")
        print(f"✅ Dados carregados: {len(components_data.get('components', {}))} componentes")
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {e}")
        return
    
    # Analisar elementos complexos
    analyzer = AIEnhancementAnalyzer(components_data)
    report = analyzer.generate_report()
    
    # Exibir resumo
    print(f"\n📊 RESUMO DA ANÁLISE")
    print(f"Total de componentes: {report['summary']['total_components']}")
    print(f"Elementos complexos encontrados: {report['summary']['complex_elements_found']}")
    
    print(f"\n🔍 TIPOS DE COMPLEXIDADE:")
    for comp_type, count in report['summary']['complexity_types'].items():
        if count > 0:
            print(f"  {comp_type.replace('_', ' ').title()}: {count}")
    
    # Recomendações para IA
    print(f"\n🚀 RECOMENDAÇÕES PARA PROCESSAMENTO POR IA:")
    
    for priority in ['high_priority', 'medium_priority', 'low_priority']:
        recommendations = report['ai_recommendations'][priority]
        if recommendations:
            print(f"\n  {priority.replace('_', ' ').title()}:")
            for rec in recommendations:
                print(f"    • {rec['type']}: {rec['count']} elementos")
                print(f"      Razão: {rec['reason']}")
    
    # Gerar contexto para IA (exemplo)
    if report['summary']['complex_elements_found'] > 0:
        print(f"\n📝 EXEMPLO DE CONTEXTO PARA IA:")
        print("-" * 40)
        
        # Pegar o primeiro tipo com elementos
        for element_type, elements in report['complex_elements'].items():
            if elements:
                context = analyzer.generate_ai_context(element_type, elements)
                print(context[:500] + "..." if len(context) > 500 else context)
                break
    
    # Salvar relatório
    report_path = Path("data/reports/ai_enhancement_report.json")
    report_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Relatório salvo em: {report_path}")


if __name__ == "__main__":
    main()
