# Figma to Code Converter

Um sistema inteligente para converter designs do Figma em componentes de código reutilizáveis com integração a Design Systems.

## Visão Geral

Este projeto implementa uma abordagem otimizada para extrair e converter elementos de design do Figma em componentes de código, focando em:

- **Descoberta inteligente**: Identifica automaticamente páginas e componentes relevantes
- **Processamento eficiente**: Evita downloads desnecessários usando a API do Figma
- **Armazenamento estruturado**: Dados organizados em Parquet e SQLite para análise
- **Integração com Design System**: Conecta com documentação do Storybook
- **Geração com IA**: Usa APIs de IA para gerar componentes React/Angular

## Estrutura do Projeto

```
design_to_code/
├── src/
│   ├── discovery/          # Descoberta de nodes do Figma
│   ├── extractors/         # Extração de dados dos nodes
│   ├── parsers/           # Análise e processamento de dados
│   ├── converters/        # Conversão para diferentes formatos
│   ├── generators/        # Geração de código final
│   ├── storage/           # Armazenamento estruturado (Parquet/SQLite)
│   ├── design_system/     # Integração com Design Systems
│   └── utils/             # Utilitários (config loader, etc.)
├── data/
│   ├── discovery/         # Dados de descoberta (JSON)
│   ├── output/           # Dados estruturados por projeto
│   │   └── {project_id}/  # Dados específicos do projeto
│   │       ├── parquet/   # Arquivos Parquet
│   │       └── sqlite/    # Banco SQLite
│   └── reports/          # Relatórios de processamento
├── docs/                 # Documentação detalhada
└── tests/               # Testes automatizados
```

## Características Principais

### 🔍 Descoberta Inteligente de Páginas e Nodes

- Lista páginas primeiro para seleção direcionada
- Análise hierárquica até depth=3 (evita granularidade excessiva)
- Identificação automática de componentes reutilizáveis
- Classificação por complexidade e relevância

### 🎯 Estratégia em Duas Etapas

1. **Seleção de Páginas**: Usuário escolhe quais páginas processar
2. **Descoberta de Nodes**: Análise focada nas páginas selecionadas

- Processamento node-by-node via API
- Evita downloads de arquivos grandes (50MB+)
- Cache inteligente de dados processados

### 💾 Armazenamento Estruturado

- **SQLite**: Para consultas rápidas e relacionamentos
- **Organização**: `data/output/{project_id}/`
- **Metadados**: Informações completas de descoberta
- **Views otimizadas**: Componentes filtrados para geração

### 🎨 Integração com Design System (Planejado)

- Carregamento de documentação do Storybook
- Mapeamento automático de componentes Figma → Design System
- Base de dados de componentes para consulta rápida
- Geração de componentes React/Angular com IA
- ✅ Escalável para arquivos grandes

## Instalação

1. Clone o repositório:

```bash
git clone <repository-url>
cd design_to_code
```

2. Instale as dependências:

```bash
pip install -r requirements.txt
```

3. Configure o projeto:

```bash
# Copie o arquivo de exemplo
cp project_config.example.yaml project_config.yaml

# Edite com suas configurações do Figma
# - file_key: Chave do arquivo Figma
# - token: Token da API Figma
```

## Como Usar

### Método Principal (Recomendado)

O projeto inclui um script unificado que simplifica todo o processo:

```bash
# 1. Configuração inicial (uma vez)
./run.sh setup

# 2. Execute o processo completo
./run.sh process
```

**O que o comando `./run.sh process` faz:**

1. Lista páginas disponíveis no arquivo Figma
2. Permite seleção interativa das páginas desejadas
3. Descobre nodes até depth=3 nas páginas selecionadas
4. Armazena dados estruturados em SQLite
5. Gera relatório de componentes identificados

**Outros comandos úteis:**

```bash
./run.sh setup      # Configuração inicial
./run.sh discover   # Apenas descoberta de nodes
./run.sh clean      # Limpar dados gerados
./run.sh help       # Mostrar ajuda completa
```

### Scripts Alternativos

```bash
# Descoberta focada em páginas
python scripts/discover_pages.py

# Descoberta tradicional com argumentos
python scripts/discover_nodes.py --file-key SEU_FILE_KEY --interactive

# Processamento de nodes já selecionados
python scripts/process_selected_nodes.py --file-key SEU_FILE_KEY \
                                        --selection-file data/discovery/selected_nodes.json

# Pipeline completo (método legado)
python scripts/run_pipeline.py --api --file-key SEU_FILE_KEY --token SEU_TOKEN
```

## Requisitos

- Python 3.10+
- Demais dependências listadas em `requirements.txt`

## Instalação

```bash
pip install -r requirements.txt
```

## Estrutura dos Dados do Figma

O JSON da API do Figma contém:

- **nodes**: Hierarquia de componentes da tela
- **components**: Definições de componentes reutilizáveis
- **componentSets**: Conjuntos de variantes de componentes
- **styles**: Estilos definidos no design system

### Tipos de Componentes Identificados

- `FRAME`: Containers e layouts
- `TEXT`: Elementos de texto
- `RECTANGLE`: Formas retangulares
- `VECTOR`: Ícones e formas vetoriais
- `INSTANCE`: Instâncias de componentes
- `GROUP`: Agrupamentos de elementos

## Arquivos Gerados

Após executar o pipeline, os seguintes arquivos são gerados:

### Dados Processados (`data/processed/`)

- `components.json` - Dados completos dos componentes em JSON
- `components.parquet` - Dados otimizados para análise (pandas/BI)
- `components.csv` - Dados em formato planilha

### Código Gerado (`data/output/`)

- `html/index.html` - HTML completo com CSS inline
- `html/index_external_css.html` - HTML com CSS externo
- `css/styles.css` - CSS avançado com design tokens e responsividade

### Relatórios (`data/reports/`)

- `pipeline_report.json` - Relatório completo da execução

## Funcionalidades Avançadas

### Design Tokens

O sistema extrai automaticamente:

- Cores utilizadas no design
- Fontes e tamanhos de texto
- Espaçamentos e margens
- Border radius e sombras

### CSS Responsivo

- Breakpoints para tablet e mobile
- Classes utilitárias (margin, padding, flexbox)
- Animações e transições
- Estados de hover e focus

### Análise de Dados

- Estatísticas dos componentes
- Distribuição de tipos
- Análise de hierarquia
- Métricas de geração

## Próximos Passos

1. ✅ Implementar parser robusto do JSON
2. ✅ Criar sistema de extração de componentes
3. ✅ Desenvolver gerador de HTML/CSS
4. 🚧 Integrar design system do projeto
5. 🚧 Gerar componentes React/Angular
6. 🚧 Implementar temas e variações
