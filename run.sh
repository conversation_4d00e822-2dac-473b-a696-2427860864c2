#!/bin/bash

# Script unificado para Figma to Code Converter
# Facilita o uso do projeto com comandos simples

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para mostrar ajuda
show_help() {
    echo -e "${BLUE}Figma to Code Converter${NC}"
    echo "=========================="
    echo ""
    echo "Uso: ./run.sh [comando]"
    echo ""
    echo "Comandos disponíveis:"
    echo ""
    echo -e "${GREEN}setup${NC}           - Configuração inicial do projeto"
    echo -e "${GREEN}process${NC}         - Processo completo (descoberta + armazenamento)"
    echo -e "${GREEN}discover${NC}        - Descoberta de páginas e nodes"
    echo -e "${GREEN}legacy${NC}          - Pipeline completo (método legado)"
    echo -e "${GREEN}install${NC}         - Instalar dependências"
    echo -e "${GREEN}clean${NC}           - Limpar dados gerados"
    echo ""
    echo "Exemplos:"
    echo "  ./run.sh setup          # Configuração inicial"
    echo "  ./run.sh process        # Processo principal"
    echo "  ./run.sh discover       # Apenas descoberta"
    echo ""
}

# Função para verificar se Python está disponível
check_python() {
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ Python 3 não encontrado${NC}"
        echo "Instale Python 3.10+ para continuar"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Python encontrado:${NC} $(python3 --version)"
}

# Função para verificar dependências
check_dependencies() {
    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}❌ Arquivo requirements.txt não encontrado${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}📦 Verificando dependências...${NC}"
    
    # Verificar se as dependências estão instaladas
    if ! python3 -c "import requests, sqlite3, yaml" &> /dev/null; then
        echo -e "${YELLOW}⚠️  Algumas dependências não estão instaladas${NC}"
        echo "Execute: ./run.sh install"
        return 1
    fi
    
    echo -e "${GREEN}✅ Dependências verificadas${NC}"
    return 0
}

# Função para verificar configuração
check_config() {
    if [ ! -f "project_config.yaml" ]; then
        echo -e "${YELLOW}⚠️  Arquivo project_config.yaml não encontrado${NC}"
        echo "Execute: ./run.sh setup"
        return 1
    fi
    
    echo -e "${GREEN}✅ Configuração encontrada${NC}"
    return 0
}

# Comando: setup
cmd_setup() {
    echo -e "${BLUE}🔧 Configuração inicial do projeto${NC}"
    echo "=================================="
    
    # Verificar Python
    check_python
    
    # Instalar dependências se necessário
    if ! check_dependencies; then
        echo -e "${BLUE}📦 Instalando dependências...${NC}"
        python3 -m pip install -r requirements.txt
        echo -e "${GREEN}✅ Dependências instaladas${NC}"
    fi
    
    # Criar arquivo de configuração
    if [ ! -f "project_config.yaml" ]; then
        if [ -f "project_config.example.yaml" ]; then
            cp project_config.example.yaml project_config.yaml
            echo -e "${GREEN}✅ Arquivo project_config.yaml criado${NC}"
            echo ""
            echo -e "${YELLOW}📝 IMPORTANTE:${NC}"
            echo "   Edite o arquivo project_config.yaml com:"
            echo "   - file_key: Chave do seu arquivo Figma"
            echo "   - token: Token da API Figma"
            echo ""
            echo "   Para obter o token: https://www.figma.com/developers/api#access-tokens"
        else
            echo -e "${RED}❌ Arquivo project_config.example.yaml não encontrado${NC}"
            exit 1
        fi
    else
        echo -e "${GREEN}✅ Arquivo project_config.yaml já existe${NC}"
    fi
    
    # Criar diretórios necessários
    mkdir -p data/logs data/discovery data/output
    echo ""
    echo -e "${GREEN}✅ Diretórios criados${NC}"
    
    echo ""
    echo -e "${GREEN}🎉 Configuração concluída!${NC}"
    echo "Próximo passo: ./run.sh process"
}

# Comando: install
cmd_install() {
    echo -e "${BLUE}📦 Instalando dependências${NC}"
    echo "========================="
    
    check_python
    python3 -m pip install -r requirements.txt
    
    echo -e "${GREEN}✅ Dependências instaladas com sucesso${NC}"
}

# Comando: process
cmd_process() {
    echo -e "${BLUE}🔍 Processo completo: Descoberta + Armazenamento${NC}"
    echo "=============================================="
    
    # Verificações
    check_python
    if ! check_dependencies; then
        echo "Execute primeiro: ./run.sh install"
        exit 1
    fi
    
    if ! check_config; then
        echo "Execute primeiro: ./run.sh setup"
        exit 1
    fi
    
    # Executar processo principal
    echo -e "${BLUE}🚀 Iniciando processo...${NC}"
    python3 -m src.scripts.process_and_store
    
    echo ""
    echo -e "${GREEN}🎉 Processo concluído!${NC}"
    echo "Verifique os resultados em data/output/"
}

# Comando: discover
cmd_discover() {
    echo -e "${BLUE}🔍 Descoberta de páginas e nodes${NC}"
    echo "==============================="
    
    # Verificações
    check_python
    if ! check_dependencies; then
        echo "Execute primeiro: ./run.sh install"
        exit 1
    fi
    
    if ! check_config; then
        echo "Execute primeiro: ./run.sh setup"
        exit 1
    fi
    
    # Executar descoberta
    echo -e "${BLUE}🚀 Iniciando descoberta...${NC}"
    python3 -m src.scripts.discover_pages
    
    echo ""
    echo -e "${GREEN}✅ Descoberta concluída!${NC}"
    echo "Resultados salvos em data/discovery/"
}

# Comando: legacy
cmd_legacy() {
    echo -e "${BLUE}🔄 Pipeline completo (método legado)${NC}"
    echo "==================================="
    
    # Verificações
    check_python
    if ! check_dependencies; then
        echo "Execute primeiro: ./run.sh install"
        exit 1
    fi
    
    # Verificar se há argumentos necessários
    if [ ! -f "project_config.yaml" ]; then
        echo -e "${RED}❌ Configuração necessária para método legado${NC}"
        echo "Execute primeiro: ./run.sh setup"
        exit 1
    fi
    
    # Executar pipeline legado
    echo -e "${BLUE}🚀 Iniciando pipeline legado...${NC}"
    echo "Nota: Este método pode ser mais lento para arquivos grandes"
    
    # Ler configuração para obter file_key e token
    FILE_KEY=$(python3 -c "import yaml; config=yaml.safe_load(open('project_config.yaml')); print(config['figma']['file_key'])")
    TOKEN=$(python3 -c "import yaml; config=yaml.safe_load(open('project_config.yaml')); print(config['figma']['token'])")
    
    python3 -m src.scripts.run_pipeline --api --file-key "$FILE_KEY" --token "$TOKEN"
    
    echo ""
    echo -e "${GREEN}✅ Pipeline legado concluído!${NC}"
}

# Comando: clean
cmd_clean() {
    echo -e "${BLUE}🧹 Limpando dados gerados${NC}"
    echo "========================"
    
    # Confirmar limpeza
    echo -e "${YELLOW}⚠️  Esta ação irá remover:${NC}"
    echo "   - data/output/ (dados estruturados)"
    echo "   - data/logs/ (arquivos de log)"
    echo "   - data/discovery/ (dados de descoberta)"
    echo ""
    read -p "Continuar? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        rm -rf data/output data/logs data/discovery
        mkdir -p data/logs data/discovery data/output
        echo -e "${GREEN}✅ Limpeza concluída${NC}"
    else
        echo -e "${BLUE}ℹ️  Limpeza cancelada${NC}"
    fi
}

# Função principal
main() {
    case "${1:-help}" in
        "setup")
            cmd_setup
            ;;
        "install")
            cmd_install
            ;;
        "process")
            cmd_process
            ;;
        "discover")
            cmd_discover
            ;;
        "legacy")
            cmd_legacy
            ;;
        "clean")
            cmd_clean
            ;;
        "help"|"--help"|"-h"|"")
            show_help
            ;;
        *)
            echo -e "${RED}❌ Comando desconhecido: $1${NC}"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# Executar função principal
main "$@"
