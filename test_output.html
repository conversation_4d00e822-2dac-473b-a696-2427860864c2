<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAT CONTABILIDADE</title>
    <style>
/* Reset básico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.5;
}

/* Classes base do Figma */
.figma-component {
    position: relative;
}

#figma_1_15529 {
    width: 1280.0px;
    height: 192.0px;
    position: absolute;
    left: 2954.0px;
    top: 3314.0px;
    background-color: rgb(255, 255, 255);
}

#figma_1_15528 {
    width: 1280.0px;
    height: 192.0px;
    position: absolute;
    left: 2954.0px;
    top: 3314.0px;
}

#figma_20_12637 {
    width: 304.0px;
    height: 16.0px;
    position: absolute;
    left: 2978.0px;
    top: 3350.0px;
    font-size: 12.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(71, 72, 76);
    background-color: rgb(71, 72, 76);
}

#figma_20_12639 {
    width: 685.0px;
    height: 20.0px;
    position: absolute;
    left: 2990.0px;
    top: 3362.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(0, 0, 0);
    background-color: rgb(0, 0, 0);
}

#figma_20_12640 {
    width: 258.0px;
    height: 24.0px;
    position: absolute;
    left: 3036.0px;
    top: 3358.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(0, 0, 0);
    background-color: rgb(0, 0, 0);
}

#figma_20_12638 {
    width: 685.0px;
    height: 24.0px;
    position: absolute;
    left: 2990.0px;
    top: 3358.0px;
    display: flex;
    flex-direction: row;
}

#figma_20_12636 {
    width: 709.0px;
    height: 48.0px;
    position: absolute;
    left: 2978.0px;
    top: 3346.0px;
    display: flex;
    flex-direction: column;
    padding-left: 12.0px;
    padding-right: 12.0px;
    padding-top: 4.0px;
    padding-bottom: 4.0px;
}

#I20_12642_12258_2410_2051_95 {
    width: 14.872543334960938px;
    height: 14.87879753112793px;
    position: absolute;
    left: 3703.557373046875px;
    top: 3362.5634765625px;
    background-color: rgb(109, 110, 113);
}

#I20_12642_12258_2410 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3701.0px;
    top: 3360.0px;
    background-color: rgb(255, 255, 255);
}

#figma_20_12642 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 3687.0px;
    top: 3346.0px;
    background-color: rgb(250, 251, 255);
}

#figma_20_12643 {
    width: 1.0px;
    height: 32.0px;
    position: absolute;
    left: 3687.0px;
    top: 3354.0px;
    background-color: rgb(217, 220, 221);
}

#I20_12644_12258_2410_2051_95 {
    width: 14.872543334960938px;
    height: 14.87879753112793px;
    position: absolute;
    left: 3704.557373046875px;
    top: 3362.5634765625px;
    background-color: rgb(109, 110, 113);
}

#I20_12644_12258_2410 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3702.0px;
    top: 3360.0px;
    background-color: rgb(255, 255, 255);
}

#figma_20_12644 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 3688.0px;
    top: 3346.0px;
    background-color: rgb(250, 251, 255);
}

#figma_20_12641 {
    width: 49.0px;
    height: 48.0px;
    position: absolute;
    left: 3687.0px;
    top: 3346.0px;
    display: flex;
    flex-direction: row;
}

#figma_20_12635 {
    width: 758.0px;
    height: 48.0px;
    position: absolute;
    left: 2978.0px;
    top: 3346.0px;
    background-color: rgb(250, 251, 255);
    border-color: rgb(167, 168, 172);
    border-width: 1.0px;
    border-style: solid;
    border-radius: 2.0px;
}

#I1_15531_9228_53862_4695_16145_60_5079 {
    width: 15.0px;
    height: 15.0px;
    position: absolute;
    left: 3784.5px;
    top: 3362.5px;
    background-color: rgb(255, 255, 255);
}

#I1_15531_9228_53862_4695_16145 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3782.0px;
    top: 3360.0px;
}

#I1_15531_9228_53862 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 3768.0px;
    top: 3346.0px;
}

#I1_15531_9228_53864 {
    width: 144.0px;
    height: 16.0px;
    position: absolute;
    left: 3768.0px;
    top: 3350.0px;
    font-size: 12.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(71, 72, 76);
    background-color: rgb(71, 72, 76);
}

#I1_15531_9228_53866 {
    width: 54.0px;
    height: 20.0px;
    position: absolute;
    left: 3780.0px;
    top: 3358.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15531_9228_53867 {
    width: 370.0px;
    height: 20.0px;
    position: absolute;
    left: 3780.0px;
    top: 3362.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(0, 0, 0);
    background-color: rgb(0, 0, 0);
}

#I1_15531_9228_53868 {
    width: 90.0px;
    height: 20.0px;
    position: absolute;
    left: 3826.0px;
    top: 3358.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(0, 0, 0);
    background-color: rgb(0, 0, 0);
}

#I1_15531_9228_53869 {
    width: 55.0px;
    height: 20.0px;
    position: absolute;
    left: 4013.0px;
    top: 3358.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: right;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15531_9228_53865 {
    width: 370.0px;
    height: 24.0px;
    position: absolute;
    left: 3780.0px;
    top: 3358.0px;
    display: flex;
    flex-direction: row;
}

#I1_15531_9228_53863 {
    width: 394.0px;
    height: 48.0px;
    position: absolute;
    left: 3768.0px;
    top: 3346.0px;
    display: flex;
    flex-direction: column;
    padding-left: 12.0px;
    padding-right: 12.0px;
    padding-top: 4.0px;
    padding-bottom: 4.0px;
}

#I1_15531_9228_53871_23_8 {
    width: 12.0px;
    height: 12.0px;
    position: absolute;
    left: 4138.0px;
    top: 3364.0px;
    background-color: rgb(9, 171, 71);
}

#I1_15531_9228_53871 {
    width: 16.0px;
    height: 16.0px;
    position: absolute;
    left: 4136.0px;
    top: 3362.0px;
}

#I1_15531_9228_53870 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 4120.0px;
    top: 3346.0px;
    display: flex;
    flex-direction: row;
    padding-left: 16.0px;
    padding-right: 16.0px;
    padding-top: 16.0px;
    padding-bottom: 16.0px;
}

#I1_15531_13260_2835_188_48 {
    width: 38.0px;
    height: 16.0px;
    position: absolute;
    left: 3986.0px;
    top: 3362.0px;
    font-size: 14.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(59, 105, 255);
    background-color: rgb(59, 105, 255);
}

#I1_15531_13260_2835 {
    width: 38.0px;
    height: 16.0px;
    position: absolute;
    left: 3986.0px;
    top: 3362.0px;
    background-color: rgb(255, 255, 255);
    border-radius: 999.0px;
}

#I1_15531_13260_2834 {
    width: 54.0px;
    height: 48.0px;
    position: absolute;
    left: 3978.0px;
    top: 3346.0px;
}

#I1_15531_9228_53872_9228_55987_60_4921 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 4176.0px;
    top: 3360.0px;
    background-color: rgb(0, 0, 0);
}

#I1_15531_9228_53872_9228_55987_60_4920 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 4176.0px;
    top: 3360.0px;
}

#I1_15531_9228_53872_9228_55987_60_4923 {
    width: 19.31169319152832px;
    height: 19.452499389648438px;
    position: absolute;
    left: 4176.34375px;
    top: 3360.271240234375px;
    background-color: rgb(59, 105, 255);
}

#I1_15531_9228_53872_9228_55987_60_4922 {
    width: 19.31169319152832px;
    height: 19.452499389648438px;
    position: absolute;
    left: 4176.34375px;
    top: 3360.271240234375px;
}

#I1_15531_9228_53872_9228_55987_60_4919 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 4176.0px;
    top: 3360.0px;
}

#I1_15531_9228_53872_9228_55987 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 4176.0px;
    top: 3360.0px;
}

#I1_15531_9228_53872 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 4162.0px;
    top: 3346.0px;
}

#I1_15531_9228_53861 {
    width: 442.0px;
    height: 48.0px;
    position: absolute;
    left: 3768.0px;
    top: 3346.0px;
    background-color: rgb(250, 251, 255);
    border-color: rgb(167, 168, 172);
    border-width: 1.0px;
    border-style: solid;
    border-radius: 2.0px;
}

#I1_15531_9228_53873_4382_1607 {
    width: 312.0px;
    height: 16.0px;
    position: absolute;
    left: 3768.0px;
    top: 3398.0px;
    font-size: 10.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15531_9228_53873_4382_1608 {
    width: 27.0px;
    height: 16.0px;
    position: absolute;
    left: 4053.0px;
    top: 3398.0px;
    font-size: 10.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: right;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15531_9228_53873 {
    width: 312.0px;
    height: 16.0px;
    position: absolute;
    left: 3768.0px;
    top: 3398.0px;
}

#figma_1_15531 {
    width: 442.0px;
    height: 48.0px;
    position: absolute;
    left: 3768.0px;
    top: 3346.0px;
}

#figma_1_15530 {
    width: 1232.0px;
    height: 48.0px;
    position: absolute;
    left: 2978.0px;
    top: 3346.0px;
    display: flex;
    flex-direction: row;
}

#I1_15534_4689_5057_4695_16145_60_5077 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 2992.0px;
    top: 3442.0px;
    background-color: rgb(0, 0, 0);
}

#I1_15534_4689_5057_4695_16145_60_5076 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 2992.0px;
    top: 3442.0px;
}

#I1_15534_4689_5057_4695_16145_60_5079 {
    width: 15.0px;
    height: 15.0px;
    position: absolute;
    left: 2994.5px;
    top: 3444.5px;
    background-color: rgb(255, 255, 255);
}

#I1_15534_4689_5057_4695_16145_60_5078 {
    width: 15.0px;
    height: 15.0px;
    position: absolute;
    left: 2994.5px;
    top: 3444.5px;
}

#I1_15534_4689_5057_4695_16145_60_5075 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 2992.0px;
    top: 3442.0px;
}

#I1_15534_4689_5057_4695_16145 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 2992.0px;
    top: 3442.0px;
}

#I1_15534_4689_5057 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 2978.0px;
    top: 3428.0px;
}

#I1_15534_4689_5059 {
    width: 147.0px;
    height: 16.0px;
    position: absolute;
    left: 2978.0px;
    top: 3432.0px;
    font-size: 12.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(71, 72, 76);
    background-color: rgb(71, 72, 76);
}

#I1_15534_4689_5061 {
    width: 54.0px;
    height: 20.0px;
    position: absolute;
    left: 2990.0px;
    top: 3440.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15534_4689_5062 {
    width: 370.0px;
    height: 20.0px;
    position: absolute;
    left: 2990.0px;
    top: 3444.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(0, 0, 0);
    background-color: rgb(0, 0, 0);
}

#I1_15534_4689_5063 {
    width: 61.0px;
    height: 20.0px;
    position: absolute;
    left: 3036.0px;
    top: 3440.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(0, 0, 0);
    background-color: rgb(0, 0, 0);
}

#I1_15534_4689_5064 {
    width: 55.0px;
    height: 20.0px;
    position: absolute;
    left: 3223.0px;
    top: 3440.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: right;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15534_4689_5060 {
    width: 370.0px;
    height: 24.0px;
    position: absolute;
    left: 2990.0px;
    top: 3440.0px;
    display: flex;
    flex-direction: row;
}

#I1_15534_4689_5058 {
    width: 394.0px;
    height: 48.0px;
    position: absolute;
    left: 2978.0px;
    top: 3428.0px;
    display: flex;
    flex-direction: column;
    padding-left: 12.0px;
    padding-right: 12.0px;
    padding-top: 4.0px;
    padding-bottom: 4.0px;
}

#I1_15534_4689_5066_23_8 {
    width: 12.0px;
    height: 12.0px;
    position: absolute;
    left: 3348.0px;
    top: 3446.0px;
    background-color: rgb(9, 171, 71);
}

#I1_15534_4689_5066 {
    width: 16.0px;
    height: 16.0px;
    position: absolute;
    left: 3346.0px;
    top: 3444.0px;
}

#I1_15534_4689_5065 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 3330.0px;
    top: 3428.0px;
    display: flex;
    flex-direction: row;
    padding-left: 16.0px;
    padding-right: 16.0px;
    padding-top: 16.0px;
    padding-bottom: 16.0px;
}

#I1_15534_4689_5067_4689_9290_60_5155 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3386.0px;
    top: 3442.0px;
    background-color: rgb(0, 0, 0);
}

#I1_15534_4689_5067_4689_9290_60_5154 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3386.0px;
    top: 3442.0px;
}

#I1_15534_4689_5067_4689_9290_60_5157 {
    width: 15.000953674316406px;
    height: 8.749751091003418px;
    position: absolute;
    left: 3388.49951171875px;
    top: 3447.624755859375px;
    background-color: rgb(59, 105, 255);
}

#I1_15534_4689_5067_4689_9290_60_5156 {
    width: 15.000953674316406px;
    height: 8.749751091003418px;
    position: absolute;
    left: 3388.49951171875px;
    top: 3447.624755859375px;
}

#I1_15534_4689_5067_4689_9290_60_5153 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3386.0px;
    top: 3442.0px;
}

#I1_15534_4689_5067_4689_9290 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3386.0px;
    top: 3442.0px;
}

#I1_15534_4689_5067 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 3372.0px;
    top: 3428.0px;
}

#I1_15534_4689_5056 {
    width: 442.0px;
    height: 50.0px;
    position: absolute;
    left: 2978.0px;
    top: 3428.0px;
    background-color: rgb(250, 251, 255);
    border-color: rgb(167, 168, 172);
    border-width: 1.0px;
    border-style: solid;
    border-radius: 2.0px;
}

#I1_15534_4689_5068_4382_1607 {
    width: 312.0px;
    height: 16.0px;
    position: absolute;
    left: 2978.0px;
    top: 3480.0px;
    font-size: 10.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15534_4689_5068_4382_1608 {
    width: 27.0px;
    height: 16.0px;
    position: absolute;
    left: 3263.0px;
    top: 3480.0px;
    font-size: 10.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: right;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15534_4689_5068 {
    width: 312.0px;
    height: 16.0px;
    position: absolute;
    left: 2978.0px;
    top: 3480.0px;
}

#figma_1_15534 {
    width: 442.0px;
    height: 50.0px;
    position: absolute;
    left: 2978.0px;
    top: 3428.0px;
}

#I1_15535_4689_5057_4695_16145_60_5077 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3466.0px;
    top: 3442.0px;
    background-color: rgb(0, 0, 0);
}

#I1_15535_4689_5057_4695_16145_60_5076 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3466.0px;
    top: 3442.0px;
}

#I1_15535_4689_5057_4695_16145_60_5079 {
    width: 15.0px;
    height: 15.0px;
    position: absolute;
    left: 3468.5px;
    top: 3444.5px;
    background-color: rgb(255, 255, 255);
}

#I1_15535_4689_5057_4695_16145_60_5078 {
    width: 15.0px;
    height: 15.0px;
    position: absolute;
    left: 3468.5px;
    top: 3444.5px;
}

#I1_15535_4689_5057_4695_16145_60_5075 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3466.0px;
    top: 3442.0px;
}

#I1_15535_4689_5057_4695_16145 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3466.0px;
    top: 3442.0px;
}

#I1_15535_4689_5057 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 3452.0px;
    top: 3428.0px;
}

#I1_15535_4689_5059 {
    width: 41.0px;
    height: 16.0px;
    position: absolute;
    left: 3452.0px;
    top: 3432.0px;
    font-size: 12.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(71, 72, 76);
    background-color: rgb(71, 72, 76);
}

#I1_15535_4689_5061 {
    width: 54.0px;
    height: 20.0px;
    position: absolute;
    left: 3464.0px;
    top: 3440.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15535_4689_5062 {
    width: 686.0px;
    height: 20.0px;
    position: absolute;
    left: 3464.0px;
    top: 3444.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(0, 0, 0);
    background-color: rgb(0, 0, 0);
}

#I1_15535_4689_5063 {
    width: 61.0px;
    height: 20.0px;
    position: absolute;
    left: 3510.0px;
    top: 3440.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: left;
    color: rgb(0, 0, 0);
    background-color: rgb(0, 0, 0);
}

#I1_15535_4689_5064 {
    width: 55.0px;
    height: 20.0px;
    position: absolute;
    left: 3697.0px;
    top: 3440.0px;
    font-size: 16.0px;
    font-family: Bradesco Sans;
    font-weight: 500;
    text-align: right;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15535_4689_5060 {
    width: 686.0px;
    height: 24.0px;
    position: absolute;
    left: 3464.0px;
    top: 3440.0px;
    display: flex;
    flex-direction: row;
}

#I1_15535_4689_5058 {
    width: 710.0px;
    height: 48.0px;
    position: absolute;
    left: 3452.0px;
    top: 3428.0px;
    display: flex;
    flex-direction: column;
    padding-left: 12.0px;
    padding-right: 12.0px;
    padding-top: 4.0px;
    padding-bottom: 4.0px;
}

#I1_15535_4689_5066_23_8 {
    width: 12.0px;
    height: 12.0px;
    position: absolute;
    left: 3822.0px;
    top: 3446.0px;
    background-color: rgb(9, 171, 71);
}

#I1_15535_4689_5066 {
    width: 16.0px;
    height: 16.0px;
    position: absolute;
    left: 3820.0px;
    top: 3444.0px;
}

#I1_15535_4689_5065 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 3804.0px;
    top: 3428.0px;
    display: flex;
    flex-direction: row;
    padding-left: 16.0px;
    padding-right: 16.0px;
    padding-top: 16.0px;
    padding-bottom: 16.0px;
}

#I1_15535_4689_5067_4689_9290_60_5155 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 4176.0px;
    top: 3442.0px;
    background-color: rgb(0, 0, 0);
}

#I1_15535_4689_5067_4689_9290_60_5154 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 4176.0px;
    top: 3442.0px;
}

#I1_15535_4689_5067_4689_9290_60_5157 {
    width: 15.000953674316406px;
    height: 8.749751091003418px;
    position: absolute;
    left: 4178.49951171875px;
    top: 3447.624755859375px;
    background-color: rgb(59, 105, 255);
}

#I1_15535_4689_5067_4689_9290_60_5156 {
    width: 15.000953674316406px;
    height: 8.749751091003418px;
    position: absolute;
    left: 4178.49951171875px;
    top: 3447.624755859375px;
}

#I1_15535_4689_5067_4689_9290_60_5153 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 4176.0px;
    top: 3442.0px;
}

#I1_15535_4689_5067_4689_9290 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 4176.0px;
    top: 3442.0px;
}

#I1_15535_4689_5067 {
    width: 48.0px;
    height: 48.0px;
    position: absolute;
    left: 4162.0px;
    top: 3428.0px;
}

#I1_15535_4689_5056 {
    width: 758.0px;
    height: 50.0px;
    position: absolute;
    left: 3452.0px;
    top: 3428.0px;
    background-color: rgb(250, 251, 255);
    border-color: rgb(167, 168, 172);
    border-width: 1.0px;
    border-style: solid;
    border-radius: 2.0px;
}

#I1_15535_4689_5068_4382_1607 {
    width: 312.0px;
    height: 16.0px;
    position: absolute;
    left: 3452.0px;
    top: 3480.0px;
    font-size: 10.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15535_4689_5068_4382_1608 {
    width: 27.0px;
    height: 16.0px;
    position: absolute;
    left: 3737.0px;
    top: 3480.0px;
    font-size: 10.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: right;
    color: rgb(109, 110, 113);
    background-color: rgb(109, 110, 113);
}

#I1_15535_4689_5068 {
    width: 312.0px;
    height: 16.0px;
    position: absolute;
    left: 3452.0px;
    top: 3480.0px;
}

#figma_1_15535 {
    width: 758.0px;
    height: 50.0px;
    position: absolute;
    left: 3452.0px;
    top: 3428.0px;
}

#figma_1_15533 {
    width: 1232.0px;
    height: 50.0px;
    position: absolute;
    left: 2978.0px;
    top: 3428.0px;
    display: flex;
    flex-direction: row;
}

#I1_15537_2104_1535_60_5079 {
    width: 15.0px;
    height: 15.0px;
    position: absolute;
    left: 3103.0px;
    top: 3442.5px;
    background-color: rgb(255, 255, 255);
}

#I1_15537_2104_1535 {
    width: 20.0px;
    height: 20.0px;
    position: absolute;
    left: 3100.5px;
    top: 3440.0px;
}

#I1_15537_2104_1536 {
    width: 47.0px;
    height: 16.0px;
    position: absolute;
    left: 3054.5px;
    top: 3442.0px;
    font-size: 14.0px;
    font-family: Bradesco Sans;
    font-weight: 700;
    text-align: center;
    color: rgb(255, 255, 255);
    background-color: rgb(255, 255, 255);
}

#figma_1_15537 {
    width: 200.0px;
    height: 48.0px;
    position: absolute;
    left: 2978.0px;
    top: 3426.0px;
    background-color: rgb(167, 168, 172);
    border-radius: 999.0px;
}

#I1_15538_179_1045 {
    width: 48.0px;
    height: 16.0px;
    position: absolute;
    left: 3206.0px;
    top: 3442.0px;
    font-size: 14.0px;
    font-family: Bradesco Sans;
    font-weight: 600;
    text-align: left;
    color: rgb(59, 105, 255);
    background-color: rgb(59, 105, 255);
}

#figma_1_15538 {
    width: 64.0px;
    height: 24.0px;
    position: absolute;
    left: 3198.0px;
    top: 3438.0px;
    background-color: rgb(255, 255, 255);
}

#figma_1_15536 {
    width: 284.0px;
    height: 48.0px;
    position: absolute;
    left: 2978.0px;
    top: 3426.0px;
    display: flex;
    flex-direction: row;
}

#figma_1_15527 {
    width: 1280.0px;
    height: 192.0px;
    position: absolute;
    left: 2954.0px;
    top: 3314.0px;
    display: flex;
    flex-direction: column;
    padding-left: 24.0px;
    padding-right: 24.0px;
    padding-top: 32.0px;
    padding-bottom: 32.0px;
}

/* Estilos por tipo de componente */
.figma-text {
    display: inline-block;
}

.figma-button {
    cursor: pointer;
    border: none;
    outline: none;
    transition: all 0.2s ease;
}

.figma-button:hover {
    opacity: 0.8;
}

.figma-input {
    border: 1px solid #ccc;
    padding: 8px 12px;
    border-radius: 4px;
}

.figma-container {
    display: block;
}

.figma-image {
    max-width: 100%;
    height: auto;
}
    </style>
</head>
<body>
<div id="figma_1_15527" class="figma-component figma-container figma-name-box-filtros" data-figma-id="1:15527" data-figma-name="Box filtros" data-figma-type="FRAME">
  <div id="figma_1_15528" class="figma-component figma-container figma-name-box" data-figma-id="1:15528" data-figma-name="box" data-figma-type="FRAME">
    <div id="figma_1_15529" class="figma-component figma-shape figma-name-rectangle-9711" data-figma-id="1:15529" data-figma-name="Rectangle 9711" data-figma-type="RECTANGLE"></div>
  </div>
  <div id="figma_1_15530" class="figma-component figma-container figma-name-linha-filtro-7" data-figma-id="1:15530" data-figma-name="linha filtro 7" data-figma-type="FRAME">
    <div id="figma_20_12635" class="figma-component figma-input figma-name-input-container" data-figma-id="20:12635" data-figma-name="Input Container" data-figma-type="FRAME">
      <div id="figma_20_12636" class="figma-component figma-container figma-name-text-container" data-figma-id="20:12636" data-figma-name="Text Container" data-figma-type="FRAME">
        <span id="figma_20_12637" class="figma-component figma-text figma-name-label" data-figma-id="20:12637" data-figma-name="Label" data-figma-type="TEXT">Cliente</span>
        <div id="figma_20_12638" class="figma-component figma-container figma-name-content" data-figma-id="20:12638" data-figma-name="Content" data-figma-type="FRAME">
          <span id="figma_20_12639" class="figma-component figma-text figma-name-cliente" data-figma-id="20:12639" data-figma-name="Cliente" data-figma-type="TEXT">Cliente</span>
          <span id="figma_20_12640" class="figma-component figma-text figma-name-content" data-figma-id="20:12640" data-figma-name="Content" data-figma-type="TEXT">Content</span>
        </div>
      </div>
      <div id="figma_20_12641" class="figma-component figma-container figma-name-actions" data-figma-id="20:12641" data-figma-name="Actions" data-figma-type="FRAME">
        <div id="figma_20_12642" class="figma-component figma-component figma-name-icon" data-figma-id="20:12642" data-figma-name=".Icon" data-figma-type="INSTANCE">
          <div id="I20_12642_12258_2410" class="figma-component figma-component figma-name-component-search" data-figma-id="I20:12642;12258:2410" data-figma-name="component-search" data-figma-type="INSTANCE">
            <div id="I20_12642_12258_2410_2051_95" class="figma-component figma-shape figma-name-component-search-path" data-figma-id="I20:12642;12258:2410;2051:95" data-figma-name="component-search-path" data-figma-type="VECTOR"></div>
          </div>
        </div>
        <div id="figma_20_12643" class="figma-component figma-shape figma-name-divider" data-figma-id="20:12643" data-figma-name="Divider" data-figma-type="RECTANGLE"></div>
        <div id="figma_20_12644" class="figma-component figma-component figma-name-icon" data-figma-id="20:12644" data-figma-name=".Icon" data-figma-type="INSTANCE">
          <div id="I20_12644_12258_2410" class="figma-component figma-component figma-name-component-search" data-figma-id="I20:12644;12258:2410" data-figma-name="component-search" data-figma-type="INSTANCE">
            <div id="I20_12644_12258_2410_2051_95" class="figma-component figma-shape figma-name-component-search-path" data-figma-id="I20:12644;12258:2410;2051:95" data-figma-name="component-search-path" data-figma-type="VECTOR"></div>
          </div>
        </div>
      </div>
    </div>
    <div id="figma_1_15531" class="figma-component figma-component figma-name-text-field-calendar" data-figma-id="1:15531" data-figma-name="Text Field Calendar" data-figma-type="INSTANCE">
      <div id="I1_15531_9228_53861" class="figma-component figma-input figma-name-input-container" data-figma-id="I1:15531;9228:53861" data-figma-name="Input Container" data-figma-type="FRAME">
        <div id="I1_15531_9228_53862" class="figma-component figma-component figma-name-icon" data-figma-id="I1:15531;9228:53862" data-figma-name=".Icon" data-figma-type="INSTANCE">
          <div id="I1_15531_9228_53862_4695_16145" class="figma-component figma-component figma-name-ui-icon-placeholder" data-figma-id="I1:15531;9228:53862;4695:16145" data-figma-name="ui-icon-placeholder" data-figma-type="INSTANCE">
            <div id="I1_15531_9228_53862_4695_16145_60_5079" class="figma-component figma-shape figma-name-caminho-4704740" data-figma-id="I1:15531;9228:53862;4695:16145;60:5079" data-figma-name="Caminho 4704740" data-figma-type="VECTOR"></div>
          </div>
        </div>
        <div id="I1_15531_9228_53863" class="figma-component figma-container figma-name-text-container" data-figma-id="I1:15531;9228:53863" data-figma-name="Text Container" data-figma-type="FRAME">
          <span id="I1_15531_9228_53864" class="figma-component figma-text figma-name-label" data-figma-id="I1:15531;9228:53864" data-figma-name="Label" data-figma-type="TEXT">Fechamento de exercício</span>
          <div id="I1_15531_9228_53865" class="figma-component figma-container figma-name-content" data-figma-id="I1:15531;9228:53865" data-figma-name="Content" data-figma-type="FRAME">
            <span id="I1_15531_9228_53866" class="figma-component figma-text figma-name-prefix" data-figma-id="I1:15531;9228:53866" data-figma-name="PREFIX" data-figma-type="TEXT">PREFIX</span>
            <span id="I1_15531_9228_53867" class="figma-component figma-text figma-name-label" data-figma-id="I1:15531;9228:53867" data-figma-name="Label" data-figma-type="TEXT">Fechamento de exercício</span>
            <span id="I1_15531_9228_53868" class="figma-component figma-text figma-name-05-08-2024" data-figma-id="I1:15531;9228:53868" data-figma-name="05/08/2024" data-figma-type="TEXT">05/08/2024</span>
            <span id="I1_15531_9228_53869" class="figma-component figma-text figma-name-suffix" data-figma-id="I1:15531;9228:53869" data-figma-name="SUFFIX" data-figma-type="TEXT">SUFFIX</span>
          </div>
        </div>
        <div id="I1_15531_9228_53870" class="figma-component figma-container figma-name-validation-icon" data-figma-id="I1:15531;9228:53870" data-figma-name="Validation Icon" data-figma-type="FRAME">
          <div id="I1_15531_9228_53871" class="figma-component figma-component figma-name-ui-icon-placeholder" data-figma-id="I1:15531;9228:53871" data-figma-name="ui-icon-placeholder" data-figma-type="INSTANCE">
            <div id="I1_15531_9228_53871_23_8" class="figma-component figma-image figma-name-icon" data-figma-id="I1:15531;9228:53871;23:8" data-figma-name="icon" data-figma-type="VECTOR"></div>
          </div>
        </div>
        <button id="I1_15531_13260_2834" class="figma-component figma-button figma-name-button-text-container btn" type="button" data-figma-id="I1:15531;13260:2834" data-figma-name="Button Text Container" data-figma-type="FRAME">
          <div id="I1_15531_13260_2835" class="figma-component figma-component figma-name-button-text" data-figma-id="I1:15531;13260:2835" data-figma-name="Button Text" data-figma-type="INSTANCE">
            <span id="I1_15531_13260_2835_188_48" class="figma-component figma-text figma-name-label" data-figma-id="I1:15531;13260:2835;188:48" data-figma-name="✏️ Label" data-figma-type="TEXT">Label</span>
          </div>
        </button>
        <div id="I1_15531_9228_53872" class="figma-component figma-component figma-name-calendar-button" data-figma-id="I1:15531;9228:53872" data-figma-name=".Calendar Button" data-figma-type="INSTANCE">
          <div id="I1_15531_9228_53872_9228_55987" class="figma-component figma-component figma-name-ui-calendar" data-figma-id="I1:15531;9228:53872;9228:55987" data-figma-name="ui-calendar" data-figma-type="INSTANCE">
            <div id="I1_15531_9228_53872_9228_55987_60_4919" class="figma-component figma-container figma-name-clip-path-group" data-figma-id="I1:15531;9228:53872;9228:55987;60:4919" data-figma-name="Clip path group" data-figma-type="GROUP">
              <div id="I1_15531_9228_53872_9228_55987_60_4920" class="figma-component figma-container figma-name-clip-path-54" data-figma-id="I1:15531;9228:53872;9228:55987;60:4920" data-figma-name="clip-path-54" data-figma-type="GROUP">
                <div id="I1_15531_9228_53872_9228_55987_60_4921" class="figma-component figma-shape figma-name-ret-ngulo-99097" data-figma-id="I1:15531;9228:53872;9228:55987;60:4921" data-figma-name="Retângulo 99097" data-figma-type="VECTOR"></div>
              </div>
              <div id="I1_15531_9228_53872_9228_55987_60_4922" class="figma-component figma-container figma-name-grupo-de-m-scara-172" data-figma-id="I1:15531;9228:53872;9228:55987;60:4922" data-figma-name="Grupo de máscara 172" data-figma-type="GROUP">
                <div id="I1_15531_9228_53872_9228_55987_60_4923" class="figma-component figma-shape figma-name-caminho-77394" data-figma-id="I1:15531;9228:53872;9228:55987;60:4923" data-figma-name="Caminho 77394" data-figma-type="VECTOR"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="I1_15531_9228_53873" class="figma-component figma-component figma-name-helper" data-figma-id="I1:15531;9228:53873" data-figma-name=".Helper" data-figma-type="INSTANCE">
        <span id="I1_15531_9228_53873_4382_1607" class="figma-component figma-text figma-name-helper-text" data-figma-id="I1:15531;9228:53873;4382:1607" data-figma-name="Helper Text" data-figma-type="TEXT">Helper Text</span>
        <span id="I1_15531_9228_53873_4382_1608" class="figma-component figma-text figma-name-16-40" data-figma-id="I1:15531;9228:53873;4382:1608" data-figma-name="16/40" data-figma-type="TEXT">16/40</span>
      </div>
    </div>
  </div>
  <div id="figma_1_15533" class="figma-component figma-container figma-name-linha-filtro-8" data-figma-id="1:15533" data-figma-name="linha filtro 8" data-figma-type="FRAME">
    <div id="figma_1_15534" class="figma-component figma-component figma-name-text-field-select" data-figma-id="1:15534" data-figma-name="Text Field Select" data-figma-type="INSTANCE">
      <div id="I1_15534_4689_5056" class="figma-component figma-input figma-name-input-container" data-figma-id="I1:15534;4689:5056" data-figma-name="Input Container" data-figma-type="FRAME">
        <div id="I1_15534_4689_5057" class="figma-component figma-component figma-name-icon" data-figma-id="I1:15534;4689:5057" data-figma-name=".Icon" data-figma-type="INSTANCE">
          <div id="I1_15534_4689_5057_4695_16145" class="figma-component figma-component figma-name-ui-icon-placeholder" data-figma-id="I1:15534;4689:5057;4695:16145" data-figma-name="ui-icon-placeholder" data-figma-type="INSTANCE">
            <div id="I1_15534_4689_5057_4695_16145_60_5075" class="figma-component figma-container figma-name-clip-path-group" data-figma-id="I1:15534;4689:5057;4695:16145;60:5075" data-figma-name="Clip path group" data-figma-type="GROUP">
              <div id="I1_15534_4689_5057_4695_16145_60_5076" class="figma-component figma-container figma-name-clip-path-52" data-figma-id="I1:15534;4689:5057;4695:16145;60:5076" data-figma-name="clip-path-52" data-figma-type="GROUP">
                <div id="I1_15534_4689_5057_4695_16145_60_5077" class="figma-component figma-shape figma-name-ret-ngulo-114899" data-figma-id="I1:15534;4689:5057;4695:16145;60:5077" data-figma-name="Retângulo 114899" data-figma-type="VECTOR"></div>
              </div>
              <div id="I1_15534_4689_5057_4695_16145_60_5078" class="figma-component figma-container figma-name-grupo-de-m-scara-122448" data-figma-id="I1:15534;4689:5057;4695:16145;60:5078" data-figma-name="Grupo de máscara 122448" data-figma-type="GROUP">
                <div id="I1_15534_4689_5057_4695_16145_60_5079" class="figma-component figma-shape figma-name-caminho-4704740" data-figma-id="I1:15534;4689:5057;4695:16145;60:5079" data-figma-name="Caminho 4704740" data-figma-type="VECTOR"></div>
              </div>
            </div>
          </div>
        </div>
        <div id="I1_15534_4689_5058" class="figma-component figma-container figma-name-text-container" data-figma-id="I1:15534;4689:5058" data-figma-name="Text Container" data-figma-type="FRAME">
          <span id="I1_15534_4689_5059" class="figma-component figma-text figma-name-label" data-figma-id="I1:15534;4689:5059" data-figma-name="Label" data-figma-type="TEXT">Demonstração Financeira</span>
          <div id="I1_15534_4689_5060" class="figma-component figma-container figma-name-content" data-figma-id="I1:15534;4689:5060" data-figma-name="Content" data-figma-type="FRAME">
            <span id="I1_15534_4689_5061" class="figma-component figma-text figma-name-prefix" data-figma-id="I1:15534;4689:5061" data-figma-name="PREFIX" data-figma-type="TEXT">PREFIX</span>
            <span id="I1_15534_4689_5062" class="figma-component figma-text figma-name-label" data-figma-id="I1:15534;4689:5062" data-figma-name="Label" data-figma-type="TEXT">Demonstração Financeira</span>
            <span id="I1_15534_4689_5063" class="figma-component figma-text figma-name-content" data-figma-id="I1:15534;4689:5063" data-figma-name="Content" data-figma-type="TEXT">Content</span>
            <span id="I1_15534_4689_5064" class="figma-component figma-text figma-name-suffix" data-figma-id="I1:15534;4689:5064" data-figma-name="SUFFIX" data-figma-type="TEXT">SUFFIX</span>
          </div>
        </div>
        <div id="I1_15534_4689_5065" class="figma-component figma-container figma-name-validation-icon" data-figma-id="I1:15534;4689:5065" data-figma-name="Validation Icon" data-figma-type="FRAME">
          <div id="I1_15534_4689_5066" class="figma-component figma-component figma-name-ui-icon-placeholder" data-figma-id="I1:15534;4689:5066" data-figma-name="ui-icon-placeholder" data-figma-type="INSTANCE">
            <div id="I1_15534_4689_5066_23_8" class="figma-component figma-image figma-name-icon" data-figma-id="I1:15534;4689:5066;23:8" data-figma-name="icon" data-figma-type="VECTOR"></div>
          </div>
        </div>
        <div id="I1_15534_4689_5067" class="figma-component figma-component figma-name-chevron" data-figma-id="I1:15534;4689:5067" data-figma-name=".Chevron" data-figma-type="INSTANCE">
          <div id="I1_15534_4689_5067_4689_9290" class="figma-component figma-component figma-name-ui-chevron-down" data-figma-id="I1:15534;4689:5067;4689:9290" data-figma-name="ui-chevron-down" data-figma-type="INSTANCE">
            <div id="I1_15534_4689_5067_4689_9290_60_5153" class="figma-component figma-container figma-name-clip-path-group" data-figma-id="I1:15534;4689:5067;4689:9290;60:5153" data-figma-name="Clip path group" data-figma-type="GROUP">
              <div id="I1_15534_4689_5067_4689_9290_60_5154" class="figma-component figma-container figma-name-clip-path-51" data-figma-id="I1:15534;4689:5067;4689:9290;60:5154" data-figma-name="clip-path-51" data-figma-type="GROUP">
                <div id="I1_15534_4689_5067_4689_9290_60_5155" class="figma-component figma-shape figma-name-ret-ngulo-112764" data-figma-id="I1:15534;4689:5067;4689:9290;60:5155" data-figma-name="Retângulo 112764" data-figma-type="VECTOR"></div>
              </div>
              <div id="I1_15534_4689_5067_4689_9290_60_5156" class="figma-component figma-container figma-name-grupo-de-m-scara-122415" data-figma-id="I1:15534;4689:5067;4689:9290;60:5156" data-figma-name="Grupo de máscara 122415" data-figma-type="GROUP">
                <div id="I1_15534_4689_5067_4689_9290_60_5157" class="figma-component figma-shape figma-name-caminho-714383" data-figma-id="I1:15534;4689:5067;4689:9290;60:5157" data-figma-name="Caminho 714383" data-figma-type="VECTOR"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="I1_15534_4689_5068" class="figma-component figma-component figma-name-helper" data-figma-id="I1:15534;4689:5068" data-figma-name=".Helper" data-figma-type="INSTANCE">
        <span id="I1_15534_4689_5068_4382_1607" class="figma-component figma-text figma-name-helper-text" data-figma-id="I1:15534;4689:5068;4382:1607" data-figma-name="Helper Text" data-figma-type="TEXT">Helper Text</span>
        <span id="I1_15534_4689_5068_4382_1608" class="figma-component figma-text figma-name-16-40" data-figma-id="I1:15534;4689:5068;4382:1608" data-figma-name="16/40" data-figma-type="TEXT">16/40</span>
      </div>
    </div>
    <div id="figma_1_15535" class="figma-component figma-component figma-name-text-field-select" data-figma-id="1:15535" data-figma-name="Text Field Select" data-figma-type="INSTANCE">
      <div id="I1_15535_4689_5056" class="figma-component figma-input figma-name-input-container" data-figma-id="I1:15535;4689:5056" data-figma-name="Input Container" data-figma-type="FRAME">
        <div id="I1_15535_4689_5057" class="figma-component figma-component figma-name-icon" data-figma-id="I1:15535;4689:5057" data-figma-name=".Icon" data-figma-type="INSTANCE">
          <div id="I1_15535_4689_5057_4695_16145" class="figma-component figma-component figma-name-ui-icon-placeholder" data-figma-id="I1:15535;4689:5057;4695:16145" data-figma-name="ui-icon-placeholder" data-figma-type="INSTANCE">
            <div id="I1_15535_4689_5057_4695_16145_60_5075" class="figma-component figma-container figma-name-clip-path-group" data-figma-id="I1:15535;4689:5057;4695:16145;60:5075" data-figma-name="Clip path group" data-figma-type="GROUP">
              <div id="I1_15535_4689_5057_4695_16145_60_5076" class="figma-component figma-container figma-name-clip-path-52" data-figma-id="I1:15535;4689:5057;4695:16145;60:5076" data-figma-name="clip-path-52" data-figma-type="GROUP">
                <div id="I1_15535_4689_5057_4695_16145_60_5077" class="figma-component figma-shape figma-name-ret-ngulo-114899" data-figma-id="I1:15535;4689:5057;4695:16145;60:5077" data-figma-name="Retângulo 114899" data-figma-type="VECTOR"></div>
              </div>
              <div id="I1_15535_4689_5057_4695_16145_60_5078" class="figma-component figma-container figma-name-grupo-de-m-scara-122448" data-figma-id="I1:15535;4689:5057;4695:16145;60:5078" data-figma-name="Grupo de máscara 122448" data-figma-type="GROUP">
                <div id="I1_15535_4689_5057_4695_16145_60_5079" class="figma-component figma-shape figma-name-caminho-4704740" data-figma-id="I1:15535;4689:5057;4695:16145;60:5079" data-figma-name="Caminho 4704740" data-figma-type="VECTOR"></div>
              </div>
            </div>
          </div>
        </div>
        <div id="I1_15535_4689_5058" class="figma-component figma-container figma-name-text-container" data-figma-id="I1:15535;4689:5058" data-figma-name="Text Container" data-figma-type="FRAME">
          <span id="I1_15535_4689_5059" class="figma-component figma-text figma-name-label" data-figma-id="I1:15535;4689:5059" data-figma-name="Label" data-figma-type="TEXT">Cliente</span>
          <div id="I1_15535_4689_5060" class="figma-component figma-container figma-name-content" data-figma-id="I1:15535;4689:5060" data-figma-name="Content" data-figma-type="FRAME">
            <span id="I1_15535_4689_5061" class="figma-component figma-text figma-name-prefix" data-figma-id="I1:15535;4689:5061" data-figma-name="PREFIX" data-figma-type="TEXT">PREFIX</span>
            <span id="I1_15535_4689_5062" class="figma-component figma-text figma-name-label" data-figma-id="I1:15535;4689:5062" data-figma-name="Label" data-figma-type="TEXT">Cliente</span>
            <span id="I1_15535_4689_5063" class="figma-component figma-text figma-name-content" data-figma-id="I1:15535;4689:5063" data-figma-name="Content" data-figma-type="TEXT">Content</span>
            <span id="I1_15535_4689_5064" class="figma-component figma-text figma-name-suffix" data-figma-id="I1:15535;4689:5064" data-figma-name="SUFFIX" data-figma-type="TEXT">SUFFIX</span>
          </div>
        </div>
        <div id="I1_15535_4689_5065" class="figma-component figma-container figma-name-validation-icon" data-figma-id="I1:15535;4689:5065" data-figma-name="Validation Icon" data-figma-type="FRAME">
          <div id="I1_15535_4689_5066" class="figma-component figma-component figma-name-ui-icon-placeholder" data-figma-id="I1:15535;4689:5066" data-figma-name="ui-icon-placeholder" data-figma-type="INSTANCE">
            <div id="I1_15535_4689_5066_23_8" class="figma-component figma-image figma-name-icon" data-figma-id="I1:15535;4689:5066;23:8" data-figma-name="icon" data-figma-type="VECTOR"></div>
          </div>
        </div>
        <div id="I1_15535_4689_5067" class="figma-component figma-component figma-name-chevron" data-figma-id="I1:15535;4689:5067" data-figma-name=".Chevron" data-figma-type="INSTANCE">
          <div id="I1_15535_4689_5067_4689_9290" class="figma-component figma-component figma-name-ui-chevron-down" data-figma-id="I1:15535;4689:5067;4689:9290" data-figma-name="ui-chevron-down" data-figma-type="INSTANCE">
            <div id="I1_15535_4689_5067_4689_9290_60_5153" class="figma-component figma-container figma-name-clip-path-group" data-figma-id="I1:15535;4689:5067;4689:9290;60:5153" data-figma-name="Clip path group" data-figma-type="GROUP">
              <div id="I1_15535_4689_5067_4689_9290_60_5154" class="figma-component figma-container figma-name-clip-path-51" data-figma-id="I1:15535;4689:5067;4689:9290;60:5154" data-figma-name="clip-path-51" data-figma-type="GROUP">
                <div id="I1_15535_4689_5067_4689_9290_60_5155" class="figma-component figma-shape figma-name-ret-ngulo-112764" data-figma-id="I1:15535;4689:5067;4689:9290;60:5155" data-figma-name="Retângulo 112764" data-figma-type="VECTOR"></div>
              </div>
              <div id="I1_15535_4689_5067_4689_9290_60_5156" class="figma-component figma-container figma-name-grupo-de-m-scara-122415" data-figma-id="I1:15535;4689:5067;4689:9290;60:5156" data-figma-name="Grupo de máscara 122415" data-figma-type="GROUP">
                <div id="I1_15535_4689_5067_4689_9290_60_5157" class="figma-component figma-shape figma-name-caminho-714383" data-figma-id="I1:15535;4689:5067;4689:9290;60:5157" data-figma-name="Caminho 714383" data-figma-type="VECTOR"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="I1_15535_4689_5068" class="figma-component figma-component figma-name-helper" data-figma-id="I1:15535;4689:5068" data-figma-name=".Helper" data-figma-type="INSTANCE">
        <span id="I1_15535_4689_5068_4382_1607" class="figma-component figma-text figma-name-helper-text" data-figma-id="I1:15535;4689:5068;4382:1607" data-figma-name="Helper Text" data-figma-type="TEXT">Helper Text</span>
        <span id="I1_15535_4689_5068_4382_1608" class="figma-component figma-text figma-name-16-40" data-figma-id="I1:15535;4689:5068;4382:1608" data-figma-name="16/40" data-figma-type="TEXT">16/40</span>
      </div>
    </div>
  </div>
  <div id="figma_1_15536" class="figma-component figma-container figma-name-actions" data-figma-id="1:15536" data-figma-name="actions" data-figma-type="FRAME">
    <div id="figma_1_15537" class="figma-component figma-component figma-name-button" data-figma-id="1:15537" data-figma-name="Button" data-figma-type="INSTANCE">
      <div id="I1_15537_2104_1535" class="figma-component figma-component figma-name-icon" data-figma-id="I1:15537;2104:1535" data-figma-name="🚺 Icon" data-figma-type="INSTANCE">
        <div id="I1_15537_2104_1535_60_5079" class="figma-component figma-shape figma-name-caminho-4704740" data-figma-id="I1:15537;2104:1535;60:5079" data-figma-name="Caminho 4704740" data-figma-type="VECTOR"></div>
      </div>
      <span id="I1_15537_2104_1536" class="figma-component figma-text figma-name-label" data-figma-id="I1:15537;2104:1536" data-figma-name="✏️ Label" data-figma-type="TEXT">Buscar</span>
    </div>
    <div id="figma_1_15538" class="figma-component figma-component figma-name-button-text" data-figma-id="1:15538" data-figma-name="Button Text" data-figma-type="INSTANCE">
      <span id="I1_15538_179_1045" class="figma-component figma-text figma-name-label" data-figma-id="I1:15538;179:1045" data-figma-name="✏️ Label" data-figma-type="TEXT">Limpar</span>
    </div>
  </div>
</div>
</body>
</html>