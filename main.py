#!/usr/bin/env python3
"""
Script principal para o sistema Figma to Code Converter.

Este script permite:
- Baixar dados da API do Figma
- Processar arquivos JSON locais
- Gerar código HTML/CSS
- Executar todo o pipeline de conversão
"""

import argparse
import logging
import os
import sys
from pathlib import Path

from src.parsers.figma_api_client import FigmaApiClient, FigmaApiConfig
from src.parsers.figma_parser import FigmaParser
from src.extractors.component_extractor import ComponentExtractor
from src.generators.html_generator import HTMLGenerator
from src.storage.data_storage import DataStorage

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def download_from_api(args):
    """
    Baixa dados da API do Figma.
    
    Args:
        args: Argumentos da linha de comando
    """
    # Verificar token
    token = args.token or os.getenv('FIGMA_TOKEN')
    if not token:
        logger.error("Token do Figma não fornecido. Use --token ou defina FIGMA_TOKEN")
        return False
    
    # Extrair file_key da URL se necessário
    if args.file_key.startswith('http'):
        try:
            file_key = FigmaApiClient.extract_file_key_from_url(args.file_key)
            logger.info(f"File key extraída da URL: {file_key}")
        except ValueError as e:
            logger.error(f"Erro ao extrair file key: {e}")
            return False
    else:
        file_key = args.file_key
    
    # Criar cliente da API
    config = FigmaApiConfig(token=token)
    client = FigmaApiClient(config)
    
    try:
        # Baixar dados
        if args.node_ids:
            # Baixar nodes específicos
            node_ids = args.node_ids.split(',')
            data = client.get_file_nodes(file_key, node_ids)
            filename = f"figma_nodes_{file_key}.json"
        else:
            # Baixar arquivo completo
            data = client.get_file(file_key)
            filename = f"figma_file_{file_key}.json"
        
        # Salvar dados
        output_path = Path("data/raw") / filename
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        client.save_data_to_file(data, str(output_path))
        logger.info(f"Dados salvos em: {output_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"Erro ao baixar dados: {e}")
        return False


def parse_file(args):
    """
    Processa um arquivo JSON do Figma.
    
    Args:
        args: Argumentos da linha de comando
    """
    input_path = Path(args.input_file)
    
    if not input_path.exists():
        logger.error(f"Arquivo não encontrado: {input_path}")
        return False
    
    try:
        # Criar parser e processar arquivo
        parser = FigmaParser()
        project = parser.load_file(str(input_path))

        # Extrair componentes
        extractor = ComponentExtractor(project)
        components = extractor.extract_all_components()

        # Exibir resumo do projeto
        summary = parser.get_project_summary()
        extraction_summary = extractor.get_extraction_summary()

        print("\n" + "="*50)
        print("RESUMO DO PROJETO FIGMA")
        print("="*50)

        for key, value in summary.items():
            if isinstance(value, dict):
                print(f"\n{key.upper()}:")
                for sub_key, sub_value in value.items():
                    print(f"  {sub_key}: {sub_value}")
            else:
                print(f"{key}: {value}")

        print("\n" + "="*50)
        print("RESUMO DA EXTRAÇÃO DE COMPONENTES")
        print("="*50)

        for key, value in extraction_summary.items():
            if isinstance(value, dict):
                print(f"\n{key.upper()}:")
                for sub_key, sub_value in value.items():
                    print(f"  {sub_key}: {sub_value}")
            else:
                print(f"{key}: {value}")

        # Salvar dados processados se solicitado
        if args.output:
            output_path = Path(args.output)
            output_path.parent.mkdir(parents=True, exist_ok=True)

            import json

            # Preparar dados para salvamento
            components_data = {}
            for comp_id, comp in components.items():
                components_data[comp_id] = {
                    'id': comp.id,
                    'name': comp.name,
                    'type': comp.type.value,
                    'original_type': comp.original_type,
                    'html_tag': comp.html_tag,
                    'css_classes': comp.css_classes,
                    'css_properties': comp.css_properties,
                    'content': comp.content,
                    'properties': comp.properties,
                    'parent_id': comp.parent_id,
                    'children_ids': [child.id for child in comp.children]
                }

            processed_data = {
                'project_summary': summary,
                'extraction_summary': extraction_summary,
                'components': components_data
            }

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(processed_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Dados processados salvos em: {output_path}")

        return True
        
    except Exception as e:
        logger.error(f"Erro ao processar arquivo: {e}")
        return False


def generate_code(args):
    """
    Gera código a partir de dados processados.

    Args:
        args: Argumentos da linha de comando
    """
    input_path = Path(args.input)

    if not input_path.exists():
        logger.error(f"Arquivo de entrada não encontrado: {input_path}")
        return False

    try:
        # Carregar dados processados
        storage = DataStorage()
        components_data = storage.load_components_json(input_path.name)

        # Gerar código baseado no formato
        if args.format == 'html':
            generator = HTMLGenerator(components_data)
            html_content = generator.generate_html()

            # Salvar HTML
            output_path = Path(args.output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            html_file = output_path / "index.html"
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(html_content)

            logger.info(f"HTML gerado e salvo em: {html_file}")

            # Exibir estatísticas
            stats = generator.get_generation_stats()
            print("\n" + "="*50)
            print("ESTATÍSTICAS DA GERAÇÃO HTML")
            print("="*50)

            for key, value in stats.items():
                if isinstance(value, dict):
                    print(f"\n{key.upper()}:")
                    for sub_key, sub_value in value.items():
                        print(f"  {sub_key}: {sub_value}")
                else:
                    print(f"{key}: {value}")

        elif args.format in ['react', 'angular']:
            logger.info(f"Geração para {args.format} ainda não implementada")
            logger.info("Será implementada nas próximas etapas do projeto")

        return True

    except Exception as e:
        logger.error(f"Erro ao gerar código: {e}")
        return False


def main():
    """Função principal do script."""
    parser = argparse.ArgumentParser(
        description="Figma to Code Converter",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Exemplos de uso:

  # Baixar dados da API do Figma
  python main.py download --file-key EWVeXlNAwQlwOsej7XstyO --token seu_token

  # Baixar nodes específicos
  python main.py download --file-key EWVeXlNAwQlwOsej7XstyO --node-ids "1:15527,1:15528"

  # Processar arquivo local
  python main.py parse data/raw/figma_api_response.json

  # Processar e salvar dados processados
  python main.py parse data/raw/figma_api_response.json --output data/processed/summary.json

  # Gerar código HTML (futuro)
  python main.py generate --input data/processed/summary.json --format html
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Comandos disponíveis')
    
    # Comando download
    download_parser = subparsers.add_parser('download', help='Baixar dados da API do Figma')
    download_parser.add_argument('--file-key', required=True,
                                help='Chave do arquivo ou URL completa do Figma')
    download_parser.add_argument('--token',
                                help='Token da API do Figma (ou use FIGMA_TOKEN env var)')
    download_parser.add_argument('--node-ids',
                                help='IDs dos nodes específicos (separados por vírgula)')
    
    # Comando parse
    parse_parser = subparsers.add_parser('parse', help='Processar arquivo JSON do Figma')
    parse_parser.add_argument('input_file', help='Caminho para o arquivo JSON')
    parse_parser.add_argument('--output', help='Arquivo de saída para dados processados')
    
    # Comando generate
    generate_parser = subparsers.add_parser('generate', help='Gerar código')
    generate_parser.add_argument('--input', required=True,
                                help='Arquivo de dados processados')
    generate_parser.add_argument('--format', choices=['html', 'react', 'angular'],
                                default='html', help='Formato de saída')
    generate_parser.add_argument('--output-dir', default='data/output',
                                help='Diretório de saída')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Executar comando
    success = False
    
    if args.command == 'download':
        success = download_from_api(args)
    elif args.command == 'parse':
        success = parse_file(args)
    elif args.command == 'generate':
        success = generate_code(args)
    
    if success:
        logger.info("Comando executado com sucesso!")
    else:
        logger.error("Erro na execução do comando")
        sys.exit(1)


if __name__ == "__main__":
    main()
