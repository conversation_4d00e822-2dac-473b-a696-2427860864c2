{"name": "SAT CONTABILIDADE", "lastModified": "2025-06-13T15:15:04Z", "thumbnailUrl": "https://s3-alpha.figma.com/thumbnails/3225883c-99bb-4b7c-b8f0-79d0ca22babd?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCZGWUDXPX%2F20250622%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250622T120000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=b2675abadff1b3b29601e894a6208427760e3473c5e6bba6ab1841d6fe64cc35", "version": "2230348733239513295", "role": "viewer", "editorType": "figma", "linkAccess": "inherit", "nodes": {"1:15527": {"document": {"id": "1:15527", "name": "Box filtros", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "1:15528", "name": "box", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "1:15529", "name": "Rectangle 9711", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 2954.0, "y": 3314.0, "width": 1280.0, "height": 192.0}, "absoluteRenderBounds": {"x": 2950.0, "y": 3312.0, "width": 1288.0, "height": 200.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "effects": [{"type": "DROP_SHADOW", "visible": true, "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.1599999964237213}, "blendMode": "NORMAL", "offset": {"x": 0.0, "y": 2.0}, "radius": 4.0, "showShadowBehindNode": false}], "styles": {"effect": "1:373"}, "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 2954.0, "y": 3314.0, "width": 1280.0, "height": 192.0}, "absoluteRenderBounds": {"x": 2950.0, "y": 3312.0, "width": 1288.0, "height": 200.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutPositioning": "ABSOLUTE", "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "1:15530", "name": "linha filtro 7", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "20:12635", "name": "Input Container", "type": "FRAME", "scrollBehavior": "SCROLLS", "boundVariables": {"rectangleCornerRadii": {"RECTANGLE_TOP_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_TOP_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}}, "fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}], "strokes": [{"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}]}, "children": [{"id": "20:12636", "name": "Text Container", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "20:12637", "name": "Label", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:ffeb962cd506a50417a01c31fab47a24720b8d23/2423:30"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:338ed5dfda5929a104fb944d57f2f484b17d75c8/2051:11"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.27843138575553894, "g": 0.2823529541492462, "b": 0.2980392277240753, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:ffeb962cd506a50417a01c31fab47a24720b8d23/2423:30"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 2978.0, "y": 3350.0, "width": 304.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Cliente", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 12.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 109.73937225341797, "lineHeightPercentFontSize": 133.3333282470703, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:15"}, "effects": [], "interactions": []}, {"id": "20:12638", "name": "Content", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "20:12639", "name": "Cliente", "type": "TEXT", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 2990.0, "y": 3362.0, "width": 685.0, "height": 20.0}, "absoluteRenderBounds": {"x": 2990.847900390625, "y": 3366.095947265625, "width": 51.739990234375, "height": 12.080078125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "characters": "Cliente", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "20:12640", "name": "Content", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3036.0, "y": 3358.0, "width": 258.0, "height": 24.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Content", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "overflowDirection": "HORIZONTAL_SCROLLING", "layoutMode": "HORIZONTAL", "itemSpacing": 4.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "MAX", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2990.0, "y": 3358.0, "width": 685.0, "height": 24.0}, "absoluteRenderBounds": {"x": 2990.0, "y": 3358.0, "width": 685.0, "height": 24.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "minHeight": 24.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "primaryAxisAlignItems": "CENTER", "paddingLeft": 12.0, "paddingRight": 12.0, "paddingTop": 4.0, "paddingBottom": 4.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3346.0, "width": 709.0, "height": 48.0}, "absoluteRenderBounds": {"x": 2978.0, "y": 3346.0, "width": 709.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 1.0, "minHeight": 48.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "FILL", "effects": [], "interactions": []}, {"id": "20:12641", "name": "Actions", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "20:12642", "name": ".Icon", "visible": false, "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}]}, "componentId": "1:2247", "componentProperties": {"🚺 Icon#11276:0": {"value": "1:2244", "type": "INSTANCE_SWAP", "preferredValues": [{"type": "COMPONENT", "key": "768bd504226188795aaa12a06786d0dc6eada606"}, {"type": "COMPONENT", "key": "aa63fc8212ba03865214433114f5e3c6cf14d1e2"}]}, "State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "20:12642", "overriddenFields": ["height", "width"]}], "children": [{"id": "I20:12642;12258:2410", "name": "component-search", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"mainComponent": "🚺 Icon#11276:0"}, "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:2244", "overrides": [], "children": [{"id": "I20:12642;12258:2410;2051:95", "name": "component-search-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3703.557373046875, "y": 3362.5634765625, "width": 14.872543334960938, "height": 14.87879753112793}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3701.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3687.0, "y": 3346.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "1:2249", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:2249", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}, {"trigger": {"type": "ON_PRESS"}, "actions": [{"type": "NODE", "destinationId": "1:2253", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}, {"id": "20:12643", "name": "Divider", "type": "RECTANGLE", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:9b02e0e13f264abcc3e3ebfc4a4b002e2a9fcc4d/2423:20"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.8509804010391235, "g": 0.8627451062202454, "b": 0.8666666746139526, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:9b02e0e13f264abcc3e3ebfc4a4b002e2a9fcc4d/2423:20"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3687.0, "y": 3354.0, "width": 1.0, "height": 32.0}, "absoluteRenderBounds": {"x": 3687.0, "y": 3354.0, "width": 1.0, "height": 32.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "20:12644", "name": ".Icon", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}]}, "componentId": "1:2247", "componentProperties": {"🚺 Icon#11276:0": {"value": "1:2244", "type": "INSTANCE_SWAP", "preferredValues": [{"type": "COMPONENT", "key": "768bd504226188795aaa12a06786d0dc6eada606"}, {"type": "COMPONENT", "key": "aa63fc8212ba03865214433114f5e3c6cf14d1e2"}]}, "State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "20:12644", "overriddenFields": ["height", "width"]}], "children": [{"id": "I20:12644;12258:2410", "name": "component-search", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"mainComponent": "🚺 Icon#11276:0"}, "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:2244", "overrides": [], "children": [{"id": "I20:12644;12258:2410;2051:95", "name": "component-search-path", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3704.557373046875, "y": 3362.5634765625, "width": 14.872543334960938, "height": 14.87879753112793}, "absoluteRenderBounds": {"x": 3704.557373046875, "y": 3362.5634765625, "width": 14.87255859375, "height": 14.87890625}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3702.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": {"x": 3702.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3688.0, "y": 3346.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": {"x": 3688.0, "y": 3346.0, "width": 48.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "1:2249", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:2249", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}, {"trigger": {"type": "ON_PRESS"}, "actions": [{"type": "NODE", "destinationId": "1:2253", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3687.0, "y": 3346.0, "width": 49.0, "height": 48.0}, "absoluteRenderBounds": {"x": 3687.0, "y": 3346.0, "width": 49.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "FILL", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.6549019813537598, "g": 0.658823549747467, "b": 0.6745098233222961, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}}}], "cornerRadius": 2.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "individualStrokeWeights": {"top": 0.0, "right": 0.0, "bottom": 2.0, "left": 0.0}, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "layoutMode": "HORIZONTAL", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3346.0, "width": 758.0, "height": 48.0}, "absoluteRenderBounds": {"x": 2978.0, "y": 3346.0, "width": 758.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "1:15531", "name": "Text Field Calendar", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:1052", "exposedInstances": ["I1:15531;9228:53872", "I1:15531;9228:53873"], "componentProperties": {"✏️ Label#3141:7": {"value": "Fechamento de exercício", "type": "TEXT"}, "✏️ Date#3141:8": {"value": "05/08/2024", "type": "TEXT"}, "✏️ Hint Text#4380:0": {"value": "00/00/0000", "type": "TEXT"}, "✏️ Range#9228:0": {"value": "05/08/2024 - 16/08/2024", "type": "TEXT"}, "Type": {"value": "Desktop", "type": "VARIANT", "boundVariables": {}}, "Is Range": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Hovered": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Focused": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Filled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Helper Text": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Validation": {"value": "Non-validation", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "1:15531", "overriddenFields": ["height", "transitionDuration", "transitionEasing", "transitionNodeID", "width"]}], "children": [{"id": "I1:15531;9228:53861", "name": "Input Container", "type": "FRAME", "scrollBehavior": "SCROLLS", "boundVariables": {"rectangleCornerRadii": {"RECTANGLE_TOP_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_TOP_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}}, "fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}], "strokes": [{"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}]}, "children": [{"id": "I1:15531;9228:53862", "name": ".Icon", "visible": false, "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:392", "componentProperties": {"🚺 Icon#4371:0": {"value": "1:226", "type": "INSTANCE_SWAP", "preferredValues": []}, "State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "True", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Type": {"value": "Leading Icon", "type": "VARIANT", "boundVariables": {}}}, "overrides": [], "children": [{"id": "I1:15531;9228:53862;4695:16145", "name": "ui-icon-placeholder", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"mainComponent": "🚺 Icon#4371:0"}, "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:226", "overrides": [], "children": [{"id": "I1:15531;9228:53862;4695:16145;60:5079", "name": "Caminho 4704740", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:b2117e745c34812eb942df930af20e835909e138/2423:2"}]}, "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.6000000238418579, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:b2117e745c34812eb942df930af20e835909e138/2423:2"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3784.5, "y": 3362.5, "width": 15.0, "height": 15.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3782.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3768.0, "y": 3346.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "I1:15531;9228:53863", "name": "Text Container", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15531;9228:53864", "name": "Label", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#3141:7"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:ffeb962cd506a50417a01c31fab47a24720b8d23/2423:30"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:338ed5dfda5929a104fb944d57f2f484b17d75c8/2051:11"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.27843138575553894, "g": 0.2823529541492462, "b": 0.2980392277240753, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:ffeb962cd506a50417a01c31fab47a24720b8d23/2423:30"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3768.0, "y": 3350.0, "width": 144.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Fechamento de exercício", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 12.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 109.73937225341797, "lineHeightPercentFontSize": 133.3333282470703, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:15"}, "effects": [], "interactions": []}, {"id": "I1:15531;9228:53865", "name": "Content", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15531;9228:53866", "name": "PREFIX", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3780.0, "y": 3358.0, "width": 54.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "PREFIX", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15531;9228:53867", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#3141:7"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3780.0, "y": 3362.0, "width": 370.0, "height": 20.0}, "absoluteRenderBounds": {"x": 3781.327880859375, "y": 3366.095947265625, "width": 185.447998046875, "height": 12.080078125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "characters": "Fechamento de exercício", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15531;9228:53868", "name": "05/08/2024", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Date#3141:8"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3826.0, "y": 3358.0, "width": 90.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "05/08/2024", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15531;9228:53869", "name": "SUFFIX", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 4013.0, "y": 3358.0, "width": 55.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "SUFFIX", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 16.0, "textAlignHorizontal": "RIGHT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 4.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "MAX", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3780.0, "y": 3358.0, "width": 370.0, "height": 24.0}, "absoluteRenderBounds": {"x": 3780.0, "y": 3358.0, "width": 370.0, "height": 24.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "minHeight": 24.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "primaryAxisAlignItems": "CENTER", "paddingLeft": 12.0, "paddingRight": 12.0, "paddingTop": 4.0, "paddingBottom": 4.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3768.0, "y": 3346.0, "width": 394.0, "height": 48.0}, "absoluteRenderBounds": {"x": 3768.0, "y": 3346.0, "width": 394.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 1.0, "minHeight": 48.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "FILL", "effects": [], "interactions": []}, {"id": "I1:15531;9228:53870", "name": "Validation Icon", "visible": false, "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15531;9228:53871", "name": "ui-icon-placeholder", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:f03c8d071d2e55cd6de7785784c7b0e57fef0a9e/225:27"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:f03c8d071d2e55cd6de7785784c7b0e57fef0a9e/225:27"}}}, "componentId": "1:408", "overrides": [], "children": [{"id": "I1:15531;9228:53871;23:8", "name": "icon", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:5853c9a3e9b4431d1801924a826f6a4518f03ae4/2423:44"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.03529411926865578, "g": 0.6705882549285889, "b": 0.27843138575553894, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:5853c9a3e9b4431d1801924a826f6a4518f03ae4/2423:44"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 4138.0, "y": 3364.0, "width": 12.0, "height": 12.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4136.0, "y": 3362.0, "width": 16.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 8.0, "paddingLeft": 16.0, "paddingRight": 16.0, "paddingTop": 16.0, "paddingBottom": 16.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 4120.0, "y": 3346.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "I1:15531;13260:2834", "name": "Button Text Container", "visible": false, "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15531;13260:2835", "name": "Button Text", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"rectangleCornerRadii": {"RECTANGLE_TOP_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:51eec62416fe080bac487f06b046716584be44a8/225:66"}, "RECTANGLE_TOP_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:51eec62416fe080bac487f06b046716584be44a8/225:66"}, "RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:51eec62416fe080bac487f06b046716584be44a8/225:66"}, "RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:51eec62416fe080bac487f06b046716584be44a8/225:66"}}}, "componentId": "1:439", "componentProperties": {"✏️ Label#212:147": {"value": "Label", "type": "TEXT"}, "🚺 Icon#212:98": {"value": "1:226", "type": "INSTANCE_SWAP", "preferredValues": [{"type": "COMPONENT", "key": "56d82bc168ff974df8671c85f06b8277ca3acc7a"}]}, "State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Leading-icon": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Trailling-icon": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "No Background": {"value": "True", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}}, "overrides": [], "children": [{"id": "I1:15531;13260:2835;188:48", "name": "✏️ Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#212:147"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:58614f24f9dd4a3009c460d0c5b3239a1deb0f91/2051:5"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.23137255012989044, "g": 0.4117647111415863, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3986.0, "y": 3362.0, "width": 38.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "maxHeight": 16.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "Label", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 117.57789611816406, "lineHeightPercentFontSize": 142.85714721679688, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:412"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "cornerRadius": 999.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 10.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3986.0, "y": 3362.0, "width": 38.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "maxHeight": 24.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "transitionNodeID": "1:539", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:539", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}, {"trigger": {"type": "ON_PRESS"}, "actions": [{"type": "NODE", "destinationId": "1:543", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "itemSpacing": 8.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 8.0, "paddingRight": 8.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3978.0, "y": 3346.0, "width": 54.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "I1:15531;9228:53872", "name": ".Calendar <PERSON><PERSON>", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:556", "isExposedInstance": true, "componentProperties": {"State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}}, "overrides": [], "children": [{"id": "I1:15531;9228:53872;9228:55987", "name": "ui-calendar", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:549", "overrides": [], "children": [{"id": "I1:15531;9228:53872;9228:55987;60:4919", "name": "Clip path group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15531;9228:53872;9228:55987;60:4920", "name": "clip-path-54", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15531;9228:53872;9228:55987;60:4921", "name": "Retângulo 99097", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 4176.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": {"x": 4176.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4176.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "isMask": true, "isMaskOutline": true, "maskType": "VECTOR", "interactions": []}, {"id": "I1:15531;9228:53872;9228:55987;60:4922", "name": "Grupo de máscara 172", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15531;9228:53872;9228:55987;60:4923", "name": "Caminho 77394", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.23137255012989044, "g": 0.4117647111415863, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 4176.34375, "y": 3360.271240234375, "width": 19.31169319152832, "height": 19.452499389648438}, "absoluteRenderBounds": {"x": 4176.34375, "y": 3360.271240234375, "width": 19.3115234375, "height": 19.452392578125}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4176.34375, "y": 3360.271240234375, "width": 19.31169319152832, "height": 19.452499389648438}, "absoluteRenderBounds": {"x": 4176.34375, "y": 3360.271240234375, "width": 19.31169319152832, "height": 19.452499389648438}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4176.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": {"x": 4176.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4176.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": {"x": 4176.0, "y": 3360.0, "width": 20.0, "height": 20.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 4162.0, "y": 3346.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": {"x": 4162.0, "y": 3346.0, "width": 48.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FILL", "effects": [], "transitionNodeID": "1:558", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:558", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}, {"trigger": {"type": "ON_PRESS"}, "actions": [{"type": "NODE", "destinationId": "1:562", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}}]}, {"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": "1:1308", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.6549019813537598, "g": 0.658823549747467, "b": 0.6745098233222961, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}}}], "cornerRadius": 2.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "individualStrokeWeights": {"top": 0.0, "right": 0.0, "bottom": 2.0, "left": 0.0}, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "layoutMode": "HORIZONTAL", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3768.0, "y": 3346.0, "width": 442.0, "height": 48.0}, "absoluteRenderBounds": {"x": 3768.0, "y": 3346.0, "width": 442.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "I1:15531;9228:53873", "name": ".Helper", "visible": false, "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:568", "isExposedInstance": true, "componentProperties": {"✏️ Counter#4383:220": {"value": "16/40", "type": "TEXT"}, "✏️ Helper Text#4383:211": {"value": "Helper Text", "type": "TEXT"}, "Is DIsabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Counter": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Helper Text": {"value": "True", "type": "VARIANT", "boundVariables": {}}}, "overrides": [], "children": [{"id": "I1:15531;9228:53873;4382:1607", "name": "Helper Text", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Helper Text#4383:211"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:570f1bec6f0487f9c658e96b48ef9ed29a11e7ad/2051:7"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3768.0, "y": 3398.0, "width": 312.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "characters": "Helper Text", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "HEIGHT", "fontSize": 10.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 131.68724060058594, "lineHeightPercentFontSize": 160.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:566"}, "effects": [], "interactions": []}, {"id": "I1:15531;9228:53873;4382:1608", "name": "16/40", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Counter#4383:220"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:570f1bec6f0487f9c658e96b48ef9ed29a11e7ad/2051:7"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 4053.0, "y": 3398.0, "width": 27.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "16/40", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 10.0, "textAlignHorizontal": "RIGHT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 131.68724060058594, "lineHeightPercentFontSize": 160.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:566"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 16.0, "primaryAxisSizingMode": "FIXED", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3768.0, "y": 3398.0, "width": 312.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 4.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3768.0, "y": 3346.0, "width": 442.0, "height": 48.0}, "absoluteRenderBounds": {"x": 3768.0, "y": 3346.0, "width": 442.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "transitionNodeID": "1:1180", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:1180", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}, {"trigger": {"type": "ON_CLICK"}, "actions": [{"type": "NODE", "destinationId": "1:1308", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 32.0, "primaryAxisSizingMode": "FIXED", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3346.0, "width": 1232.0, "height": 48.0}, "absoluteRenderBounds": {"x": 2978.0, "y": 3346.0, "width": 1232.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "1:15533", "name": "linha filtro 8", "visible": false, "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "1:15534", "name": "Text Field Select", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:6337", "exposedInstances": ["I1:15534;4689:5057", "I1:15534;4689:5067", "I1:15534;4689:5068"], "componentProperties": {"✏️ Content#3141:8": {"value": "Content", "type": "TEXT"}, "✏️ Label#3141:7": {"value": "Demonstração Financeira", "type": "TEXT"}, "✏️ Hint Text#4380:0": {"value": "Hint Text", "type": "TEXT"}, "✏️ Selected Itens#4709:0": {"value": "1 selecionado(s)", "type": "TEXT"}, "✏️ Prefix#4382:97": {"value": "PREFIX", "type": "TEXT"}, "Device": {"value": "Desktop", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Selected": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Hovered": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Focused": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Filled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Leading-icon": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Helper Text": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Prefix": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Validation": {"value": "Non-validation", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "1:15534", "overriddenFields": ["height", "transitionDuration", "transitionEasing", "transitionNodeID", "width"]}, {"id": "I1:15534;4689:5067", "overriddenFields": ["visible"]}], "children": [{"id": "I1:15534;4689:5056", "name": "Input Container", "type": "FRAME", "scrollBehavior": "SCROLLS", "boundVariables": {"rectangleCornerRadii": {"RECTANGLE_TOP_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_TOP_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}}, "fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}], "strokes": [{"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}]}, "children": [{"id": "I1:15534;4689:5057", "name": ".Icon", "visible": false, "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:5877", "isExposedInstance": true, "componentProperties": {"🚺 Icon#4371:0": {"value": "1:5860", "type": "INSTANCE_SWAP", "preferredValues": []}, "State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "True", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Type": {"value": "Leading Icon", "type": "VARIANT", "boundVariables": {}}}, "overrides": [], "children": [{"id": "I1:15534;4689:5057;4695:16145", "name": "ui-icon-placeholder", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"mainComponent": "🚺 Icon#4371:0"}, "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:5860", "overrides": [], "children": [{"id": "I1:15534;4689:5057;4695:16145;60:5075", "name": "Clip path group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5057;4695:16145;60:5076", "name": "clip-path-52", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5057;4695:16145;60:5077", "name": "Retângulo 114899", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 2992.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 2992.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "isMask": true, "isMaskOutline": true, "maskType": "VECTOR", "interactions": []}, {"id": "I1:15534;4689:5057;4695:16145;60:5078", "name": "Grupo de máscara 122448", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5057;4695:16145;60:5079", "name": "Caminho 4704740", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:b2117e745c34812eb942df930af20e835909e138/2423:2"}]}, "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.6000000238418579, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:b2117e745c34812eb942df930af20e835909e138/2423:2"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 2994.5, "y": 3444.5, "width": 15.0, "height": 15.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 2994.5, "y": 3444.5, "width": 15.0, "height": 15.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 2992.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 2992.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3428.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "I1:15534;4689:5058", "name": "Text Container", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5059", "name": "Label", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#3141:7"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:ffeb962cd506a50417a01c31fab47a24720b8d23/2423:30"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:338ed5dfda5929a104fb944d57f2f484b17d75c8/2051:11"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.27843138575553894, "g": 0.2823529541492462, "b": 0.2980392277240753, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:ffeb962cd506a50417a01c31fab47a24720b8d23/2423:30"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 2978.0, "y": 3432.0, "width": 147.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Demonstração Financeira", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 12.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 109.73937225341797, "lineHeightPercentFontSize": 133.3333282470703, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:15"}, "effects": [], "interactions": []}, {"id": "I1:15534;4689:5060", "name": "Content", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5061", "name": "PREFIX", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Prefix#4382:97"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 2990.0, "y": 3440.0, "width": 54.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "PREFIX", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15534;4689:5062", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#3141:7"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 2990.0, "y": 3444.0, "width": 370.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "characters": "Demonstração Financeira", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15534;4689:5063", "name": "Content", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Content#3141:8"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3036.0, "y": 3440.0, "width": 61.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Content", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15534;4689:5064", "name": "SUFFIX", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3223.0, "y": 3440.0, "width": 55.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "SUFFIX", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 16.0, "textAlignHorizontal": "RIGHT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 4.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "MAX", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2990.0, "y": 3440.0, "width": 370.0, "height": 24.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "minHeight": 24.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "primaryAxisAlignItems": "CENTER", "paddingLeft": 12.0, "paddingRight": 12.0, "paddingTop": 4.0, "paddingBottom": 4.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3428.0, "width": 394.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 1.0, "minHeight": 48.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "FILL", "effects": [], "interactions": []}, {"id": "I1:15534;4689:5065", "name": "Validation Icon", "visible": false, "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5066", "name": "ui-icon-placeholder", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:f03c8d071d2e55cd6de7785784c7b0e57fef0a9e/225:27"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:f03c8d071d2e55cd6de7785784c7b0e57fef0a9e/225:27"}}}, "componentId": "1:408", "overrides": [], "children": [{"id": "I1:15534;4689:5066;23:8", "name": "icon", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:5853c9a3e9b4431d1801924a826f6a4518f03ae4/2423:44"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.03529411926865578, "g": 0.6705882549285889, "b": 0.27843138575553894, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:5853c9a3e9b4431d1801924a826f6a4518f03ae4/2423:44"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3348.0, "y": 3446.0, "width": 12.0, "height": 12.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3346.0, "y": 3444.0, "width": 16.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 8.0, "paddingLeft": 16.0, "paddingRight": 16.0, "paddingTop": 16.0, "paddingBottom": 16.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3330.0, "y": 3428.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "I1:15534;4689:5067", "name": ".Ch<PERSON>ron", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:5904", "isExposedInstance": true, "componentProperties": {"State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Selected": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Type": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I1:15534;4689:5067", "overriddenFields": ["visible"]}], "children": [{"id": "I1:15534;4689:5067;4689:9290", "name": "ui-chevron-down", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:5887", "overrides": [], "children": [{"id": "I1:15534;4689:5067;4689:9290;60:5153", "name": "Clip path group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5067;4689:9290;60:5154", "name": "clip-path-51", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5067;4689:9290;60:5155", "name": "Retângulo 112764", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3386.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3386.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "isMask": true, "isMaskOutline": true, "maskType": "VECTOR", "interactions": []}, {"id": "I1:15534;4689:5067;4689:9290;60:5156", "name": "Grupo de máscara 122415", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15534;4689:5067;4689:9290;60:5157", "name": "Caminho 714383", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.23137255012989044, "g": 0.4117647111415863, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3388.49951171875, "y": 3447.624755859375, "width": 15.000953674316406, "height": 8.749751091003418}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3388.49951171875, "y": 3447.624755859375, "width": 15.000953674316406, "height": 8.749751091003418}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3386.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3386.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3372.0, "y": 3428.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "1:5912", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:5912", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}, {"trigger": {"type": "ON_PRESS"}, "actions": [{"type": "NODE", "destinationId": "1:5928", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.6549019813537598, "g": 0.658823549747467, "b": 0.6745098233222961, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}}}], "cornerRadius": 2.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "individualStrokeWeights": {"top": 0.0, "right": 0.0, "bottom": 2.0, "left": 0.0}, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "layoutMode": "HORIZONTAL", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "MAX", "strokesIncludedInLayout": true, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3428.0, "width": 442.0, "height": 50.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "I1:15534;4689:5068", "name": ".Helper", "visible": false, "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:5981", "isExposedInstance": true, "componentProperties": {"✏️ Counter#4383:220": {"value": "16/40", "type": "TEXT"}, "✏️ Helper Text#4383:211": {"value": "Helper Text", "type": "TEXT"}, "Is DIsabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Counter": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Helper Text": {"value": "True", "type": "VARIANT", "boundVariables": {}}}, "overrides": [], "children": [{"id": "I1:15534;4689:5068;4382:1607", "name": "Helper Text", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Helper Text#4383:211"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:570f1bec6f0487f9c658e96b48ef9ed29a11e7ad/2051:7"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 2978.0, "y": 3480.0, "width": 312.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "characters": "Helper Text", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "HEIGHT", "fontSize": 10.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 131.68724060058594, "lineHeightPercentFontSize": 160.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:30"}, "effects": [], "interactions": []}, {"id": "I1:15534;4689:5068;4382:1608", "name": "16/40", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Counter#4383:220"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:570f1bec6f0487f9c658e96b48ef9ed29a11e7ad/2051:7"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3263.0, "y": 3480.0, "width": 27.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "16/40", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 10.0, "textAlignHorizontal": "RIGHT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 131.68724060058594, "lineHeightPercentFontSize": 160.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:30"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 16.0, "primaryAxisSizingMode": "FIXED", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3480.0, "width": 312.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 4.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3428.0, "width": 442.0, "height": 50.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "transitionNodeID": "1:6785", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:6785", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}, {"id": "1:15535", "name": "Text Field Select", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:6337", "exposedInstances": ["I1:15535;4689:5057", "I1:15535;4689:5067", "I1:15535;4689:5068"], "componentProperties": {"✏️ Content#3141:8": {"value": "Content", "type": "TEXT"}, "✏️ Label#3141:7": {"value": "Cliente", "type": "TEXT"}, "✏️ Hint Text#4380:0": {"value": "Hint Text", "type": "TEXT"}, "✏️ Selected Itens#4709:0": {"value": "1 selecionado(s)", "type": "TEXT"}, "✏️ Prefix#4382:97": {"value": "PREFIX", "type": "TEXT"}, "Device": {"value": "Desktop", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Selected": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Hovered": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Focused": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Filled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Leading-icon": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Helper Text": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Prefix": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Validation": {"value": "Non-validation", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "1:15535", "overriddenFields": ["height", "transitionDuration", "transitionEasing", "transitionNodeID", "width"]}, {"id": "I1:15535;4689:5067", "overriddenFields": ["visible"]}], "children": [{"id": "I1:15535;4689:5056", "name": "Input Container", "type": "FRAME", "scrollBehavior": "SCROLLS", "boundVariables": {"rectangleCornerRadii": {"RECTANGLE_TOP_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_TOP_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}, "RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:32ac2be1d95bb2573fd921b561554b74e5024cdc/225:11"}}, "fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}], "strokes": [{"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}]}, "children": [{"id": "I1:15535;4689:5057", "name": ".Icon", "visible": false, "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:5877", "isExposedInstance": true, "componentProperties": {"🚺 Icon#4371:0": {"value": "1:5860", "type": "INSTANCE_SWAP", "preferredValues": []}, "State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "True", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Type": {"value": "Leading Icon", "type": "VARIANT", "boundVariables": {}}}, "overrides": [], "children": [{"id": "I1:15535;4689:5057;4695:16145", "name": "ui-icon-placeholder", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"mainComponent": "🚺 Icon#4371:0"}, "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:5860", "overrides": [], "children": [{"id": "I1:15535;4689:5057;4695:16145;60:5075", "name": "Clip path group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5057;4695:16145;60:5076", "name": "clip-path-52", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5057;4695:16145;60:5077", "name": "Retângulo 114899", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3466.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3466.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "isMask": true, "isMaskOutline": true, "maskType": "VECTOR", "interactions": []}, {"id": "I1:15535;4689:5057;4695:16145;60:5078", "name": "Grupo de máscara 122448", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5057;4695:16145;60:5079", "name": "Caminho 4704740", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:b2117e745c34812eb942df930af20e835909e138/2423:2"}]}, "blendMode": "PASS_THROUGH", "fills": [{"opacity": 0.6000000238418579, "blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:b2117e745c34812eb942df930af20e835909e138/2423:2"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3468.5, "y": 3444.5, "width": 15.0, "height": 15.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3468.5, "y": 3444.5, "width": 15.0, "height": 15.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3466.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3466.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3452.0, "y": 3428.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "I1:15535;4689:5058", "name": "Text Container", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5059", "name": "Label", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#3141:7"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:ffeb962cd506a50417a01c31fab47a24720b8d23/2423:30"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:338ed5dfda5929a104fb944d57f2f484b17d75c8/2051:11"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.27843138575553894, "g": 0.2823529541492462, "b": 0.2980392277240753, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:ffeb962cd506a50417a01c31fab47a24720b8d23/2423:30"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3452.0, "y": 3432.0, "width": 41.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Cliente", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 12.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 109.73937225341797, "lineHeightPercentFontSize": 133.3333282470703, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:15"}, "effects": [], "interactions": []}, {"id": "I1:15535;4689:5060", "name": "Content", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5061", "name": "PREFIX", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Prefix#4382:97"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3464.0, "y": 3440.0, "width": 54.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "PREFIX", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15535;4689:5062", "name": "Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#3141:7"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3464.0, "y": 3444.0, "width": 686.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "characters": "Cliente", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15535;4689:5063", "name": "Content", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Content#3141:8"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:75028bf3a314d72959d361a24a0198b1bff661b5/2423:32"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3510.0, "y": 3440.0, "width": 61.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "Content", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "textTruncation": "ENDING", "maxLines": 1, "fontSize": 16.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}, {"id": "I1:15535;4689:5064", "name": "SUFFIX", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:c1c6c97513fc05b1f75f7b0b650ed4594ad791df/2051:1"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:53544629739363db92f864604d358b16c3a449c4/2051:4"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3697.0, "y": 3440.0, "width": 55.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "SUFFIX", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Medium", "fontStyle": "Medium", "fontWeight": 500, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 16.0, "textAlignHorizontal": "RIGHT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 102.88065338134766, "lineHeightPercentFontSize": 125.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:403"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 4.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "MAX", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3464.0, "y": 3440.0, "width": 686.0, "height": 24.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "minHeight": 24.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "primaryAxisAlignItems": "CENTER", "paddingLeft": 12.0, "paddingRight": 12.0, "paddingTop": 4.0, "paddingBottom": 4.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3452.0, "y": 3428.0, "width": 710.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 1.0, "minHeight": 48.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "FILL", "effects": [], "interactions": []}, {"id": "I1:15535;4689:5065", "name": "Validation Icon", "visible": false, "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5066", "name": "ui-icon-placeholder", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:f03c8d071d2e55cd6de7785784c7b0e57fef0a9e/225:27"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:f03c8d071d2e55cd6de7785784c7b0e57fef0a9e/225:27"}}}, "componentId": "1:408", "overrides": [], "children": [{"id": "I1:15535;4689:5066;23:8", "name": "icon", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:5853c9a3e9b4431d1801924a826f6a4518f03ae4/2423:44"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.03529411926865578, "g": 0.6705882549285889, "b": 0.27843138575553894, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:5853c9a3e9b4431d1801924a826f6a4518f03ae4/2423:44"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3822.0, "y": 3446.0, "width": 12.0, "height": 12.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3820.0, "y": 3444.0, "width": 16.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 8.0, "paddingLeft": 16.0, "paddingRight": 16.0, "paddingTop": 16.0, "paddingBottom": 16.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3804.0, "y": 3428.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "I1:15535;4689:5067", "name": ".Ch<PERSON>ron", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:5904", "isExposedInstance": true, "componentProperties": {"State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Selected": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Type": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "I1:15535;4689:5067", "overriddenFields": ["visible"]}], "children": [{"id": "I1:15535;4689:5067;4689:9290", "name": "ui-chevron-down", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:5887", "overrides": [], "children": [{"id": "I1:15535;4689:5067;4689:9290;60:5153", "name": "Clip path group", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5067;4689:9290;60:5154", "name": "clip-path-51", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5067;4689:9290;60:5155", "name": "Retângulo 112764", "type": "VECTOR", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 4176.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4176.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "isMask": true, "isMaskOutline": true, "maskType": "VECTOR", "interactions": []}, {"id": "I1:15535;4689:5067;4689:9290;60:5156", "name": "Grupo de máscara 122415", "type": "GROUP", "scrollBehavior": "SCROLLS", "children": [{"id": "I1:15535;4689:5067;4689:9290;60:5157", "name": "Caminho 714383", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.23137255012989044, "g": 0.4117647111415863, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 4178.49951171875, "y": 3447.624755859375, "width": 15.000953674316406, "height": 8.749751091003418}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4178.49951171875, "y": 3447.624755859375, "width": 15.000953674316406, "height": 8.749751091003418}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4176.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 4176.0, "y": 3442.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 4162.0, "y": 3428.0, "width": 48.0, "height": 48.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "1:5912", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:5912", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}, {"trigger": {"type": "ON_PRESS"}, "actions": [{"type": "NODE", "destinationId": "1:5928", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:47f72341ea73f8aa1286402f9add30a581c82d6b/2423:18"}}}], "strokes": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.6549019813537598, "g": 0.658823549747467, "b": 0.6745098233222961, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}}}], "cornerRadius": 2.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "individualStrokeWeights": {"top": 0.0, "right": 0.0, "bottom": 2.0, "left": 0.0}, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.9803921580314636, "g": 0.9843137264251709, "b": 1.0, "a": 1.0}, "layoutMode": "HORIZONTAL", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "MAX", "strokesIncludedInLayout": true, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3452.0, "y": 3428.0, "width": 758.0, "height": 50.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "I1:15535;4689:5068", "name": ".Helper", "visible": false, "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentId": "1:5981", "isExposedInstance": true, "componentProperties": {"✏️ Counter#4383:220": {"value": "16/40", "type": "TEXT"}, "✏️ Helper Text#4383:211": {"value": "Helper Text", "type": "TEXT"}, "Is DIsabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Counter": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Helper Text": {"value": "True", "type": "VARIANT", "boundVariables": {}}}, "overrides": [], "children": [{"id": "I1:15535;4689:5068;4382:1607", "name": "Helper Text", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Helper Text#4383:211"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:570f1bec6f0487f9c658e96b48ef9ed29a11e7ad/2051:7"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3452.0, "y": 3480.0, "width": 312.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1.0, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "characters": "Helper Text", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "HEIGHT", "fontSize": 10.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 131.68724060058594, "lineHeightPercentFontSize": 160.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:30"}, "effects": [], "interactions": []}, {"id": "I1:15535;4689:5068;4382:1608", "name": "16/40", "visible": false, "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Counter#4383:220"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:570f1bec6f0487f9c658e96b48ef9ed29a11e7ad/2051:7"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.4274509847164154, "g": 0.4313725531101227, "b": 0.4431372582912445, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:2b17ab2565343043e400612bbdb5451e57955f61/2423:26"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3737.0, "y": 3480.0, "width": 27.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "characters": "16/40", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 10.0, "textAlignHorizontal": "RIGHT", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 131.68724060058594, "lineHeightPercentFontSize": 160.0, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:30"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 16.0, "primaryAxisSizingMode": "FIXED", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3452.0, "y": 3480.0, "width": 312.0, "height": 16.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 4.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3452.0, "y": 3428.0, "width": 758.0, "height": 50.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "transitionNodeID": "1:6785", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:6785", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 32.0, "primaryAxisSizingMode": "FIXED", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3428.0, "width": 1232.0, "height": 50.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "STRETCH", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, {"id": "1:15536", "name": "actions", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "1:15537", "name": "<PERSON><PERSON>", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"itemSpacing": {"type": "VARIABLE_ALIAS", "id": "VariableID:ba06adae1e837460bc5c0b4670b79ae86eb9b63b/1217:33"}, "paddingLeft": {"type": "VARIABLE_ALIAS", "id": "VariableID:0f58981179631ea4ed825f50528108f1f9a71d57/1217:25"}, "paddingRight": {"type": "VARIABLE_ALIAS", "id": "VariableID:0f58981179631ea4ed825f50528108f1f9a71d57/1217:25"}, "rectangleCornerRadii": {"RECTANGLE_TOP_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:a5c3f803292d93292fdee291ed8f9e586b1a30da/240:176"}, "RECTANGLE_TOP_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:a5c3f803292d93292fdee291ed8f9e586b1a30da/240:176"}, "RECTANGLE_BOTTOM_LEFT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:a5c3f803292d93292fdee291ed8f9e586b1a30da/240:176"}, "RECTANGLE_BOTTOM_RIGHT_CORNER_RADIUS": {"type": "VARIABLE_ALIAS", "id": "VariableID:a5c3f803292d93292fdee291ed8f9e586b1a30da/240:176"}}, "fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}]}, "componentId": "1:5780", "componentProperties": {"✏️ Label#211:2": {"value": "Buscar", "type": "TEXT"}, "🚺 Icon#366:0": {"value": "1:226", "type": "INSTANCE_SWAP", "preferredValues": [{"type": "COMPONENT", "key": "56d82bc168ff974df8671c85f06b8277ca3acc7a"}]}, "Type": {"value": "Primary", "type": "VARIANT", "boundVariables": {}}, "Size": {"value": "Large", "type": "VARIANT", "boundVariables": {}}, "State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "True", "type": "VARIANT", "boundVariables": {}}, "Has Floating": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is Quickbutton": {"value": "False", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "1:15537", "overriddenFields": ["height", "width"]}], "children": [{"id": "I1:15537;2104:1535", "name": "🚺 Icon", "visible": false, "type": "INSTANCE", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"mainComponent": "🚺 Icon#366:0"}, "boundVariables": {"size": {"x": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}, "y": {"type": "VARIABLE_ALIAS", "id": "VariableID:9992ef6a9f6a196f00df3ea3056fcad3cf3aca6a/3043:1"}}}, "explicitVariableModes": {"VariableCollectionId:2e273fec0f08a92e70f3795b430fe4f9aadf0b3e/2819:3": "2578:6"}, "componentId": "1:226", "overrides": [], "children": [{"id": "I1:15537;2104:1535;60:5079", "name": "Caminho 4704740", "type": "VECTOR", "scrollBehavior": "SCROLLS", "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:765f8513ff4b2774eb49e5e0e1eb1f2762b769af/2423:16"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:765f8513ff4b2774eb49e5e0e1eb1f2762b769af/2423:16"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "absoluteBoundingBox": {"x": 3103.0, "y": 3442.5, "width": 15.0, "height": 15.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "SCALE", "horizontal": "SCALE"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "absoluteBoundingBox": {"x": 3100.5, "y": 3440.0, "width": 20.0, "height": 20.0}, "absoluteRenderBounds": null, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "I1:15537;2104:1536", "name": "✏️ Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#211:2"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:765f8513ff4b2774eb49e5e0e1eb1f2762b769af/2423:16"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:0644e13914b132a4c3ab78a471a7582a08113f4f/2051:12"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:58614f24f9dd4a3009c460d0c5b3239a1deb0f91/2051:5"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:1257a181d15a3bcf2ec89609ce6e8bfad5c0fc23/2051:10"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:765f8513ff4b2774eb49e5e0e1eb1f2762b769af/2423:16"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3054.5, "y": 3442.0, "width": 47.0, "height": 16.0}, "absoluteRenderBounds": {"x": 3055.767333984375, "y": 3445.535888671875, "width": 45.0341796875, "height": 9.632080078125}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "maxHeight": 20.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "Buscar", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-Bold", "fontStyle": "Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "textTruncation": "ENDING", "fontSize": 14.0, "textAlignHorizontal": "CENTER", "textAlignVertical": "TOP", "letterSpacing": 0.0, "lineHeightPx": 16.0, "lineHeightPercent": 94.06231689453125, "lineHeightPercentFontSize": 114.28571319580078, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:233"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": true, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.6549019813537598, "g": 0.658823549747467, "b": 0.6745098233222961, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.6549019813537598, "g": 0.658823549747467, "b": 0.6745098233222961, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:157553d4273e18b4b9b08f0062c03f3c7cf89f20/2423:23"}}}], "strokes": [], "cornerRadius": 999.0, "cornerSmoothing": 0.0, "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.6549019813537598, "g": 0.658823549747467, "b": 0.6745098233222961, "a": 1.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 8.0, "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 24.0, "paddingRight": 24.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3426.0, "width": 200.0, "height": 48.0}, "absoluteRenderBounds": {"x": 2978.0, "y": 3426.0, "width": 200.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "maxHeight": 48.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "FIXED", "effects": [], "interactions": []}, {"id": "1:15538", "name": "Button Text", "type": "INSTANCE", "scrollBehavior": "SCROLLS", "boundVariables": {"paddingLeft": {"type": "VARIABLE_ALIAS", "id": "VariableID:acea07cbb5dc614e2e89145d41dc23802a531143/225:18"}, "paddingRight": {"type": "VARIABLE_ALIAS", "id": "VariableID:acea07cbb5dc614e2e89145d41dc23802a531143/225:18"}}, "componentId": "1:10530", "componentProperties": {"🚺 Icon#212:98": {"value": "1:226", "type": "INSTANCE_SWAP", "preferredValues": [{"type": "COMPONENT", "key": "56d82bc168ff974df8671c85f06b8277ca3acc7a"}]}, "✏️ Label#212:147": {"value": "Limpar", "type": "TEXT"}, "State": {"value": "<PERSON><PERSON><PERSON>", "type": "VARIANT", "boundVariables": {}}, "Is Disabled": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Leading-icon": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Has Trailling-icon": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "No Background": {"value": "False", "type": "VARIANT", "boundVariables": {}}, "Is OnColor": {"value": "False", "type": "VARIANT", "boundVariables": {}}}, "overrides": [{"id": "1:15538", "overriddenFields": ["height", "transitionDuration", "transitionEasing", "transitionNodeID", "width"]}], "children": [{"id": "I1:15538;179:1045", "name": "✏️ Label", "type": "TEXT", "scrollBehavior": "SCROLLS", "componentPropertyReferences": {"characters": "✏️ Label#212:147"}, "boundVariables": {"fills": [{"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}], "lineHeight": [{"type": "VARIABLE_ALIAS", "id": "VariableID:66d40677200590685584525c0c66386f47a6bdb3/2051:9"}], "fontFamily": [{"type": "VARIABLE_ALIAS", "id": "VariableID:d65ea259759096f5cce25703e4fe15937b3146b2/2051:2"}], "fontSize": [{"type": "VARIABLE_ALIAS", "id": "VariableID:58614f24f9dd4a3009c460d0c5b3239a1deb0f91/2051:5"}], "fontStyle": [{"type": "VARIABLE_ALIAS", "id": "VariableID:3db7988347c99dcde8523f9f6f91a0462e67b1a1/2051:6"}]}, "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.23137255012989044, "g": 0.4117647111415863, "b": 1.0, "a": 1.0}, "boundVariables": {"color": {"type": "VARIABLE_ALIAS", "id": "VariableID:bca02670e74980cb9bd5309e0c3a384eac9c19ee/2423:28"}}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "OUTSIDE", "absoluteBoundingBox": {"x": 3206.0, "y": 3442.0, "width": 48.0, "height": 16.0}, "absoluteRenderBounds": {"x": 3207.134033203125, "y": 3444.612060546875, "width": 45.4775390625, "height": 13.48193359375}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "maxHeight": 16.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "characters": "Limpar", "characterStyleOverrides": [], "styleOverrideTable": {}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "Bradesco Sans", "fontPostScriptName": "BradescoSans-SemiBold", "fontStyle": "SemiBold", "fontWeight": 600, "textAutoResize": "WIDTH_AND_HEIGHT", "fontSize": 14.0, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0.0, "lineHeightPx": 20.0, "lineHeightPercent": 117.57789611816406, "lineHeightPercentFontSize": 142.85714721679688, "lineHeightUnit": "PIXELS"}, "layoutVersion": 4, "styles": {"text": "1:412"}, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "fills": [{"blendMode": "NORMAL", "visible": false, "type": "SOLID", "color": {"r": 1.0, "g": 1.0, "b": 1.0, "a": 1.0}}], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 10.0, "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 8.0, "paddingRight": 8.0, "paddingTop": 4.0, "paddingBottom": 4.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 3198.0, "y": 3438.0, "width": 64.0, "height": 24.0}, "absoluteRenderBounds": {"x": 3198.0, "y": 3438.0, "width": 64.0, "height": 24.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "maxHeight": 24.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "FIXED", "effects": [], "transitionNodeID": "1:10646", "transitionDuration": 300.0, "transitionEasing": "EASE_OUT", "interactions": [{"trigger": {"type": "ON_HOVER"}, "actions": [{"type": "NODE", "destinationId": "1:10646", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}, {"trigger": {"type": "ON_PRESS"}, "actions": [{"type": "NODE", "destinationId": "1:10650", "navigation": "CHANGE_TO", "transition": {"type": "DISSOLVE", "easing": {"type": "EASE_OUT"}, "duration": 0.30000001192092896}, "resetVideoPosition": false}]}]}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "HORIZONTAL", "itemSpacing": 20.0, "counterAxisAlignItems": "CENTER", "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2978.0, "y": 3426.0, "width": 284.0, "height": 48.0}, "absoluteRenderBounds": {"x": 2978.0, "y": 3426.0, "width": 284.0, "height": 48.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "HUG", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [], "fills": [], "strokes": [], "strokeWeight": 1.0, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.0, "g": 0.0, "b": 0.0, "a": 0.0}, "layoutMode": "VERTICAL", "counterAxisSizingMode": "FIXED", "itemSpacing": 32.0, "primaryAxisAlignItems": "MAX", "paddingLeft": 24.0, "paddingRight": 24.0, "paddingTop": 32.0, "paddingBottom": 32.0, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 2954.0, "y": 3314.0, "width": 1280.0, "height": 192.0}, "absoluteRenderBounds": {"x": 2950.0, "y": 3312.0, "width": 1288.0, "height": 200.0}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 0.0, "layoutSizingHorizontal": "FIXED", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, "components": {"1:2247": {"key": "18170431e77bab85299152e715b4c9961b805e5b", "name": "State=Default, Is OnColor=False, Is Disabled=False", "description": "", "remote": true, "componentSetId": "1:2246", "documentationLinks": []}, "1:2244": {"key": "768bd504226188795aaa12a06786d0dc6eada606", "name": "component-search", "description": "⚠️ Atenção\nNão utilizar esse ícone.\nCaso necessite o uso desse ícone entre em\ncontato com o time do Liquid.\n\n\n<EMAIL>", "remote": true, "documentationLinks": []}, "1:1052": {"key": "ad3514ff50f57b59c3a3c9cd187b6dfe2f7e80c2", "name": "Type=Desktop, Is Range=False, Is Hovered=False, Is Focused=False, Is Filled=False, Is Disabled=False, Has Helper Text=False, Validation=Non-validation", "description": "", "remote": true, "componentSetId": "1:1051", "documentationLinks": []}, "1:392": {"key": "2616d5d024504a6d0af76b2066ba0587778a6b16", "name": "State=Default, Is OnColor=True, Is Disabled=False, Type=Leading Icon", "description": "", "remote": true, "componentSetId": "1:381", "documentationLinks": []}, "1:226": {"key": "5ee99fef31f5f352907f3e6199b586472436d4b9", "name": "ui-icon-placeholder", "description": "", "remote": true, "documentationLinks": []}, "1:408": {"key": "1e99f3c2dbb55fe302d32cfbf46d0456f35da562", "name": "ui-icon-placeholder", "description": "", "remote": true, "documentationLinks": []}, "1:439": {"key": "ede6fb16fe5dc2ef69e2287c7a2b0a73f807bd22", "name": "State=Default, Is Disabled=False, Has Leading-icon=False, Has Trailling-icon=False, No Background=True, Is OnColor=False", "description": "", "remote": true, "componentSetId": "1:420", "documentationLinks": []}, "1:556": {"key": "563cdd8ab75832a3aefba8d56b6ea520f42032da", "name": "State=Default, Is Disabled=False", "description": "", "remote": true, "componentSetId": "1:555", "documentationLinks": []}, "1:549": {"key": "106b110cec10863464ef3398d62c5d37829849ee", "name": "ui-calendar", "description": "", "remote": true, "documentationLinks": []}, "1:568": {"key": "45c96a54587cc65025f47cdc3a68842bfa94509f", "name": "Is DIsabled=False, Is OnColor=False, Is Counter=False, Is Helper Text=True", "description": "", "remote": true, "componentSetId": "1:567", "documentationLinks": []}, "1:6337": {"key": "aadca30020a1585acb9bc7eda860c77e00741723", "name": "Device=Desktop, Is OnColor=False, Is Selected=False, Is Hovered=False, Is Focused=False, Is Filled=False, Is Disabled=False, Has Leading-icon=False, Has Helper Text=False, Has Prefix=False, Validation=Non-validation", "description": "", "remote": true, "componentSetId": "1:6336", "documentationLinks": []}, "1:5877": {"key": "2616d5d024504a6d0af76b2066ba0587778a6b16", "name": "State=Default, Is OnColor=True, Is Disabled=False, Type=Leading Icon", "description": "", "remote": true, "componentSetId": "1:5866", "documentationLinks": []}, "1:5860": {"key": "5ee99fef31f5f352907f3e6199b586472436d4b9", "name": "ui-icon-placeholder", "description": "", "remote": true, "documentationLinks": []}, "1:5904": {"key": "4bf964984efb22dfb30ecf8f295f5cbe787afa78", "name": "State=Default, Is OnColor=False, Is Disabled=False, Is Selected=False, Type=Traling Icon", "description": "", "remote": true, "componentSetId": "1:5899", "documentationLinks": []}, "1:5887": {"key": "5a16ee3e7375f359a379c62d2919868b1dcb03dc", "name": "ui-chevron-down", "description": "", "remote": true, "documentationLinks": []}, "1:5981": {"key": "45c96a54587cc65025f47cdc3a68842bfa94509f", "name": "Is DIsabled=False, Is OnColor=False, Is Counter=False, Is Helper Text=True", "description": "", "remote": true, "componentSetId": "1:5980", "documentationLinks": []}, "1:5780": {"key": "4eae2cc3b740db4911cb892e3b3e7f48e2879270", "name": "Type=Primary, Size=Large, State=Default, Is OnColor=False, Is Disabled=True, Has Floating=False, Is Quickbutton=False", "description": "", "remote": true, "componentSetId": "1:5545", "documentationLinks": []}, "1:10530": {"key": "51d4f25f3947c5d9bb9d2facd1a573fba1cf8732", "name": "State=Default, Is Disabled=False, Has Leading-icon=False, Has Trailling-icon=False, No Background=False, Is OnColor=False", "description": "", "remote": true, "componentSetId": "1:10529", "documentationLinks": []}}, "componentSets": {"1:2246": {"key": "eaeef28834cd335b1e6ef22b54c2ab49c21f85f7", "name": ".Icon", "description": "🚧 BUILDING BLOCKS", "remote": true}, "1:1051": {"key": "e628081bfc3502acd2e0f7e0eaf101bde47582bb", "name": "Text Field Calendar", "description": "✅ CORE COMPONENT\n\n\nCalendar é um componente onde o usuário pode interagir de duas maneiras: Escrevendo no text field ou abrindo o calendário. Após digitar a data, pode visualizar a seleção no calendário.", "remote": true}, "1:381": {"key": "9d40f87206a01f76a64a1c9499af5bb5b97a31bd", "name": ".Icon", "description": "🚧 BUILDING BLOCKS", "remote": true}, "1:420": {"key": "b4bf01c5532b9a7d6d8c0c4d5f627d0166928fc3", "name": "Button Text", "description": "✅ CORE COMPONENT\n\n\nOs botões permitem que os usuários executem uma ação ou naveguem para outra página. Eles têm vários estilos para diversas necessidades e são ideais para chamar a atenção para onde um usuário precisa fazer algo para avançar em um fluxo.", "remote": true}, "1:555": {"key": "65f793641fbbfbc32d7a0a3f4ad65b63ba4e857e", "name": ".Calendar <PERSON><PERSON>", "description": "🚧 BUILDING BLOCKS", "remote": true}, "1:567": {"key": "00d1b411fb0c8d2ca15a5b0b7a5a8fc9e171a669", "name": ".Helper", "description": "🚧 BUILDING BLOCKS", "remote": true}, "1:6336": {"key": "65b802b4b2e22ffb1012583f42d445c042aba774", "name": "Text Field Select", "description": "✅ CORE COMPONENT\n⚠️ Utilizar o componente Modal Select [Mobile] para prototipação da seleção. \n\n\nOs campos de texto são usados ​​para coletar entradas baseadas em texto usando um teclado. Eles são um tipo de entrada que permite aos usuários inserir e editar texto.", "remote": true}, "1:5866": {"key": "9d40f87206a01f76a64a1c9499af5bb5b97a31bd", "name": ".Icon", "description": "🚧 BUILDING BLOCKS", "remote": true}, "1:5899": {"key": "d070798dfaacb8af65991ad2d8fc5bdaeb0e6a9a", "name": ".Ch<PERSON>ron", "description": "🚧 BUILDING BLOCKS", "remote": true}, "1:5980": {"key": "00d1b411fb0c8d2ca15a5b0b7a5a8fc9e171a669", "name": ".Helper", "description": "🚧 BUILDING BLOCKS", "remote": true}, "1:5545": {"key": "2aa0f0b4f4f58003d1a5d9e91891c17a9db39b26", "name": "<PERSON><PERSON>", "description": "✅ CORE COMPONENT\n\n\nOs botões permitem que os usuários executem uma ação ou naveguem para outra página. Eles têm vários estilos para diversas necessidades e são ideais para chamar a atenção para onde um usuário precisa fazer algo para avançar em um fluxo.\n#QuickButton", "remote": true}, "1:10529": {"key": "b4bf01c5532b9a7d6d8c0c4d5f627d0166928fc3", "name": "Button Text", "description": "✅ CORE COMPONENT\n\n\nOs botões permitem que os usuários executem uma ação ou naveguem para outra página. Eles têm vários estilos para diversas necessidades e são ideais para chamar a atenção para onde um usuário precisa fazer algo para avançar em um fluxo.", "remote": true}}, "schemaVersion": 0, "styles": {"1:373": {"key": "13baec9847bd6f40236b138406e37e378b037fc5", "name": "Elevation/$brad-elevation-10", "styleType": "EFFECT", "remote": true, "description": ""}, "1:15": {"key": "981b1e114ae8b5cace5426d001f68f71efb26fc3", "name": "Subtitle Xs", "styleType": "TEXT", "remote": true, "description": "$brad-font-subtitle-xs"}, "1:403": {"key": "6644a96ff98e8e83c41f4a40af0b97668daa4e3b", "name": "Paragraph Md", "styleType": "TEXT", "remote": true, "description": "$brad-font-paragraph-md"}, "1:412": {"key": "0adb4fd07fa8cb5b82ed26daca4e2723ef5bbcf7", "name": "Link Sm", "styleType": "TEXT", "remote": true, "description": "$brad-font-link-sm"}, "1:566": {"key": "5d2f41699a4efbd040856febbf4c54d23595207d", "name": "Subtitle Xxs", "styleType": "TEXT", "remote": true, "description": "$brad-font-subtitle-xxs"}, "1:30": {"key": "5d2f41699a4efbd040856febbf4c54d23595207d", "name": "Subtitle Xxs", "styleType": "TEXT", "remote": true, "description": "$brad-font-subtitle-xxs"}, "1:233": {"key": "074a64f511d62eeb7bf1bc27396f6cb78850abd5", "name": "<PERSON><PERSON>", "styleType": "TEXT", "remote": true, "description": ""}}}}}