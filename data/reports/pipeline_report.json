{"pipeline_config": {"data_path": "data", "save_parquet": true, "save_csv": true, "use_api": false, "input_file": "data/raw/figma_16-4995.json"}, "project_summary": {"project_name": "SAT CONTABILIDADE", "total_nodes": 1, "total_components": 20, "total_component_sets": 8, "design_tokens": 38}, "extraction_summary": {"total_components": 1, "design_tokens_created": 38, "conversion_method": "enhanced_mcp_based"}, "html_generation_stats": {"total_components": 0, "components_with_css": 0, "components_with_content": 0, "html_tags_used": {}, "css_properties_count": 0}, "css_generation_stats": {"total_lines": 217, "total_characters": 4996, "total_rules": 127, "design_tokens": 7, "css_variables": 0, "components_with_styles": 0}, "component_analysis": {"total_components": 112, "components_by_type": {"container": 32, "component": 29, "text": 27, "shape": 16, "input": 4, "image": 3, "button": 1}, "components_by_html_tag": {"div": 84, "span": 27, "button": 1}, "average_size": {"width": 199.05689542634147, "height": 30.318407126835414}, "size_distribution": {"width": {"min": 1.0, "max": 1280.0, "median": 48.0}, "height": {"min": 8.749751091003418, "max": 192.0, "median": 20.0}}, "hierarchy_stats": {"root_components": 1, "components_with_children": 66, "max_children": "5", "average_children": 0.9910714285714286}}, "storage_summary": {"processed_files": [{"name": "components.json", "path": "processed/components.json", "size_mb": 0.0, "type": ".json"}, {"name": "simplified_design.json", "path": "processed/simplified_design.json", "size_mb": 0.15, "type": ".json"}, {"name": "components.csv", "path": "processed/components.csv", "size_mb": 0.01, "type": ".csv"}, {"name": "components.parquet", "path": "processed/components.parquet", "size_mb": 0.01, "type": ".parquet"}], "output_files": [{"name": "index_enhanced_external.html", "path": "output/html/index_enhanced_external.html", "size_mb": 0.01, "type": ".html"}, {"name": "index_enhanced.html", "path": "output/html/index_enhanced.html", "size_mb": 0.02, "type": ".html"}, {"name": "index_enhanced2.html", "path": "output/html/index_enhanced2.html", "size_mb": 0.26, "type": ".html"}, {"name": "styles_enhanced.css", "path": "output/css/styles_enhanced.css", "size_mb": 0.0, "type": ".css"}], "total_size_mb": 0.47}}