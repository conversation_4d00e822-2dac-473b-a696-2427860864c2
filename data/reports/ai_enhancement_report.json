{"summary": {"total_components": 112, "complex_elements_found": 46, "complexity_types": {"variable_aliases": 0, "complex_layouts": 0, "interactive_elements": 0, "custom_components": 29, "vector_graphics": 17, "text_with_formatting": 0, "responsive_elements": 0}}, "complex_elements": {"variable_aliases": [], "complex_layouts": [], "interactive_elements": [], "custom_components": [{"id": "I20:12642;12258:2410", "name": "component-search", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3701.0, "y": 3360.0, "width": 20.0, "height": 20.0}}}, {"id": "20:12642", "name": ".Icon", "component_properties": {"visible": false, "locked": false, "opacity": 1.0, "position": {"x": 3687.0, "y": 3346.0, "width": 48.0, "height": 48.0}}}, {"id": "I20:12644;12258:2410", "name": "component-search", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3702.0, "y": 3360.0, "width": 20.0, "height": 20.0}}}, {"id": "20:12644", "name": ".Icon", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3688.0, "y": 3346.0, "width": 48.0, "height": 48.0}}}, {"id": "I1:15531;9228:53862;4695:16145", "name": "ui-icon-placeholder", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3782.0, "y": 3360.0, "width": 20.0, "height": 20.0}}}, {"id": "I1:15531;9228:53862", "name": ".Icon", "component_properties": {"visible": false, "locked": false, "opacity": 1.0, "position": {"x": 3768.0, "y": 3346.0, "width": 48.0, "height": 48.0}}}, {"id": "I1:15531;9228:53871", "name": "ui-icon-placeholder", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 4136.0, "y": 3362.0, "width": 16.0, "height": 16.0}}}, {"id": "I1:15531;13260:2835", "name": "Button Text", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3986.0, "y": 3362.0, "width": 38.0, "height": 16.0}}}, {"id": "I1:15531;9228:53872;9228:55987", "name": "ui-calendar", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 4176.0, "y": 3360.0, "width": 20.0, "height": 20.0}}}, {"id": "I1:15531;9228:53872", "name": ".Calendar <PERSON><PERSON>", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 4162.0, "y": 3346.0, "width": 48.0, "height": 48.0}}}, {"id": "I1:15531;9228:53873", "name": ".Helper", "component_properties": {"visible": false, "locked": false, "opacity": 1.0, "position": {"x": 3768.0, "y": 3398.0, "width": 312.0, "height": 16.0}}}, {"id": "1:15531", "name": "Text Field Calendar", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3768.0, "y": 3346.0, "width": 442.0, "height": 48.0}}}, {"id": "I1:15534;4689:5057;4695:16145", "name": "ui-icon-placeholder", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 2992.0, "y": 3442.0, "width": 20.0, "height": 20.0}}}, {"id": "I1:15534;4689:5057", "name": ".Icon", "component_properties": {"visible": false, "locked": false, "opacity": 1.0, "position": {"x": 2978.0, "y": 3428.0, "width": 48.0, "height": 48.0}}}, {"id": "I1:15534;4689:5066", "name": "ui-icon-placeholder", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3346.0, "y": 3444.0, "width": 16.0, "height": 16.0}}}, {"id": "I1:15534;4689:5067;4689:9290", "name": "ui-chevron-down", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3386.0, "y": 3442.0, "width": 20.0, "height": 20.0}}}, {"id": "I1:15534;4689:5067", "name": ".Ch<PERSON>ron", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3372.0, "y": 3428.0, "width": 48.0, "height": 48.0}}}, {"id": "I1:15534;4689:5068", "name": ".Helper", "component_properties": {"visible": false, "locked": false, "opacity": 1.0, "position": {"x": 2978.0, "y": 3480.0, "width": 312.0, "height": 16.0}}}, {"id": "1:15534", "name": "Text Field Select", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 2978.0, "y": 3428.0, "width": 442.0, "height": 50.0}}}, {"id": "I1:15535;4689:5057;4695:16145", "name": "ui-icon-placeholder", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3466.0, "y": 3442.0, "width": 20.0, "height": 20.0}}}, {"id": "I1:15535;4689:5057", "name": ".Icon", "component_properties": {"visible": false, "locked": false, "opacity": 1.0, "position": {"x": 3452.0, "y": 3428.0, "width": 48.0, "height": 48.0}}}, {"id": "I1:15535;4689:5066", "name": "ui-icon-placeholder", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3820.0, "y": 3444.0, "width": 16.0, "height": 16.0}}}, {"id": "I1:15535;4689:5067;4689:9290", "name": "ui-chevron-down", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 4176.0, "y": 3442.0, "width": 20.0, "height": 20.0}}}, {"id": "I1:15535;4689:5067", "name": ".Ch<PERSON>ron", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 4162.0, "y": 3428.0, "width": 48.0, "height": 48.0}}}, {"id": "I1:15535;4689:5068", "name": ".Helper", "component_properties": {"visible": false, "locked": false, "opacity": 1.0, "position": {"x": 3452.0, "y": 3480.0, "width": 312.0, "height": 16.0}}}, {"id": "1:15535", "name": "Text Field Select", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3452.0, "y": 3428.0, "width": 758.0, "height": 50.0}}}, {"id": "I1:15537;2104:1535", "name": "🚺 Icon", "component_properties": {"visible": false, "locked": false, "opacity": 1.0, "position": {"x": 3100.5, "y": 3440.0, "width": 20.0, "height": 20.0}}}, {"id": "1:15537", "name": "<PERSON><PERSON>", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 2978.0, "y": 3426.0, "width": 200.0, "height": 48.0}}}, {"id": "1:15538", "name": "Button Text", "component_properties": {"visible": true, "locked": false, "opacity": 1.0, "position": {"x": 3198.0, "y": 3438.0, "width": 64.0, "height": 24.0}}}], "vector_graphics": [{"id": "I20:12642;12258:2410;2051:95", "name": "component-search-path", "original_type": "VECTOR"}, {"id": "I20:12644;12258:2410;2051:95", "name": "component-search-path", "original_type": "VECTOR"}, {"id": "I1:15531;9228:53862;4695:16145;60:5079", "name": "Caminho 4704740", "original_type": "VECTOR"}, {"id": "I1:15531;9228:53871;23:8", "name": "icon", "original_type": "VECTOR"}, {"id": "I1:15531;9228:53872;9228:55987;60:4921", "name": "Retângulo 99097", "original_type": "VECTOR"}, {"id": "I1:15531;9228:53872;9228:55987;60:4923", "name": "Caminho 77394", "original_type": "VECTOR"}, {"id": "I1:15534;4689:5057;4695:16145;60:5077", "name": "Retângulo 114899", "original_type": "VECTOR"}, {"id": "I1:15534;4689:5057;4695:16145;60:5079", "name": "Caminho 4704740", "original_type": "VECTOR"}, {"id": "I1:15534;4689:5066;23:8", "name": "icon", "original_type": "VECTOR"}, {"id": "I1:15534;4689:5067;4689:9290;60:5155", "name": "Retângulo 112764", "original_type": "VECTOR"}, {"id": "I1:15534;4689:5067;4689:9290;60:5157", "name": "Caminho 714383", "original_type": "VECTOR"}, {"id": "I1:15535;4689:5057;4695:16145;60:5077", "name": "Retângulo 114899", "original_type": "VECTOR"}, {"id": "I1:15535;4689:5057;4695:16145;60:5079", "name": "Caminho 4704740", "original_type": "VECTOR"}, {"id": "I1:15535;4689:5066;23:8", "name": "icon", "original_type": "VECTOR"}, {"id": "I1:15535;4689:5067;4689:9290;60:5155", "name": "Retângulo 112764", "original_type": "VECTOR"}, {"id": "I1:15535;4689:5067;4689:9290;60:5157", "name": "Caminho 714383", "original_type": "VECTOR"}, {"id": "I1:15537;2104:1535;60:5079", "name": "Caminho 4704740", "original_type": "VECTOR"}], "text_with_formatting": [], "responsive_elements": []}, "ai_recommendations": {"high_priority": [], "medium_priority": [{"type": "vector_graphics", "count": 17, "reason": "Gráficos vetoriais podem precisar de SVG ou ícones"}], "low_priority": []}}