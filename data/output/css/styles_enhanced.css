:root {
  --stroke-f7f3cb41-color: #9747ff;
  --stroke-f7f3cb41-width: 1.0px;
  --stroke-f7f3cb41-style: dashed;
  --fill-b2d4e19d: #fafbff;
  --stroke-8b43a06e-color: #47484c;
  --stroke-8b43a06e-top-width: 0.0px;
  --stroke-8b43a06e-right-width: 0.0px;
  --stroke-8b43a06e-bottom-width: 2.0px;
  --stroke-8b43a06e-left-width: 0.0px;
  --stroke-8b43a06e-style: solid;
  --style-2307eb9b-font-family: Bradesco Sans;
  --style-2307eb9b-font-weight: 600;
  --style-2307eb9b-font-size: 12.0;
  --style-2307eb9b-line-height: 1.3333333333333333em;
  --style-2307eb9b-text-align-horizontal: left;
  --style-2307eb9b-text-align-vertical: top;
  --fill-64a0e084: #47484c;
  --style-1816976b-font-family: Bradesco Sans;
  --style-1816976b-font-weight: 500;
  --style-1816976b-font-size: 16.0;
  --style-1816976b-line-height: 1.25em;
  --style-1816976b-text-align-horizontal: left;
  --style-1816976b-text-align-vertical: top;
  --fill-dac60cb0: #000000;
  --fill-e18bd0be: #3b69ff;
  --fill-a5eed152: #d9dcdd;
  --stroke-626ad390-color: #a7a8ac;
  --stroke-626ad390-top-width: 0.0px;
  --stroke-626ad390-right-width: 0.0px;
  --stroke-626ad390-bottom-width: 2.0px;
  --stroke-626ad390-left-width: 0.0px;
  --stroke-626ad390-style: solid;
  --fill-f6dc0550: #6d6e71;
  --stroke-7906ef1b-color: #3b69ff;
  --stroke-7906ef1b-top-width: 0.0px;
  --stroke-7906ef1b-right-width: 0.0px;
  --stroke-7906ef1b-bottom-width: 2.0px;
  --stroke-7906ef1b-left-width: 0.0px;
  --stroke-7906ef1b-style: solid;
  --stroke-d2c048c6-color: #000000;
  --stroke-d2c048c6-width: 1.0px;
  --stroke-d2c048c6-style: solid;
  --effect-7de7775c-box-shadow: 0.0px 8.0px 16.0px rgba(0, 0, 0, 0.1599999964237213);
  --fill-3acfdca0: #ffffff;
  --style-bf0b927c-font-family: Bradesco Sans;
  --style-bf0b927c-font-weight: 600;
  --style-bf0b927c-font-size: 16.0;
  --style-bf0b927c-line-height: 1.25em;
  --style-bf0b927c-text-align-horizontal: left;
  --style-bf0b927c-text-align-vertical: middle;
  --fill-cca01286: #cc092f;
  --style-94b5cec3-font-family: Bradesco Sans;
  --style-94b5cec3-font-weight: 700;
  --style-94b5cec3-font-size: 14.0;
  --style-94b5cec3-line-height: 1.1428571428571428em;
  --style-94b5cec3-text-align-horizontal: center;
  --style-94b5cec3-text-align-vertical: top;
  --style-965775aa-font-family: Bradesco Sans;
  --style-965775aa-font-weight: 500;
  --style-965775aa-font-size: 16.0;
  --style-965775aa-line-height: 1.25em;
  --style-965775aa-text-align-horizontal: left;
  --style-965775aa-text-align-vertical: bottom;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.element-busca-default-buscar-por-cliente {
  border-color: #9747ff;
  border-width: 1.0px;
  border-style: dashed;
  border-radius: 5.0px;
}

.container-input-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fafbff;
  border-color: #3b69ff;
  border-top-width: 0.0px;
  border-right-width: 0.0px;
  border-bottom-width: 2.0px;
  border-left-width: 0.0px;
  border-style: solid;
  border-radius: 2.0px;
}

.container-text-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 4.0px 12.0px;
}

.text-label {
  color: #3b69ff;
  font-family: Bradesco Sans;
  font-weight: 600;
  font-size: 12.0px;
  line-height: 1.3333333333333333em;
  text-align: left;
}

.container-content {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 4.0px;
}

.text-content {
  color: #000000;
  font-family: Bradesco Sans;
  font-weight: 500;
  font-size: 16.0px;
  line-height: 1.25em;
  text-align: left;
}

.container-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.element-icon {
  background-color: #fafbff;
}

.icon-component-close-delete-path {
  background-color: #3b69ff;
}

.shape-divider {
  background-color: #d9dcdd;
}

.icon-component-search-path {
  background-color: #3b69ff;
}

.element-pointer {
  border-color: #000000;
  border-width: 1.0px;
  border-style: solid;
}

.element-dropdown {
  box-shadow: 0.0px 8.0px 16.0px rgba(0, 0, 0, 0.1599999964237213);
}

.container-list-container {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 4.0px 4.0px 0.0px 0.0px;
}

.element-list-item {
  background-color: #ffffff;
}

.container-main-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8.0px;
}

.container-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  gap: 10.0px;
  padding: 16.0px 14.0px;
  background-color: #ffffff;
  box-shadow: 0.0px 8.0px 16.0px rgba(0, 0, 0, 0.1599999964237213);
  border-radius: 0.0px 0.0px 4.0px 4.0px;
}

.element-button {
  background-color: #cc092f;
  border-radius: 999.0px;
}

.text-hint-text {
  color: #6d6e71;
  font-family: Bradesco Sans;
  font-weight: 500;
  font-size: 16.0px;
  line-height: 1.25em;
  text-align: left;
}