<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SAT CONTABILIDADE</title>
    <style>
:root {
  --stroke-f7f3cb41-color: #9747ff;
  --stroke-f7f3cb41-width: 1.0px;
  --stroke-f7f3cb41-style: dashed;
  --fill-b2d4e19d: #fafbff;
  --stroke-8b43a06e-color: #47484c;
  --stroke-8b43a06e-top-width: 0.0px;
  --stroke-8b43a06e-right-width: 0.0px;
  --stroke-8b43a06e-bottom-width: 2.0px;
  --stroke-8b43a06e-left-width: 0.0px;
  --stroke-8b43a06e-style: solid;
  --style-2307eb9b-font-family: Bradesco Sans;
  --style-2307eb9b-font-weight: 600;
  --style-2307eb9b-font-size: 12.0;
  --style-2307eb9b-line-height: 1.3333333333333333em;
  --style-2307eb9b-text-align-horizontal: left;
  --style-2307eb9b-text-align-vertical: top;
  --fill-64a0e084: #47484c;
  --style-1816976b-font-family: Bradesco Sans;
  --style-1816976b-font-weight: 500;
  --style-1816976b-font-size: 16.0;
  --style-1816976b-line-height: 1.25em;
  --style-1816976b-text-align-horizontal: left;
  --style-1816976b-text-align-vertical: top;
  --fill-dac60cb0: #000000;
  --fill-e18bd0be: #3b69ff;
  --fill-a5eed152: #d9dcdd;
  --stroke-626ad390-color: #a7a8ac;
  --stroke-626ad390-top-width: 0.0px;
  --stroke-626ad390-right-width: 0.0px;
  --stroke-626ad390-bottom-width: 2.0px;
  --stroke-626ad390-left-width: 0.0px;
  --stroke-626ad390-style: solid;
  --fill-f6dc0550: #6d6e71;
  --stroke-7906ef1b-color: #3b69ff;
  --stroke-7906ef1b-top-width: 0.0px;
  --stroke-7906ef1b-right-width: 0.0px;
  --stroke-7906ef1b-bottom-width: 2.0px;
  --stroke-7906ef1b-left-width: 0.0px;
  --stroke-7906ef1b-style: solid;
  --stroke-d2c048c6-color: #000000;
  --stroke-d2c048c6-width: 1.0px;
  --stroke-d2c048c6-style: solid;
  --effect-7de7775c-box-shadow: 0.0px 8.0px 16.0px rgba(0, 0, 0, 0.1599999964237213);
  --fill-3acfdca0: #ffffff;
  --style-bf0b927c-font-family: Bradesco Sans;
  --style-bf0b927c-font-weight: 600;
  --style-bf0b927c-font-size: 16.0;
  --style-bf0b927c-line-height: 1.25em;
  --style-bf0b927c-text-align-horizontal: left;
  --style-bf0b927c-text-align-vertical: middle;
  --fill-cca01286: #cc092f;
  --style-94b5cec3-font-family: Bradesco Sans;
  --style-94b5cec3-font-weight: 700;
  --style-94b5cec3-font-size: 14.0;
  --style-94b5cec3-line-height: 1.1428571428571428em;
  --style-94b5cec3-text-align-horizontal: center;
  --style-94b5cec3-text-align-vertical: top;
  --style-965775aa-font-family: Bradesco Sans;
  --style-965775aa-font-weight: 500;
  --style-965775aa-font-size: 16.0;
  --style-965775aa-line-height: 1.25em;
  --style-965775aa-text-align-horizontal: left;
  --style-965775aa-text-align-vertical: bottom;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.element-busca-default-buscar-por-cliente {
  border-color: #9747ff;
  border-width: 1.0px;
  border-style: dashed;
  border-radius: 5.0px;
}

.container-input-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: #fafbff;
  border-color: #3b69ff;
  border-top-width: 0.0px;
  border-right-width: 0.0px;
  border-bottom-width: 2.0px;
  border-left-width: 0.0px;
  border-style: solid;
  border-radius: 2.0px;
}

.container-text-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 4.0px 12.0px;
}

.text-label {
  color: #3b69ff;
  font-family: Bradesco Sans;
  font-weight: 600;
  font-size: 12.0px;
  line-height: 1.3333333333333333em;
  text-align: left;
}

.container-content {
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  gap: 4.0px;
}

.text-content {
  color: #000000;
  font-family: Bradesco Sans;
  font-weight: 500;
  font-size: 16.0px;
  line-height: 1.25em;
  text-align: left;
}

.container-actions {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.element-icon {
  background-color: #fafbff;
}

.icon-component-close-delete-path {
  background-color: #3b69ff;
}

.shape-divider {
  background-color: #d9dcdd;
}

.icon-component-search-path {
  background-color: #3b69ff;
}

.element-pointer {
  border-color: #000000;
  border-width: 1.0px;
  border-style: solid;
}

.element-dropdown {
  box-shadow: 0.0px 8.0px 16.0px rgba(0, 0, 0, 0.1599999964237213);
}

.container-list-container {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 4.0px 4.0px 0.0px 0.0px;
}

.element-list-item {
  background-color: #ffffff;
}

.container-main-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 8.0px;
}

.container-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  gap: 10.0px;
  padding: 16.0px 14.0px;
  background-color: #ffffff;
  box-shadow: 0.0px 8.0px 16.0px rgba(0, 0, 0, 0.1599999964237213);
  border-radius: 0.0px 0.0px 4.0px 4.0px;
}

.element-button {
  background-color: #cc092f;
  border-radius: 999.0px;
}

.text-hint-text {
  color: #6d6e71;
  font-family: Bradesco Sans;
  font-weight: 500;
  font-size: 16.0px;
  line-height: 1.25em;
  text-align: left;
}

    </style>
</head>
<body>
<div class="element-busca-default-buscar-por-cliente" id="figma-16:4995">
    <div class="element-property-1-selecteed" id="figma-16:4996">
          <input class="element-text-field-search-static" id="figma-16:4997">
                  <input class="container-input-container" id="figma-I16:4997;12258:2244">
                            <div class="container-text-container" id="figma-I16:4997;12258:2245">
                                        <span class="text-label" id="figma-I16:4997;12258:2246">Cliente</span>
                                        <main class="container-content" id="figma-I16:4997;12258:2247">
                                                      <span class="text-content" id="figma-I16:4997;12258:2249">Zenith Alt | 99.ZEN.123/01DE-01</span>
                                        </main>
                            </div>
                            <div class="container-actions" id="figma-I16:4997;12258:2250">
                                        <div class="element-icon" id="figma-I16:4997;12258:2251">
                                                      <div class="element-component-close-delete" id="figma-I16:4997;12258:2251;12258:2414">
                                                                      <svg class="icon-component-close-delete-path" id="figma-I16:4997;12258:2251;12258:2414;2051:89"></svg>
                                                      </div>
                                        </div>
                                        <div class="shape-divider" id="figma-I16:4997;12258:2252"></div>
                                        <div class="element-icon" id="figma-I16:4997;12258:2253">
                                                      <div class="element-component-search" id="figma-I16:4997;12258:2253;12258:2414">
                                                                      <svg class="icon-component-search-path" id="figma-I16:4997;12258:2253;12258:2414;2051:95"></svg>
                                                      </div>
                                        </div>
                            </div>
                  </input>
          </input>
    </div>
    <div class="element-property-1-hover" id="figma-16:4998">
          <input class="element-text-field-search-static" id="figma-16:4999">
                  <input class="container-input-container" id="figma-I16:4999;12258:1788">
                            <div class="container-text-container" id="figma-I16:4999;12258:1789">
                                        <main class="container-content" id="figma-I16:4999;12258:1791">
                                                      <span class="text-label" id="figma-I16:4999;12258:1792">Cliente</span>
                                        </main>
                            </div>
                            <div class="container-actions" id="figma-I16:4999;12258:1794">
                                        <div class="shape-divider" id="figma-I16:4999;12258:1796"></div>
                                        <div class="element-icon" id="figma-I16:4999;12258:1797">
                                                      <div class="element-component-search" id="figma-I16:4999;12258:1797;12258:2410">
                                                                      <svg class="icon-component-search-path" id="figma-I16:4999;12258:1797;12258:2410;2051:95"></svg>
                                                      </div>
                                        </div>
                            </div>
                  </input>
          </input>
    </div>
    <div class="element-property-1-default" id="figma-16:5000">
          <input class="element-text-field-search-static" id="figma-16:5001">
                  <input class="container-input-container" id="figma-I16:5001;12258:1788">
                            <div class="container-text-container" id="figma-I16:5001;12258:1789">
                                        <main class="container-content" id="figma-I16:5001;12258:1791">
                                                      <span class="text-label" id="figma-I16:5001;12258:1792">Cliente</span>
                                        </main>
                            </div>
                            <div class="container-actions" id="figma-I16:5001;12258:1794">
                                        <div class="shape-divider" id="figma-I16:5001;12258:1796"></div>
                                        <div class="element-icon" id="figma-I16:5001;12258:1797">
                                                      <div class="element-component-search" id="figma-I16:5001;12258:1797;12258:2410">
                                                                      <svg class="icon-component-search-path" id="figma-I16:5001;12258:1797;12258:2410;2051:95"></svg>
                                                      </div>
                                        </div>
                            </div>
                  </input>
          </input>
    </div>
    <div class="element-property-1-filled" id="figma-16:5002">
          <input class="element-text-field-search-static" id="figma-16:5003">
                  <input class="container-input-container" id="figma-I16:5003;12258:2128">
                            <div class="container-text-container" id="figma-I16:5003;12258:2129">
                                        <span class="text-label" id="figma-I16:5003;12258:2130">Cliente</span>
                                        <main class="container-content" id="figma-I16:5003;12258:2131">
                                                      <span class="text-content" id="figma-I16:5003;12258:2133">Zeni</span>
                                                      <div class="element-pointer" id="figma-I16:5003;12258:2134"></div>
                                        </main>
                            </div>
                            <div class="container-actions" id="figma-I16:5003;12258:2136">
                                        <div class="element-icon" id="figma-I16:5003;12258:2137">
                                                      <div class="element-component-close-delete" id="figma-I16:5003;12258:2137;12258:2414">
                                                                      <svg class="icon-component-close-delete-path" id="figma-I16:5003;12258:2137;12258:2414;2051:89"></svg>
                                                      </div>
                                        </div>
                                        <div class="shape-divider" id="figma-I16:5003;12258:2138"></div>
                                        <div class="element-icon" id="figma-I16:5003;12258:2139">
                                                      <div class="element-component-search" id="figma-I16:5003;12258:2139;12258:2414">
                                                                      <svg class="icon-component-search-path" id="figma-I16:5003;12258:2139;12258:2414;2051:95"></svg>
                                                      </div>
                                        </div>
                            </div>
                  </input>
          </input>
          <div class="element-dropdown" id="figma-16:5004">
                  <div class="container-list-container" id="figma-I16:5004;5811:2517">
                            <div class="element-list-item" id="figma-I16:5004;5811:1032">
                                        <main class="container-main-content" id="figma-I16:5004;5811:1032;5330:30287">
                                                      <span class="text-label" id="figma-I16:5004;5811:1032;5330:30288">Zenith Alt | 99.ZEN.123/01DE-01</span>
                                        </main>
                            </div>
                            <div class="element-list-item" id="figma-I16:5004;5811:1033">
                                        <main class="container-main-content" id="figma-I16:5004;5811:1033;5330:30287">
                                                      <span class="text-label" id="figma-I16:5004;5811:1033;5330:30288">Zenith Brasil | 99.ZEN.123/01DE-02</span>
                                        </main>
                            </div>
                            <div class="element-list-item" id="figma-I16:5004;5811:1034">
                                        <main class="container-main-content" id="figma-I16:5004;5811:1034;5330:30287">
                                                      <span class="text-label" id="figma-I16:5004;5811:1034;5330:30288">Zenith Capital | 99.ZEN.123/01DE-03</span>
                                        </main>
                            </div>
                            <div class="element-list-item" id="figma-I16:5004;5811:1035">
                                        <main class="container-main-content" id="figma-I16:5004;5811:1035;5330:30287">
                                                      <span class="text-label" id="figma-I16:5004;5811:1035;5330:30288">Zenith Discovery | 99.ZEN.123/01DE-04</span>
                                        </main>
                            </div>
                            <div class="element-list-item" id="figma-I16:5004;9143:9967">
                                        <main class="container-main-content" id="figma-I16:5004;9143:9967;5330:30287">
                                                      <span class="text-label" id="figma-I16:5004;9143:9967;5330:30288">Zenith Enterprise | 99.ZEN.123/01DE-05</span>
                                        </main>
                            </div>
                            <div class="element-list-item" id="figma-I16:5004;9143:9968">
                                        <main class="container-main-content" id="figma-I16:5004;9143:9968;5330:30287">
                                                      <span class="text-label" id="figma-I16:5004;9143:9968;5330:30288">Zenith Florest | 99.ZEN.123/01DE-06</span>
                                        </main>
                            </div>
                            <div class="element-list-item" id="figma-I16:5004;9143:9969">
                                        <main class="container-main-content" id="figma-I16:5004;9143:9969;5330:30287">
                                                      <span class="text-label" id="figma-I16:5004;9143:9969;5330:30288">Zenith Terra | 99.ZEN.123/01DE-07</span>
                                        </main>
                            </div>
                            <div class="element-list-item" id="figma-I16:5004;9143:9970">
                                        <main class="container-main-content" id="figma-I16:5004;9143:9970;5330:30287">
                                                      <span class="text-label" id="figma-I16:5004;9143:9970;5330:30288">Zenith Zero | 99.ZEN.123/01DE-08</span>
                                        </main>
                            </div>
                  </div>
                  <div class="container-container" id="figma-I16:5004;5811:1083">
                            <button class="element-button" id="figma-I16:5004;5811:1084">
                                        <span class="text-label" id="figma-I16:5004;5811:1084;2104:1411">Buscar</span>
                            </button>
                  </div>
          </div>
    </div>
    <div class="element-property-1-focused" id="figma-16:5005">
          <input class="element-text-field-search-static" id="figma-16:5006">
                  <input class="container-input-container" id="figma-I16:5006;12258:2004">
                            <div class="container-text-container" id="figma-I16:5006;12258:2005">
                                        <span class="text-label" id="figma-I16:5006;12258:2006">Cliente</span>
                                        <main class="container-content" id="figma-I16:5006;12258:2007">
                                                      <div class="element-pointer" id="figma-I16:5006;12258:2010"></div>
                                                      <span class="text-hint-text" id="figma-I16:5006;12258:2011">Informe</span>
                                        </main>
                            </div>
                            <div class="container-actions" id="figma-I16:5006;12258:2012">
                                        <div class="shape-divider" id="figma-I16:5006;12258:2014"></div>
                                        <div class="element-icon" id="figma-I16:5006;12258:2015">
                                                      <div class="element-component-search" id="figma-I16:5006;12258:2015;12258:2414">
                                                                      <svg class="icon-component-search-path" id="figma-I16:5006;12258:2015;12258:2414;2051:95"></svg>
                                                      </div>
                                        </div>
                            </div>
                  </input>
          </input>
    </div>
</div>
</body>
</html>