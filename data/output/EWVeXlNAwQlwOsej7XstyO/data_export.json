{"export_info": {"timestamp": "2025-06-27T01:15:47.224613", "project_id": "EWVeXlNAwQlwOsej7XstyO", "database_path": "data/output/EWVeXlNAwQlwOsej7XstyO/figma_data.db"}, "projects": [{"id": "EWVeXlNAwQlwOsej7XstyO", "name": "SAT CONTABILIDADE", "version": "2233664422745476190", "last_modified": "2025-06-25T18:56:28Z", "discovery_date": "2025-06-27T01:15:47.206976", "discovery_params": "{\"file_key\": \"EWVeXlNAwQlwOsej7XstyO\", \"max_depth\": 3, \"selected_pages\": [{\"id\": \"0:1\", \"name\": \"Fluxo SAT Contabilidade\"}], \"total_pages_processed\": 1}"}], "nodes": [{"id": "404:5587", "name": "1.0 Gestão de Empresas Originadoras -  Inicial", "type": "FRAME", "node_type": "frame", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 1, "has_layout": 0, "has_content": 0, "estimated_complexity": "low", "description": "Container/Frame | 1 filhos", "created_at": "2025-06-27T00:44:41.719488"}, {"id": "13:27245", "name": "ADM X DIRETOR X CONTADOR", "type": "SECTION", "node_type": "other", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 5, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento | 5 filhos", "created_at": "2025-06-27T00:44:41.718869"}, {"id": "404:9455", "name": "Atualização de componentes do LIQUID", "type": "TEXT", "node_type": "text", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 0, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento de texto | texto: \"Atualização de componentes do ...\"", "created_at": "2025-06-27T00:44:41.719540"}, {"id": "13:27244", "name": "CADASTRO CONTADOR", "type": "SECTION", "node_type": "other", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 9, "has_layout": 0, "has_content": 1, "estimated_complexity": "medium", "description": "Elemento | 9 filhos", "created_at": "2025-06-27T00:44:41.718310"}, {"id": "13:27243", "name": "CADASTRO DIRETOR", "type": "SECTION", "node_type": "other", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 10, "has_layout": 0, "has_content": 1, "estimated_complexity": "medium", "description": "Elemento | 10 filhos", "created_at": "2025-06-27T00:44:41.717606"}, {"id": "262:12294", "name": "Cadastro de notas Explicativas", "type": "SECTION", "node_type": "other", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 1, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento | 1 filhos", "created_at": "2025-06-27T00:44:41.719369"}, {"id": "262:12409", "name": "Campos a inserir (inicial)[CNPJ CARTEIRA] [ADMINISTRADOR CARTEIRA] [CNPJ ADMINISTRADOR][PUBLICO ALVO] [OBJETIVO]", "type": "TEXT", "node_type": "text", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 0, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento de texto | texto: \"Campos a inserir (inicial)[...\"", "created_at": "2025-06-27T00:44:41.719440"}, {"id": "13:27242", "name": "DEMONSTRAÇÃO FINANCEIRA", "type": "SECTION", "node_type": "other", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 4, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento | 4 filhos", "created_at": "2025-06-27T00:44:41.717038"}, {"id": "20:11049", "name": "<PERSON><PERSON><PERSON>", "type": "SECTION", "node_type": "other", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 1, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento | 1 filhos", "created_at": "2025-06-27T00:44:41.719276"}, {"id": "262:12410", "name": "Nome da NotaOrdem da notas", "type": "TEXT", "node_type": "text", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 0, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento de texto | texto: \"Nome da NotaOrdem da notas\"", "created_at": "2025-06-27T00:44:41.719455"}, {"id": "404:9456", "name": "Rectangle 9712", "type": "RECTANGLE", "node_type": "shape", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 0, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Forma/Shape", "created_at": "2025-06-27T00:44:41.716560"}, {"id": "262:12411", "name": "Tipo de fundo que essa nota contempla6 tipos de fundoFIFCLASSEFIDCFIPFIIOffshore", "type": "TEXT", "node_type": "text", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "children_count": 0, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento de texto | texto: \"Tipo de fundo que essa nota co...\"", "created_at": "2025-06-27T00:44:41.719473"}, {"id": "404:5588", "name": "1.0 Gestão de Empresas Originadoras -  Inicial", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "404:5587", "parent_name": "1.0 Gestão de Empresas Originadoras -  Inicial", "children_count": 1, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 1 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719503"}, {"id": "1:15519", "name": "1.0 SAT Contabilidade -  Lista", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27242", "parent_name": "DEMONSTRAÇÃO FINANCEIRA", "children_count": 4, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717065"}, {"id": "20:11666", "name": "1.0 SAT Contabilidade -  Lista", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27242", "parent_name": "DEMONSTRAÇÃO FINANCEIRA", "children_count": 4, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717145"}, {"id": "13:27246", "name": "1.2 SAT Contabilidade - Carregamento", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27242", "parent_name": "DEMONSTRAÇÃO FINANCEIRA", "children_count": 4, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T01:15:47.208600"}, {"id": "1:42952", "name": "2.0 Cadastro contador", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 4, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718325"}, {"id": "1:42445", "name": "2.0 Dashboard Cadastro diretor", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 4, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717621"}, {"id": "1:63016", "name": "4.0 Vinculo Adm x diretor x contador", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "children_count": 4, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718884"}, {"id": "20:11551", "name": "5.0 SAT Contabilidade -  serviço indisponivel", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "20:11049", "parent_name": "<PERSON><PERSON><PERSON>", "children_count": 4, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719291"}, {"id": "16:4995", "name": "Busca default - buscar por Cliente", "type": "COMPONENT_SET", "node_type": "other", "level": 2, "parent_id": "13:27242", "parent_name": "DEMONSTRAÇÃO FINANCEIRA", "children_count": 5, "has_layout": 0, "has_content": 1, "estimated_complexity": "low", "description": "Elemento | 5 filhos", "created_at": "2025-06-27T00:44:41.717422"}, {"id": "649:6568", "name": "Cadastrar Notas Explicativas", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "262:12294", "parent_name": "Cadastro de notas Explicativas", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719383"}, {"id": "336:8294", "name": "Excluir Diretor", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 5, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 5 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718158"}, {"id": "336:5857", "name": "Excluir contador", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 5, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 5 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718780"}, {"id": "13:7203", "name": "Modal - Cadastro novo diretor", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717692"}, {"id": "20:12182", "name": "Modal - Cadastro novo diretor - Erro ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717748"}, {"id": "13:7367", "name": "Modal - Edição novo diretor", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717873"}, {"id": "13:7453", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718399"}, {"id": "13:7733", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718455"}, {"id": "13:12118", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719058"}, {"id": "160:28357", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719160"}, {"id": "160:28268", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719221"}, {"id": "13:7219", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717928"}, {"id": "13:7438", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717986"}, {"id": "1:65131", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718966"}, {"id": "336:8292", "name": "Snackbar - Exclusão com sucesso", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 1, "has_layout": 0, "has_content": 0, "estimated_complexity": "low", "description": "Container/Frame | 1 filhos", "created_at": "2025-06-27T00:44:41.718129"}, {"id": "336:8291", "name": "Snackbar - Exclusão com sucesso", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 1, "has_layout": 0, "has_content": 0, "estimated_complexity": "low", "description": "Container/Frame | 1 filhos", "created_at": "2025-06-27T00:44:41.718585"}, {"id": "336:8289", "name": "Snackbar - cadastro criado com sucesso", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 1, "has_layout": 0, "has_content": 0, "estimated_complexity": "low", "description": "Container/Frame | 1 filhos", "created_at": "2025-06-27T00:44:41.718510"}, {"id": "336:8290", "name": "Snackbar - edição salva com sucesso", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 1, "has_layout": 0, "has_content": 0, "estimated_complexity": "low", "description": "Container/Frame | 1 filhos", "created_at": "2025-06-27T00:44:41.718556"}, {"id": "224:25747", "name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718682"}, {"id": "224:25758", "name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718730"}, {"id": "20:12592", "name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718045"}, {"id": "20:12614", "name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718087"}, {"id": "20:12602", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:12592", "parent_name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718073"}, {"id": "20:12624", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:12614", "parent_name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718115"}, {"id": "336:8314", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718283"}, {"id": "224:25757", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "224:25747", "parent_name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718711"}, {"id": "224:25768", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "224:25758", "parent_name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718766"}, {"id": "336:5877", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718838"}, {"id": "649:6571", "name": "Body", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "649:6568", "parent_name": "Cadastrar Notas Explicativas", "children_count": 6, "has_layout": 1, "has_content": 1, "estimated_complexity": "high", "description": "Container/Frame | 6 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719426"}, {"id": "I13:7219;2808:1305", "name": "Content", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7219", "parent_name": "Snackbar", "children_count": 3, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717956"}, {"id": "I13:7438;2808:1305", "name": "Content", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7438", "parent_name": "Snackbar", "children_count": 3, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718017"}, {"id": "I1:65131;2808:1305", "name": "Content", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:65131", "parent_name": "Snackbar", "children_count": 3, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719013"}, {"id": "13:7213", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7203", "parent_name": "Modal - Cadastro novo diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717720"}, {"id": "20:12192", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12182", "parent_name": "Modal - Cadastro novo diretor - Erro ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717844"}, {"id": "13:7377", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7367", "parent_name": "Modal - Edição novo diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717901"}, {"id": "13:7463", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7453", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718427"}, {"id": "13:7743", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7733", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718483"}, {"id": "13:12128", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:12118", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719111"}, {"id": "160:28367", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28357", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719193"}, {"id": "160:28278", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28268", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719247"}, {"id": "336:8304", "name": "<PERSON><PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "children_count": 1, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718252"}, {"id": "336:5867", "name": "<PERSON><PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "children_count": 1, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718809"}, {"id": "16:5000", "name": "Property 1=Default", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "children_count": 1, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Componente reutilizável | 1 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717545"}, {"id": "16:5002", "name": "Property 1=Filled", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Componente reutilizável | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717572"}, {"id": "16:5005", "name": "Property 1=Focused", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Componente reutilizável | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717592"}, {"id": "16:4998", "name": "Property 1=Hover", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "children_count": 1, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Componente reutilizável | 1 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717453"}, {"id": "16:4996", "name": "Property 1=Selecteed", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "children_count": 1, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Componente reutilizável | 1 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717437"}, {"id": "336:8293", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8292", "parent_name": "Snackbar - Exclusão com sucesso", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718143"}, {"id": "13:7489", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8289", "parent_name": "Snackbar - cadastro criado com sucesso", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718524"}, {"id": "13:7490", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8290", "parent_name": "Snackbar - edição salva com sucesso", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718571"}, {"id": "336:8274", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8291", "parent_name": "Snackbar - Exclusão com sucesso", "children_count": 3, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718660"}, {"id": "336:8305", "name": "Table-fundos", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718269"}, {"id": "336:5868", "name": "Table-fundos", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718824"}, {"id": "404:5589", "name": "Tela Inicial", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "404:5588", "parent_name": "1.0 Gestão de Empresas Originadoras -  Inicial", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719516"}, {"id": "1:15610", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:15519", "parent_name": "1.0 SAT Contabilidade -  Lista", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717131"}, {"id": "20:11758", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11666", "parent_name": "1.0 SAT Contabilidade -  Lista", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717327"}, {"id": "13:27338", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:27246", "parent_name": "1.2 SAT Contabilidade - Carregamento", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717408"}, {"id": "1:42537", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42445", "parent_name": "2.0 Dashboard Cadastro diretor", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717678"}, {"id": "1:43045", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42952", "parent_name": "2.0 Cadastro contador", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718382"}, {"id": "1:63109", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:63016", "parent_name": "4.0 Vinculo Adm x diretor x contador", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718944"}, {"id": "20:11556", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11551", "parent_name": "5.0 SAT Contabilidade -  serviço indisponivel", "children_count": 2, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719355"}, {"id": "13:7216", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7203", "parent_name": "Modal - Cadastro novo diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717735"}, {"id": "20:12197", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12182", "parent_name": "Modal - Cadastro novo diretor - Erro ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717859"}, {"id": "13:7382", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7367", "parent_name": "Modal - Edição novo diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717915"}, {"id": "336:8315", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718296"}, {"id": "13:7468", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7453", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718441"}, {"id": "13:7750", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7733", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718496"}, {"id": "336:5878", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718855"}, {"id": "13:12131", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:12118", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.719135"}, {"id": "160:28370", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28357", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.719208"}, {"id": "160:28281", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28268", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.719262"}, {"id": "1:15521", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:15519", "parent_name": "1.0 SAT Contabilidade -  Lista", "children_count": 4, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717116"}, {"id": "20:11669", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:11666", "parent_name": "1.0 SAT Contabilidade -  Lista", "children_count": 4, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717189"}, {"id": "13:27249", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:27246", "parent_name": "1.2 SAT Contabilidade - Carregamento", "children_count": 1, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 1 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717394"}, {"id": "1:42448", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:42445", "parent_name": "2.0 Dashboard Cadastro diretor", "children_count": 3, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717664"}, {"id": "8:5855", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:42952", "parent_name": "2.0 Cadastro contador", "children_count": 3, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718367"}, {"id": "1:63019", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:63016", "parent_name": "4.0 Vinculo Adm x diretor x contador", "children_count": 4, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718926"}, {"id": "20:11554", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:11551", "parent_name": "5.0 SAT Contabilidade -  serviço indisponivel", "children_count": 1, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 1 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719340"}, {"id": "I13:7219;2808:1309", "name": "component-close-delete", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:7219", "parent_name": "Snackbar", "children_count": 1, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos", "created_at": "2025-06-27T00:44:41.717971"}, {"id": "I13:7438;2808:1309", "name": "component-close-delete", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:7438", "parent_name": "Snackbar", "children_count": 1, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos", "created_at": "2025-06-27T00:44:41.718031"}, {"id": "I1:65131;2808:1309", "name": "component-close-delete", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:65131", "parent_name": "Snackbar", "children_count": 1, "has_layout": 1, "has_content": 1, "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos", "created_at": "2025-06-27T00:44:41.719035"}, {"id": "1:15520", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:15519", "parent_name": "1.0 SAT Contabilidade -  Lista", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717083"}, {"id": "20:11667", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11666", "parent_name": "1.0 SAT Contabilidade -  Lista", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717159"}, {"id": "13:27247", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:27246", "parent_name": "1.2 SAT Contabilidade - Carregamento", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717365"}, {"id": "1:42446", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42445", "parent_name": "2.0 Dashboard Cadastro diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.717635"}, {"id": "1:42953", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42952", "parent_name": "2.0 Cadastro contador", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718339"}, {"id": "1:63017", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:63016", "parent_name": "4.0 Vinculo Adm x diretor x contador", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.718898"}, {"id": "20:11552", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11551", "parent_name": "5.0 SAT Contabilidade -  serviço indisponivel", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719309"}, {"id": "649:6569", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "649:6568", "parent_name": "Cadastrar Notas Explicativas", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical", "created_at": "2025-06-27T00:44:41.719398"}, {"id": "I13:7219;2808:1304", "name": "feedback-check-box", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:7219", "parent_name": "Snackbar", "children_count": 1, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos", "created_at": "2025-06-27T00:44:41.717942"}, {"id": "I13:7438;2808:1304", "name": "feedback-check-box", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:7438", "parent_name": "Snackbar", "children_count": 1, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos", "created_at": "2025-06-27T00:44:41.718003"}, {"id": "I1:65131;2808:1304", "name": "feedback-check-box", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:65131", "parent_name": "Snackbar", "children_count": 1, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos", "created_at": "2025-06-27T00:44:41.718990"}, {"id": "13:7204", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7203", "parent_name": "Modal - Cadastro novo diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717706"}, {"id": "20:12183", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12182", "parent_name": "Modal - Cadastro novo diretor - Erro ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717822"}, {"id": "13:7368", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7367", "parent_name": "Modal - Edição novo diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717887"}, {"id": "20:12593", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12592", "parent_name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718059"}, {"id": "20:12615", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12614", "parent_name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718101"}, {"id": "336:8295", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718228"}, {"id": "13:7454", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7453", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718413"}, {"id": "13:7734", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7733", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718469"}, {"id": "224:25748", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "224:25747", "parent_name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718697"}, {"id": "224:25759", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "224:25758", "parent_name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718751"}, {"id": "336:5858", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718795"}, {"id": "13:12119", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:12118", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.719088"}, {"id": "160:28358", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28357", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.719179"}, {"id": "160:28269", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28268", "parent_name": "Modal - Vinculo ", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.719234"}, {"id": "1:41750", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:15519", "parent_name": "1.0 SAT Contabilidade -  Lista", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717101"}, {"id": "20:11668", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11666", "parent_name": "1.0 SAT Contabilidade -  Lista", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717174"}, {"id": "13:27248", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:27246", "parent_name": "1.2 SAT Contabilidade - Carregamento", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717380"}, {"id": "1:42447", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42445", "parent_name": "2.0 Dashboard Cadastro diretor", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.717650"}, {"id": "1:42954", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42952", "parent_name": "2.0 Cadastro contador", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718353"}, {"id": "1:63018", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:63016", "parent_name": "4.0 Vinculo Adm x diretor x contador", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.718912"}, {"id": "20:11553", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11551", "parent_name": "5.0 SAT Contabilidade -  serviço indisponivel", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.719325"}, {"id": "649:6570", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "649:6568", "parent_name": "Cadastrar Notas Explicativas", "children_count": 2, "has_layout": 1, "has_content": 0, "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal", "created_at": "2025-06-27T00:44:41.719412"}], "components": [{"id": "404:5587", "name": "1.0 Gestão de Empresas Originadoras -  Inicial", "type": "FRAME", "node_type": "frame", "level": 1, "parent_id": "0:1", "parent_name": "Page_0:1", "estimated_complexity": "low", "description": "Container/Frame | 1 filhos"}, {"id": "1:15519", "name": "1.0 SAT Contabilidade -  Lista", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27242", "parent_name": "DEMONSTRAÇÃO FINANCEIRA", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "20:11666", "name": "1.0 SAT Contabilidade -  Lista", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27242", "parent_name": "DEMONSTRAÇÃO FINANCEIRA", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "1:42445", "name": "2.0 Dashboard Cadastro diretor", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "13:7203", "name": "Modal - Cadastro novo diretor", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "20:12182", "name": "Modal - Cadastro novo diretor - Erro ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "13:7367", "name": "Modal - Edição novo diretor", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "20:12592", "name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "20:12614", "name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "336:8294", "name": "Excluir Diretor", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Container/Frame | 5 filhos | layout vertical"}, {"id": "1:42952", "name": "2.0 Cadastro contador", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "13:7453", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "13:7733", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "224:25747", "name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "224:25758", "name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "336:5857", "name": "Excluir contador", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 5 filhos | layout vertical"}, {"id": "1:63016", "name": "4.0 Vinculo Adm x diretor x contador", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "13:12118", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "160:28357", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "160:28268", "name": "Modal - Vinculo ", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "20:11551", "name": "5.0 SAT Contabilidade -  serviço indisponivel", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "20:11049", "parent_name": "<PERSON><PERSON><PERSON>", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "649:6568", "name": "Cadastrar Notas Explicativas", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "262:12294", "parent_name": "Cadastro de notas Explicativas", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "404:5588", "name": "1.0 Gestão de Empresas Originadoras -  Inicial", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "404:5587", "parent_name": "1.0 Gestão de Empresas Originadoras -  Inicial", "estimated_complexity": "medium", "description": "Container/Frame | 1 filhos | layout vertical"}, {"id": "13:27246", "name": "1.2 SAT Contabilidade - Carregamento", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27242", "parent_name": "DEMONSTRAÇÃO FINANCEIRA", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "13:7219", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal"}, {"id": "13:7438", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal"}, {"id": "1:65131", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 2, "parent_id": "13:27245", "parent_name": "ADM X DIRETOR X CONTADOR", "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal"}, {"id": "336:8292", "name": "Snackbar - Exclusão com sucesso", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27243", "parent_name": "CADASTRO DIRETOR", "estimated_complexity": "low", "description": "Container/Frame | 1 filhos"}, {"id": "336:8289", "name": "Snackbar - cadastro criado com sucesso", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "low", "description": "Container/Frame | 1 filhos"}, {"id": "336:8290", "name": "Snackbar - edição salva com sucesso", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "low", "description": "Container/Frame | 1 filhos"}, {"id": "336:8291", "name": "Snackbar - Exclusão com sucesso", "type": "FRAME", "node_type": "frame", "level": 2, "parent_id": "13:27244", "parent_name": "CADASTRO CONTADOR", "estimated_complexity": "low", "description": "Container/Frame | 1 filhos"}, {"id": "16:4996", "name": "Property 1=Selecteed", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "estimated_complexity": "medium", "description": "Componente reutilizável | 1 filhos | layout vertical"}, {"id": "16:4998", "name": "Property 1=Hover", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "estimated_complexity": "medium", "description": "Componente reutilizável | 1 filhos | layout vertical"}, {"id": "16:5000", "name": "Property 1=Default", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "estimated_complexity": "medium", "description": "Componente reutilizável | 1 filhos | layout vertical"}, {"id": "16:5002", "name": "Property 1=Filled", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "estimated_complexity": "medium", "description": "Componente reutilizável | 2 filhos | layout vertical"}, {"id": "16:5005", "name": "Property 1=Focused", "type": "COMPONENT", "node_type": "component", "level": 3, "parent_id": "16:4995", "parent_name": "Busca default - buscar por Cliente", "estimated_complexity": "medium", "description": "Componente reutilizável | 2 filhos | layout vertical"}, {"id": "1:15521", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:15519", "parent_name": "1.0 SAT Contabilidade -  Lista", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "20:11669", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:11666", "parent_name": "1.0 SAT Contabilidade -  Lista", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "13:27249", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:27246", "parent_name": "1.2 SAT Contabilidade - Carregamento", "estimated_complexity": "medium", "description": "Container/Frame | 1 filhos | layout vertical"}, {"id": "1:42448", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:42445", "parent_name": "2.0 Dashboard Cadastro diretor", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "13:7204", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7203", "parent_name": "Modal - Cadastro novo diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "13:7213", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7203", "parent_name": "Modal - Cadastro novo diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "13:7216", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7203", "parent_name": "Modal - Cadastro novo diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "20:12183", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12182", "parent_name": "Modal - Cadastro novo diretor - Erro ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "20:12192", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12182", "parent_name": "Modal - Cadastro novo diretor - Erro ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "20:12197", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12182", "parent_name": "Modal - Cadastro novo diretor - Erro ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "13:7368", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7367", "parent_name": "Modal - Edição novo diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "13:7377", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7367", "parent_name": "Modal - Edição novo diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "13:7382", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7367", "parent_name": "Modal - Edição novo diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "I13:7219;2808:1305", "name": "Content", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7219", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "I13:7438;2808:1305", "name": "Content", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7438", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "20:12593", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12592", "parent_name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "20:12615", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:12614", "parent_name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "336:8295", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "336:8305", "name": "Table-fundos", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "336:8315", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "8:5855", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:42952", "parent_name": "2.0 Cadastro contador", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "13:7454", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7453", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "13:7463", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7453", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "13:7468", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7453", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "13:7734", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7733", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "13:7743", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7733", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "13:7750", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:7733", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "224:25748", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "224:25747", "parent_name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "224:25759", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "224:25758", "parent_name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "336:5858", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "336:5868", "name": "Table-fundos", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "336:5878", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "1:63019", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:63016", "parent_name": "4.0 Vinculo Adm x diretor x contador", "estimated_complexity": "medium", "description": "Container/Frame | 4 filhos | layout vertical"}, {"id": "I1:65131;2808:1305", "name": "Content", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "1:65131", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Container/Frame | 3 filhos | layout vertical"}, {"id": "13:12119", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:12118", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "13:12128", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:12118", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "13:12131", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "13:12118", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "160:28358", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28357", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "160:28367", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28357", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "160:28370", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28357", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "160:28269", "name": "header", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28268", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "160:28278", "name": "Nova empresa", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28268", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout vertical"}, {"id": "160:28281", "name": "actions", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "160:28268", "parent_name": "Modal - Vinculo ", "estimated_complexity": "medium", "description": "Container/Frame | 2 filhos | layout horizontal"}, {"id": "20:11554", "name": "base", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "20:11551", "parent_name": "5.0 SAT Contabilidade -  serviço indisponivel", "estimated_complexity": "medium", "description": "Container/Frame | 1 filhos | layout vertical"}, {"id": "1:15520", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:15519", "parent_name": "1.0 SAT Contabilidade -  Lista", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "1:41750", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:15519", "parent_name": "1.0 SAT Contabilidade -  Lista", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "1:15610", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:15519", "parent_name": "1.0 SAT Contabilidade -  Lista", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "20:11667", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11666", "parent_name": "1.0 SAT Contabilidade -  Lista", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "20:11668", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11666", "parent_name": "1.0 SAT Contabilidade -  Lista", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "20:11758", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11666", "parent_name": "1.0 SAT Contabilidade -  Lista", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "13:27247", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:27246", "parent_name": "1.2 SAT Contabilidade - Carregamento", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "13:27248", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:27246", "parent_name": "1.2 SAT Contabilidade - Carregamento", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "13:27338", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:27246", "parent_name": "1.2 SAT Contabilidade - Carregamento", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "1:42446", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42445", "parent_name": "2.0 Dashboard Cadastro diretor", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "1:42447", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42445", "parent_name": "2.0 Dashboard Cadastro diretor", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "1:42537", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42445", "parent_name": "2.0 Dashboard Cadastro diretor", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "I13:7219;2808:1304", "name": "feedback-check-box", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:7219", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos"}, {"id": "I13:7219;2808:1309", "name": "component-close-delete", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:7219", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos"}, {"id": "I13:7438;2808:1304", "name": "feedback-check-box", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:7438", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos"}, {"id": "I13:7438;2808:1309", "name": "component-close-delete", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "13:7438", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos"}, {"id": "20:12602", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:12592", "parent_name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "20:12624", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:12614", "parent_name": "[<PERSON><PERSON>] Modal - Cadastro novo diretor - erro gen<PERSON><PERSON>o", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "336:8293", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8292", "parent_name": "Snackbar - Exclusão com sucesso", "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal"}, {"id": "336:8304", "name": "<PERSON><PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos | layout vertical"}, {"id": "336:8314", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8294", "parent_name": "Excluir Diretor", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "1:42953", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42952", "parent_name": "2.0 Cadastro contador", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "1:42954", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42952", "parent_name": "2.0 Cadastro contador", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "1:43045", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:42952", "parent_name": "2.0 Cadastro contador", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "13:7489", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8289", "parent_name": "Snackbar - cadastro criado com sucesso", "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal"}, {"id": "13:7490", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8290", "parent_name": "Snackbar - edição salva com sucesso", "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal"}, {"id": "336:8274", "name": "Snackbar", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:8291", "parent_name": "Snackbar - Exclusão com sucesso", "estimated_complexity": "medium", "description": "Instância de componente | 3 filhos | layout horizontal"}, {"id": "224:25757", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "224:25747", "parent_name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "224:25768", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "224:25758", "parent_name": "[<PERSON><PERSON>] <PERSON>dal - Cadastro novo contador - erro gen<PERSON><PERSON>o", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "336:5867", "name": "<PERSON><PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos | layout vertical"}, {"id": "336:5877", "name": "<PERSON><PERSON>", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "336:5857", "parent_name": "Excluir contador", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "1:63017", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:63016", "parent_name": "4.0 Vinculo Adm x diretor x contador", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "1:63018", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:63016", "parent_name": "4.0 Vinculo Adm x diretor x contador", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "1:63109", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:63016", "parent_name": "4.0 Vinculo Adm x diretor x contador", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "I1:65131;2808:1304", "name": "feedback-check-box", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:65131", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos"}, {"id": "I1:65131;2808:1309", "name": "component-close-delete", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "1:65131", "parent_name": "Snackbar", "estimated_complexity": "medium", "description": "Instância de componente | 1 filhos"}, {"id": "20:11552", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11551", "parent_name": "5.0 SAT Contabilidade -  serviço indisponivel", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "20:11553", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11551", "parent_name": "5.0 SAT Contabilidade -  serviço indisponivel", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "20:11556", "name": "acfi-footer", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "20:11551", "parent_name": "5.0 SAT Contabilidade -  serviço indisponivel", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "649:6569", "name": "csf-header-gest<PERSON> de acesso", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "649:6568", "parent_name": "Cadastrar Notas Explicativas", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "649:6570", "name": "menu-item-dropdown/default", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "649:6568", "parent_name": "Cadastrar Notas Explicativas", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout horizontal"}, {"id": "404:5589", "name": "Tela Inicial", "type": "INSTANCE", "node_type": "instance", "level": 3, "parent_id": "404:5588", "parent_name": "1.0 Gestão de Empresas Originadoras -  Inicial", "estimated_complexity": "medium", "description": "Instância de componente | 2 filhos | layout vertical"}, {"id": "649:6571", "name": "Body", "type": "FRAME", "node_type": "frame", "level": 3, "parent_id": "649:6568", "parent_name": "Cadastrar Notas Explicativas", "estimated_complexity": "high", "description": "Container/Frame | 6 filhos | layout vertical"}], "summary": {"total_nodes": 135, "total_components": 123, "nodes_by_type": {"frame": 73, "other": 7, "text": 4, "shape": 1, "instance": 45, "component": 5}, "nodes_by_level": {"level_1": 12, "level_2": 31, "level_3": 92}, "components_by_complexity": {"low": 5, "medium": 117, "high": 1}}}