{"name": "SAT CONTABILIDADE", "last_modified": "2025-06-25T18:56:28Z", "thumbnail_url": "https://s3-alpha.figma.com/thumbnails/6ddd596c-320c-455a-bdae-6628580c4fe5?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCZGWUDXPX%2F20250626%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250626T000000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=28d1665b9ee10c682f6d2144e9cfcf7aad744145859ef9174211be8120b9f34d", "nodes": [{"id": "16:4995", "name": "Busca default - buscar por Cliente", "type": "COMPONENT_SET", "bounding_box": {"x": 4365.0, "y": 3066.0, "width": 812.0, "height": 936.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": "stroke_f7f3cb41", "effects": null, "opacity": null, "border_radius": "5.0px", "layout": "layout_9b4c115b", "component_id": null, "component_properties": null, "children": [{"id": "16:4996", "name": "Property 1=Selecteed", "type": "COMPONENT", "bounding_box": {"x": 4390.0, "y": 3934.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_40163364", "component_id": null, "component_properties": null, "children": [{"id": "16:4997", "name": "Text Field Search [Static]", "type": "INSTANCE", "bounding_box": {"x": 4390.0, "y": 3934.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "13:19984", "component_properties": [{"name": "✏️ Content#3141:8", "value": "Zenith Alt | 99.ZEN.123/01DE-01", "type": "TEXT"}, {"name": "✏️ Label#3141:7", "value": "Cliente", "type": "TEXT"}, {"name": "✏️ Hint Text#3811:5", "value": "Hint Text", "type": "TEXT"}, {"name": "✏️ Helper Text#3141:6", "value": "Helper Text", "type": "TEXT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Hovered", "value": "False", "type": "VARIANT"}, {"name": "Is Filled", "value": "True", "type": "VARIANT"}, {"name": "Is Focused", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "<PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has Helper Text", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:4997;12258:2244", "name": "Input Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3934.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": "stroke_8b43a06e", "effects": null, "opacity": null, "border_radius": "2.0px", "layout": "layout_2fa345d1", "component_id": null, "component_properties": null, "children": [{"id": "I16:4997;12258:2245", "name": "Text Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3934.0, "width": 661.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_826ee542", "component_id": null, "component_properties": null, "children": [{"id": "I16:4997;12258:2246", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.0, "y": 3938.0, "width": 637.0, "height": 16.0}, "text": "Cliente", "text_style": "style_2307eb9b", "fills": "fill_64a0e084", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:4997;12258:2247", "name": "Content", "type": "FRAME", "bounding_box": {"x": 4402.0, "y": 3954.0, "width": 637.0, "height": 24.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_f0f11426", "component_id": null, "component_properties": null, "children": [{"id": "I16:4997;12258:2249", "name": "Content", "type": "TEXT", "bounding_box": {"x": 4402.0, "y": 3958.0, "width": 234.0, "height": 20.0}, "text": "Zenith Alt | 99.ZEN.123/01DE-01", "text_style": "style_1816976b", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_e875f8c6", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:4997;12258:2250", "name": "Actions", "type": "FRAME", "bounding_box": {"x": 5051.0, "y": 3934.0, "width": 97.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c7720fd3", "component_id": null, "component_properties": null, "children": [{"id": "I16:4997;12258:2251", "name": ".Icon", "type": "INSTANCE", "bounding_box": {"x": 5051.0, "y": 3934.0, "width": 48.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c9140640", "component_id": "1:2251", "component_properties": [{"name": "🚺 Icon#11276:0", "value": "1:2289", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "Focused", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:4997;12258:2251;12258:2414", "name": "component-close-delete", "type": "INSTANCE", "bounding_box": {"x": 5065.0, "y": 3948.0, "width": 20.0, "height": 20.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_99543f67", "component_id": "1:2289", "component_properties": null, "children": [{"id": "I16:4997;12258:2251;12258:2414;2051:89", "name": "component-close-delete-path", "type": "IMAGE-SVG", "bounding_box": {"x": 5067.5625, "y": 3950.562255859375, "width": 14.875089645385742, "height": 14.875089645385742}, "text": null, "text_style": null, "fills": "fill_e18bd0be", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_9b4c115b", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:4997;12258:2252", "name": "Divider", "type": "RECTANGLE", "bounding_box": {"x": 5099.0, "y": 3942.0, "width": 1.0, "height": 32.0}, "text": null, "text_style": null, "fills": "fill_a5eed152", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_cde2683d", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:4997;12258:2253", "name": ".Icon", "type": "INSTANCE", "bounding_box": {"x": 5100.0, "y": 3934.0, "width": 48.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c9140640", "component_id": "1:2251", "component_properties": [{"name": "🚺 Icon#11276:0", "value": "1:2244", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "Focused", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:4997;12258:2253;12258:2414", "name": "component-search", "type": "INSTANCE", "bounding_box": {"x": 5114.0, "y": 3948.0, "width": 20.0, "height": 20.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_99543f67", "component_id": "1:2244", "component_properties": null, "children": [{"id": "I16:4997;12258:2253;12258:2414;2051:95", "name": "component-search-path", "type": "IMAGE-SVG", "bounding_box": {"x": 5116.5576171875, "y": 3950.5634765625, "width": 14.872543334960938, "height": 14.87879753112793}, "text": null, "text_style": null, "fills": "fill_e18bd0be", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_9b4c115b", "component_id": null, "component_properties": null, "children": null}]}]}]}]}]}]}, {"id": "16:4998", "name": "Property 1=Hover", "type": "COMPONENT", "bounding_box": {"x": 4390.0, "y": 3174.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_40163364", "component_id": null, "component_properties": null, "children": [{"id": "16:4999", "name": "Text Field Search [Static]", "type": "INSTANCE", "bounding_box": {"x": 4390.0, "y": 3174.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "13:19536", "component_properties": [{"name": "✏️ Label#3141:7", "value": "Cliente", "type": "TEXT"}, {"name": "✏️ Content#3141:8", "value": "Content", "type": "TEXT"}, {"name": "✏️ Hint Text#3811:5", "value": "Informe", "type": "TEXT"}, {"name": "✏️ Helper Text#3141:6", "value": "Helper Text", "type": "TEXT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Hovered", "value": "False", "type": "VARIANT"}, {"name": "Is Filled", "value": "False", "type": "VARIANT"}, {"name": "Is Focused", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "<PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has Helper Text", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:4999;12258:1788", "name": "Input Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3174.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": "stroke_626ad390", "effects": null, "opacity": null, "border_radius": "2.0px", "layout": "layout_2fa345d1", "component_id": null, "component_properties": null, "children": [{"id": "I16:4999;12258:1789", "name": "Text Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3174.0, "width": 709.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_826ee542", "component_id": null, "component_properties": null, "children": [{"id": "I16:4999;12258:1791", "name": "Content", "type": "FRAME", "bounding_box": {"x": 4402.0, "y": 3186.0, "width": 685.0, "height": 24.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_f0f11426", "component_id": null, "component_properties": null, "children": [{"id": "I16:4999;12258:1792", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.0, "y": 3190.0, "width": 685.0, "height": 20.0}, "text": "Cliente", "text_style": "style_1816976b", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:4999;12258:1794", "name": "Actions", "type": "FRAME", "bounding_box": {"x": 5099.0, "y": 3174.0, "width": 49.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c7720fd3", "component_id": null, "component_properties": null, "children": [{"id": "I16:4999;12258:1796", "name": "Divider", "type": "RECTANGLE", "bounding_box": {"x": 5099.0, "y": 3182.0, "width": 1.0, "height": 32.0}, "text": null, "text_style": null, "fills": "fill_a5eed152", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_cde2683d", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:4999;12258:1797", "name": ".Icon", "type": "INSTANCE", "bounding_box": {"x": 5100.0, "y": 3174.0, "width": 48.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c9140640", "component_id": "1:2247", "component_properties": [{"name": "🚺 Icon#11276:0", "value": "1:2244", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:4999;12258:1797;12258:2410", "name": "component-search", "type": "INSTANCE", "bounding_box": {"x": 5114.0, "y": 3188.0, "width": 20.0, "height": 20.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_99543f67", "component_id": "1:2244", "component_properties": null, "children": [{"id": "I16:4999;12258:1797;12258:2410;2051:95", "name": "component-search-path", "type": "IMAGE-SVG", "bounding_box": {"x": 5116.5576171875, "y": 3190.5634765625, "width": 14.872543334960938, "height": 14.87879753112793}, "text": null, "text_style": null, "fills": "fill_f6dc0550", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_9b4c115b", "component_id": null, "component_properties": null, "children": null}]}]}]}]}]}]}, {"id": "16:5000", "name": "Property 1=Default", "type": "COMPONENT", "bounding_box": {"x": 4390.0, "y": 3086.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_40163364", "component_id": null, "component_properties": null, "children": [{"id": "16:5001", "name": "Text Field Search [Static]", "type": "INSTANCE", "bounding_box": {"x": 4390.0, "y": 3086.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "13:19536", "component_properties": [{"name": "✏️ Label#3141:7", "value": "Cliente", "type": "TEXT"}, {"name": "✏️ Content#3141:8", "value": "Content", "type": "TEXT"}, {"name": "✏️ Hint Text#3811:5", "value": "Informe", "type": "TEXT"}, {"name": "✏️ Helper Text#3141:6", "value": "Helper Text", "type": "TEXT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Hovered", "value": "False", "type": "VARIANT"}, {"name": "Is Filled", "value": "False", "type": "VARIANT"}, {"name": "Is Focused", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "<PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has Helper Text", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5001;12258:1788", "name": "Input Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3086.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": "stroke_626ad390", "effects": null, "opacity": null, "border_radius": "2.0px", "layout": "layout_2fa345d1", "component_id": null, "component_properties": null, "children": [{"id": "I16:5001;12258:1789", "name": "Text Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3086.0, "width": 709.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_826ee542", "component_id": null, "component_properties": null, "children": [{"id": "I16:5001;12258:1791", "name": "Content", "type": "FRAME", "bounding_box": {"x": 4402.0, "y": 3098.0, "width": 685.0, "height": 24.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_f0f11426", "component_id": null, "component_properties": null, "children": [{"id": "I16:5001;12258:1792", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.0, "y": 3102.0, "width": 685.0, "height": 20.0}, "text": "Cliente", "text_style": "style_1816976b", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5001;12258:1794", "name": "Actions", "type": "FRAME", "bounding_box": {"x": 5099.0, "y": 3086.0, "width": 49.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c7720fd3", "component_id": null, "component_properties": null, "children": [{"id": "I16:5001;12258:1796", "name": "Divider", "type": "RECTANGLE", "bounding_box": {"x": 5099.0, "y": 3094.0, "width": 1.0, "height": 32.0}, "text": null, "text_style": null, "fills": "fill_a5eed152", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_cde2683d", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:5001;12258:1797", "name": ".Icon", "type": "INSTANCE", "bounding_box": {"x": 5100.0, "y": 3086.0, "width": 48.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c9140640", "component_id": "1:2247", "component_properties": [{"name": "🚺 Icon#11276:0", "value": "1:2244", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5001;12258:1797;12258:2410", "name": "component-search", "type": "INSTANCE", "bounding_box": {"x": 5114.0, "y": 3100.0, "width": 20.0, "height": 20.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_99543f67", "component_id": "1:2244", "component_properties": null, "children": [{"id": "I16:5001;12258:1797;12258:2410;2051:95", "name": "component-search-path", "type": "IMAGE-SVG", "bounding_box": {"x": 5116.5576171875, "y": 3102.5634765625, "width": 14.872543334960938, "height": 14.87879753112793}, "text": null, "text_style": null, "fills": "fill_f6dc0550", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_9b4c115b", "component_id": null, "component_properties": null, "children": null}]}]}]}]}]}]}, {"id": "16:5002", "name": "Property 1=Filled", "type": "COMPONENT", "bounding_box": {"x": 4390.0, "y": 3350.0, "width": 758.0, "height": 50.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_8a931b90", "component_id": null, "component_properties": null, "children": [{"id": "16:5003", "name": "Text Field Search [Static]", "type": "INSTANCE", "bounding_box": {"x": 4390.0, "y": 3350.0, "width": 758.0, "height": 52.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_99ef3bb6", "component_id": "13:19872", "component_properties": [{"name": "✏️ Content#3141:8", "value": "<PERSON><PERSON>", "type": "TEXT"}, {"name": "✏️ Label#3141:7", "value": "Cliente", "type": "TEXT"}, {"name": "✏️ Hint Text#3811:5", "value": "Hint Text", "type": "TEXT"}, {"name": "✏️ Helper Text#3141:6", "value": "Helper Text", "type": "TEXT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Hovered", "value": "False", "type": "VARIANT"}, {"name": "Is Filled", "value": "True", "type": "VARIANT"}, {"name": "Is Focused", "value": "True", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "<PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has Helper Text", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5003;12258:2128", "name": "Input Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3350.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": "stroke_7906ef1b", "effects": null, "opacity": null, "border_radius": "2.0px", "layout": "layout_2fa345d1", "component_id": null, "component_properties": null, "children": [{"id": "I16:5003;12258:2129", "name": "Text Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3350.0, "width": 661.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_826ee542", "component_id": null, "component_properties": null, "children": [{"id": "I16:5003;12258:2130", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.0, "y": 3354.0, "width": 637.0, "height": 16.0}, "text": "Cliente", "text_style": "style_2307eb9b", "fills": "fill_e18bd0be", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:5003;12258:2131", "name": "Content", "type": "FRAME", "bounding_box": {"x": 4402.0, "y": 3370.0, "width": 637.0, "height": 24.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_f0f11426", "component_id": null, "component_properties": null, "children": [{"id": "I16:5003;12258:2133", "name": "Content", "type": "TEXT", "bounding_box": {"x": 4402.0, "y": 3374.0, "width": 33.0, "height": 20.0}, "text": "<PERSON><PERSON>", "text_style": "style_1816976b", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_e875f8c6", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:5003;12258:2134", "name": "Pointer", "type": "LINE", "bounding_box": {"x": 4438.999998950927, "y": 3370.0, "width": 1.049073034664616e-06, "height": 24.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": "stroke_d2c048c6", "effects": null, "opacity": null, "border_radius": null, "layout": "layout_6b458617", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5003;12258:2136", "name": "Actions", "type": "FRAME", "bounding_box": {"x": 5051.0, "y": 3350.0, "width": 97.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c7720fd3", "component_id": null, "component_properties": null, "children": [{"id": "I16:5003;12258:2137", "name": ".Icon", "type": "INSTANCE", "bounding_box": {"x": 5051.0, "y": 3350.0, "width": 48.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c9140640", "component_id": "1:2251", "component_properties": [{"name": "🚺 Icon#11276:0", "value": "1:2289", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "Focused", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5003;12258:2137;12258:2414", "name": "component-close-delete", "type": "INSTANCE", "bounding_box": {"x": 5065.0, "y": 3364.0, "width": 20.0, "height": 20.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_99543f67", "component_id": "1:2289", "component_properties": null, "children": [{"id": "I16:5003;12258:2137;12258:2414;2051:89", "name": "component-close-delete-path", "type": "IMAGE-SVG", "bounding_box": {"x": 5067.5625, "y": 3366.562255859375, "width": 14.875089645385742, "height": 14.875089645385742}, "text": null, "text_style": null, "fills": "fill_e18bd0be", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_9b4c115b", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5003;12258:2138", "name": "Divider", "type": "RECTANGLE", "bounding_box": {"x": 5099.0, "y": 3358.0, "width": 1.0, "height": 32.0}, "text": null, "text_style": null, "fills": "fill_a5eed152", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_cde2683d", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:5003;12258:2139", "name": ".Icon", "type": "INSTANCE", "bounding_box": {"x": 5100.0, "y": 3350.0, "width": 48.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c9140640", "component_id": "1:2251", "component_properties": [{"name": "🚺 Icon#11276:0", "value": "1:2244", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "Focused", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5003;12258:2139;12258:2414", "name": "component-search", "type": "INSTANCE", "bounding_box": {"x": 5114.0, "y": 3364.0, "width": 20.0, "height": 20.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_99543f67", "component_id": "1:2244", "component_properties": null, "children": [{"id": "I16:5003;12258:2139;12258:2414;2051:95", "name": "component-search-path", "type": "IMAGE-SVG", "bounding_box": {"x": 5116.5576171875, "y": 3366.5634765625, "width": 14.872543334960938, "height": 14.87879753112793}, "text": null, "text_style": null, "fills": "fill_e18bd0be", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_9b4c115b", "component_id": null, "component_properties": null, "children": null}]}]}]}]}]}, {"id": "16:5004", "name": "Dropdown", "type": "INSTANCE", "bounding_box": {"x": 4390.0, "y": 3406.0, "width": 758.0, "height": 488.5465393066406}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": "effect_7de7775c", "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:5815", "component_properties": [{"name": "Type", "value": "Custom List", "type": "VARIANT"}, {"name": "<PERSON>", "value": "True", "type": "VARIANT"}, {"name": "Has Icon", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;5811:2517", "name": "List Container", "type": "FRAME", "bounding_box": {"x": 4389.925211859867, "y": 3410.0, "width": 758.1499382723123, "height": 416.27326236094814}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": "4.0px 4.0px 0.0px 0.0px", "layout": "layout_23867e30", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;5811:1032", "name": "List Item", "type": "INSTANCE", "bounding_box": {"x": 4390.056447380921, "y": 3410.0, "width": 758.0187027512584, "height": 52.273284057038836}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:4545", "component_properties": [{"name": "Lead Content#5330:8", "value": "1:2952", "type": "INSTANCE_SWAP"}, {"name": "✏️ Paragraph#5330:7", "value": "Paragraph", "type": "TEXT"}, {"name": "✏️ Label#5330:6", "value": "Zenith Alt | 99.ZEN.123/01DE-01", "type": "TEXT"}, {"name": "Trail Content#5330:5", "value": "1:5018", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "<PERSON> Paragraph", "value": "False", "type": "VARIANT"}, {"name": "Has Lead Content", "value": "False", "type": "VARIANT"}, {"name": "Has Trail Content", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has <PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has LineTop", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;5811:1032;5330:30287", "name": "Main Content", "type": "FRAME", "bounding_box": {"x": 4402.062125194585, "y": 3426.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4d88ed0d", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;5811:1032;5330:30288", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.062125194585, "y": 3426.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": "Zenith Alt | 99.ZEN.123/01DE-01", "text_style": "style_bf0b927c", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5004;5811:1033", "name": "List Item", "type": "INSTANCE", "bounding_box": {"x": 4390.037892693421, "y": 3462.0, "width": 758.0187027512584, "height": 52.273284057038836}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:4545", "component_properties": [{"name": "Lead Content#5330:8", "value": "1:2952", "type": "INSTANCE_SWAP"}, {"name": "✏️ Paragraph#5330:7", "value": "Paragraph", "type": "TEXT"}, {"name": "✏️ Label#5330:6", "value": "Zenith Brasil | 99.ZEN.123/01DE-02", "type": "TEXT"}, {"name": "Trail Content#5330:5", "value": "1:5018", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "<PERSON> Paragraph", "value": "False", "type": "VARIANT"}, {"name": "Has Lead Content", "value": "False", "type": "VARIANT"}, {"name": "Has Trail Content", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has <PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has LineTop", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;5811:1033;5330:30287", "name": "Main Content", "type": "FRAME", "bounding_box": {"x": 4402.043570507085, "y": 3478.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4d88ed0d", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;5811:1033;5330:30288", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.043570507085, "y": 3478.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": "Zenith Brasil | 99.ZEN.123/01DE-02", "text_style": "style_bf0b927c", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5004;5811:1034", "name": "List Item", "type": "INSTANCE", "bounding_box": {"x": 4390.018849724671, "y": 3514.0, "width": 758.0187027512584, "height": 52.273284057038836}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:4545", "component_properties": [{"name": "Lead Content#5330:8", "value": "1:2952", "type": "INSTANCE_SWAP"}, {"name": "✏️ Paragraph#5330:7", "value": "Paragraph", "type": "TEXT"}, {"name": "✏️ Label#5330:6", "value": "Zenith Capital | 99.ZEN.123/01DE-03", "type": "TEXT"}, {"name": "Trail Content#5330:5", "value": "1:5018", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "<PERSON> Paragraph", "value": "False", "type": "VARIANT"}, {"name": "Has Lead Content", "value": "False", "type": "VARIANT"}, {"name": "Has Trail Content", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has <PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has LineTop", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;5811:1034;5330:30287", "name": "Main Content", "type": "FRAME", "bounding_box": {"x": 4402.024527538335, "y": 3530.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4d88ed0d", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;5811:1034;5330:30288", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.024527538335, "y": 3530.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": "Zenith Capital | 99.ZEN.123/01DE-03", "text_style": "style_bf0b927c", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5004;5811:1035", "name": "List Item", "type": "INSTANCE", "bounding_box": {"x": 4390.000295037171, "y": 3566.0, "width": 758.0187027512584, "height": 52.273284057038836}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:4545", "component_properties": [{"name": "Lead Content#5330:8", "value": "1:2952", "type": "INSTANCE_SWAP"}, {"name": "✏️ Paragraph#5330:7", "value": "Paragraph", "type": "TEXT"}, {"name": "✏️ Label#5330:6", "value": "Zenith Discovery | 99.ZEN.123/01DE-04", "type": "TEXT"}, {"name": "Trail Content#5330:5", "value": "1:5018", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "<PERSON> Paragraph", "value": "False", "type": "VARIANT"}, {"name": "Has Lead Content", "value": "False", "type": "VARIANT"}, {"name": "Has Trail Content", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has <PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has LineTop", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;5811:1035;5330:30287", "name": "Main Content", "type": "FRAME", "bounding_box": {"x": 4402.005972850835, "y": 3582.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4d88ed0d", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;5811:1035;5330:30288", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.005972850835, "y": 3582.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": "Zenith Discovery | 99.ZEN.123/01DE-04", "text_style": "style_bf0b927c", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5004;9143:9967", "name": "List Item", "type": "INSTANCE", "bounding_box": {"x": 4389.981252068421, "y": 3618.0, "width": 758.0187027512584, "height": 52.273284057038836}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:4545", "component_properties": [{"name": "Lead Content#5330:8", "value": "1:2952", "type": "INSTANCE_SWAP"}, {"name": "✏️ Paragraph#5330:7", "value": "Paragraph", "type": "TEXT"}, {"name": "✏️ Label#5330:6", "value": "Zenith Enterprise | 99.ZEN.123/01DE-05", "type": "TEXT"}, {"name": "Trail Content#5330:5", "value": "1:5018", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "<PERSON> Paragraph", "value": "False", "type": "VARIANT"}, {"name": "Has Lead Content", "value": "False", "type": "VARIANT"}, {"name": "Has Trail Content", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has <PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has LineTop", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;9143:9967;5330:30287", "name": "Main Content", "type": "FRAME", "bounding_box": {"x": 4401.986929882085, "y": 3634.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4d88ed0d", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;9143:9967;5330:30288", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4401.986929882085, "y": 3634.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": "Zenith Enterprise | 99.ZEN.123/01DE-05", "text_style": "style_bf0b927c", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5004;9143:9968", "name": "List Item", "type": "INSTANCE", "bounding_box": {"x": 4389.962697380921, "y": 3670.0, "width": 758.0187027512584, "height": 52.273284057038836}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:4545", "component_properties": [{"name": "Lead Content#5330:8", "value": "1:2952", "type": "INSTANCE_SWAP"}, {"name": "✏️ Paragraph#5330:7", "value": "Paragraph", "type": "TEXT"}, {"name": "✏️ Label#5330:6", "value": "<PERSON><PERSON> | 99.ZEN.123/01DE-06", "type": "TEXT"}, {"name": "Trail Content#5330:5", "value": "1:5018", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "<PERSON> Paragraph", "value": "False", "type": "VARIANT"}, {"name": "Has Lead Content", "value": "False", "type": "VARIANT"}, {"name": "Has Trail Content", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has <PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has LineTop", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;9143:9968;5330:30287", "name": "Main Content", "type": "FRAME", "bounding_box": {"x": 4401.968375194585, "y": 3686.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4d88ed0d", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;9143:9968;5330:30288", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4401.968375194585, "y": 3686.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": "<PERSON><PERSON> | 99.ZEN.123/01DE-06", "text_style": "style_bf0b927c", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5004;9143:9969", "name": "List Item", "type": "INSTANCE", "bounding_box": {"x": 4389.944142693421, "y": 3722.0, "width": 758.0187027512584, "height": 52.273284057038836}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:4545", "component_properties": [{"name": "Lead Content#5330:8", "value": "1:2952", "type": "INSTANCE_SWAP"}, {"name": "✏️ Paragraph#5330:7", "value": "Paragraph", "type": "TEXT"}, {"name": "✏️ Label#5330:6", "value": "Zenith Terra | 99.ZEN.123/01DE-07", "type": "TEXT"}, {"name": "Trail Content#5330:5", "value": "1:5018", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "<PERSON> Paragraph", "value": "False", "type": "VARIANT"}, {"name": "Has Lead Content", "value": "False", "type": "VARIANT"}, {"name": "Has Trail Content", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has <PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has LineTop", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;9143:9969;5330:30287", "name": "Main Content", "type": "FRAME", "bounding_box": {"x": 4401.949820507085, "y": 3738.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4d88ed0d", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;9143:9969;5330:30288", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4401.949820507085, "y": 3738.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": "Zenith Terra | 99.ZEN.123/01DE-07", "text_style": "style_bf0b927c", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5004;9143:9970", "name": "List Item", "type": "INSTANCE", "bounding_box": {"x": 4389.925099724671, "y": 3774.0, "width": 758.0187027512584, "height": 52.273284057038836}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "1:4545", "component_properties": [{"name": "Lead Content#5330:8", "value": "1:2952", "type": "INSTANCE_SWAP"}, {"name": "✏️ Paragraph#5330:7", "value": "Paragraph", "type": "TEXT"}, {"name": "✏️ Label#5330:6", "value": "Zenith Zero | 99.ZEN.123/01DE-08", "type": "TEXT"}, {"name": "Trail Content#5330:5", "value": "1:5018", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "<PERSON> Paragraph", "value": "False", "type": "VARIANT"}, {"name": "Has Lead Content", "value": "False", "type": "VARIANT"}, {"name": "Has Trail Content", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has <PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has LineTop", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;9143:9970;5330:30287", "name": "Main Content", "type": "FRAME", "bounding_box": {"x": 4401.930777538335, "y": 3790.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4d88ed0d", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;9143:9970;5330:30288", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4401.930777538335, "y": 3790.00439453125, "width": 734.0071669931058, "height": 20.26463307288941}, "text": "Zenith Zero | 99.ZEN.123/01DE-08", "text_style": "style_bf0b927c", "fills": "fill_dac60cb0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}]}]}]}, {"id": "I16:5004;5811:1083", "name": "Container", "type": "FRAME", "bounding_box": {"x": 4389.988644372672, "y": 3826.273193359375, "width": 758.0230291970074, "height": 64.2732833417831}, "text": null, "text_style": null, "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": "effect_7de7775c", "opacity": null, "border_radius": "0.0px 0.0px 4.0px 4.0px", "layout": "layout_8de2537e", "component_id": null, "component_properties": null, "children": [{"id": "I16:5004;5811:1084", "name": "<PERSON><PERSON>", "type": "INSTANCE", "bounding_box": {"x": 5054.994322186336, "y": 3842.512939453125, "width": 79.01153247989714, "height": 32.02848052716581}, "text": null, "text_style": null, "fills": "fill_cca01286", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": "999.0px", "layout": "layout_81bfee4a", "component_id": "1:5705", "component_properties": [{"name": "✏️ Label#211:2", "value": "Buscar", "type": "TEXT"}, {"name": "🚺 Icon#366:0", "value": "1:226", "type": "INSTANCE_SWAP"}, {"name": "Type", "value": "Primary", "type": "VARIANT"}, {"name": "Size", "value": "Mid", "type": "VARIANT"}, {"name": "State", "value": "<PERSON><PERSON><PERSON>", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "Has Floating", "value": "False", "type": "VARIANT"}, {"name": "<PERSON>", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5004;5811:1084;2104:1411", "name": "✏️ Label", "type": "TEXT", "bounding_box": {"x": 5070.997161093168, "y": 3850.518798828125, "width": 47.005765792913735, "height": 16.01694429217605}, "text": "Buscar", "text_style": "style_94b5cec3", "fills": "fill_3acfdca0", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_e875f8c6", "component_id": null, "component_properties": null, "children": null}]}]}]}]}, {"id": "16:5005", "name": "Property 1=Focused", "type": "COMPONENT", "bounding_box": {"x": 4390.0, "y": 3262.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_40163364", "component_id": null, "component_properties": null, "children": [{"id": "16:5006", "name": "Text Field Search [Static]", "type": "INSTANCE", "bounding_box": {"x": 4390.0, "y": 3262.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": "13:19752", "component_properties": [{"name": "✏️ Content#3141:8", "value": "Content", "type": "TEXT"}, {"name": "✏️ Label#3141:7", "value": "Cliente", "type": "TEXT"}, {"name": "✏️ Hint Text#3811:5", "value": "Informe", "type": "TEXT"}, {"name": "✏️ Helper Text#3141:6", "value": "Helper Text", "type": "TEXT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Hovered", "value": "False", "type": "VARIANT"}, {"name": "Is Filled", "value": "False", "type": "VARIANT"}, {"name": "Is Focused", "value": "True", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}, {"name": "<PERSON>", "value": "False", "type": "VARIANT"}, {"name": "Has Helper Text", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5006;12258:2004", "name": "Input Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3262.0, "width": 758.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": "stroke_7906ef1b", "effects": null, "opacity": null, "border_radius": "2.0px", "layout": "layout_2fa345d1", "component_id": null, "component_properties": null, "children": [{"id": "I16:5006;12258:2005", "name": "Text Container", "type": "FRAME", "bounding_box": {"x": 4390.0, "y": 3262.0, "width": 709.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_826ee542", "component_id": null, "component_properties": null, "children": [{"id": "I16:5006;12258:2006", "name": "Label", "type": "TEXT", "bounding_box": {"x": 4402.0, "y": 3266.0, "width": 685.0, "height": 16.0}, "text": "Cliente", "text_style": "style_2307eb9b", "fills": "fill_e18bd0be", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_b2eb7a4c", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:5006;12258:2007", "name": "Content", "type": "FRAME", "bounding_box": {"x": 4402.0, "y": 3282.0, "width": 685.0, "height": 24.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_f0f11426", "component_id": null, "component_properties": null, "children": [{"id": "I16:5006;12258:2010", "name": "Pointer", "type": "LINE", "bounding_box": {"x": 4401.999998950927, "y": 3282.0, "width": 1.049073034664616e-06, "height": 24.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": "stroke_d2c048c6", "effects": null, "opacity": null, "border_radius": null, "layout": "layout_6b458617", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:5006;12258:2011", "name": "Hint Text", "type": "TEXT", "bounding_box": {"x": 4406.0, "y": 3282.0, "width": 681.0, "height": 24.0}, "text": "Informe", "text_style": "style_965775aa", "fills": "fill_f6dc0550", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_4fe00eb3", "component_id": null, "component_properties": null, "children": null}]}]}, {"id": "I16:5006;12258:2012", "name": "Actions", "type": "FRAME", "bounding_box": {"x": 5099.0, "y": 3262.0, "width": 49.0, "height": 48.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c7720fd3", "component_id": null, "component_properties": null, "children": [{"id": "I16:5006;12258:2014", "name": "Divider", "type": "RECTANGLE", "bounding_box": {"x": 5099.0, "y": 3270.0, "width": 1.0, "height": 32.0}, "text": null, "text_style": null, "fills": "fill_a5eed152", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_cde2683d", "component_id": null, "component_properties": null, "children": null}, {"id": "I16:5006;12258:2015", "name": ".Icon", "type": "INSTANCE", "bounding_box": {"x": 5100.0, "y": 3262.0, "width": 48.0, "height": 48.0}, "text": null, "text_style": null, "fills": "fill_b2d4e19d", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_c9140640", "component_id": "1:2251", "component_properties": [{"name": "🚺 Icon#11276:0", "value": "1:2244", "type": "INSTANCE_SWAP"}, {"name": "State", "value": "Focused", "type": "VARIANT"}, {"name": "Is OnColor", "value": "False", "type": "VARIANT"}, {"name": "Is Disabled", "value": "False", "type": "VARIANT"}], "children": [{"id": "I16:5006;12258:2015;12258:2414", "name": "component-search", "type": "INSTANCE", "bounding_box": {"x": 5114.0, "y": 3276.0, "width": 20.0, "height": 20.0}, "text": null, "text_style": null, "fills": null, "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_99543f67", "component_id": "1:2244", "component_properties": null, "children": [{"id": "I16:5006;12258:2015;12258:2414;2051:95", "name": "component-search-path", "type": "IMAGE-SVG", "bounding_box": {"x": 5116.5576171875, "y": 3278.5634765625, "width": 14.872543334960938, "height": 14.87879753112793}, "text": null, "text_style": null, "fills": "fill_e18bd0be", "styles": null, "strokes": null, "effects": null, "opacity": null, "border_radius": null, "layout": "layout_9b4c115b", "component_id": null, "component_properties": null, "children": null}]}]}]}]}]}]}]}], "components": {"16:4996": {"key": "ff679a872f366eb907e24407dee532dc7a178f4d", "name": "Property 1=Selecteed", "description": "", "remote": false, "componentSetId": "16:4995", "documentationLinks": []}, "13:19984": {"key": "552d1cc0e7273b88d401a4231d4c37c3e76ffc37", "name": "Is OnColor=False, Is Hovered=False, Is Filled=True, Is Focused=False, Is Disabled=False, Has Button Text=False, Has Helper Text=False", "description": "", "remote": true, "componentSetId": "13:19483", "documentationLinks": []}, "1:2251": {"key": "f435ad7691cbf769a98c6bbf004b3c7de1826ff5", "name": "State=Focused, Is OnColor=False, Is Disabled=False", "description": "", "remote": true, "componentSetId": "1:2246", "documentationLinks": []}, "1:2289": {"key": "aa63fc8212ba03865214433114f5e3c6cf14d1e2", "name": "component-close-delete", "description": "⚠️ Atenção\nNão utilizar esse ícone.\nCaso necessite o uso desse ícone entre em\ncontato com o time do Liquid.\n\n\n<EMAIL>", "remote": true, "documentationLinks": []}, "1:2244": {"key": "768bd504226188795aaa12a06786d0dc6eada606", "name": "component-search", "description": "⚠️ Atenção\nNão utilizar esse ícone.\nCaso necessite o uso desse ícone entre em\ncontato com o time do Liquid.\n\n\n<EMAIL>", "remote": true, "documentationLinks": []}, "16:4998": {"key": "123575cd68a7cf5eaa9dbb0b97a74c8fcf98a74d", "name": "Property 1=Hover", "description": "", "remote": false, "componentSetId": "16:4995", "documentationLinks": []}, "13:19536": {"key": "d664a3564da5471accf99ad9cbba8c37dde9ffe6", "name": "Is OnColor=False, Is Hovered=False, Is Filled=False, Is Focused=False, Is Disabled=False, Has Button Text=False, Has Helper Text=False", "description": "", "remote": true, "componentSetId": "13:19483", "documentationLinks": []}, "1:2247": {"key": "18170431e77bab85299152e715b4c9961b805e5b", "name": "State=Default, Is OnColor=False, Is Disabled=False", "description": "", "remote": true, "componentSetId": "1:2246", "documentationLinks": []}, "16:5000": {"key": "f86f3e97b38796fc699213207cc0c8e4f2400719", "name": "Property 1=Default", "description": "", "remote": false, "componentSetId": "16:4995", "documentationLinks": []}, "16:5002": {"key": "198ace3acf5e4054c8c8506ac0a10d76d15b4090", "name": "Property 1=Filled", "description": "", "remote": false, "componentSetId": "16:4995", "documentationLinks": []}, "13:19872": {"key": "f849e4a2e89ba5b386b6cbf9940926f0ef6c94df", "name": "Is OnColor=False, Is Hovered=False, Is Filled=True, Is Focused=True, Is Disabled=False, Has Button Text=False, Has Helper Text=False", "description": "", "remote": true, "componentSetId": "13:19483", "documentationLinks": []}, "1:5815": {"key": "d78698a922c93724ebdd049eeeea808d3118d5e4", "name": "Type=Custom List, Has But<PERSON>=True, Has Icon=False", "description": "", "remote": true, "componentSetId": "1:5786", "documentationLinks": []}, "1:4545": {"key": "0001ac34844d1d04d4b9226411aa56f70ef2b170", "name": "State=Default, Is OnColor=False, Has Paragraph=False, Has Lead Content=False, Has Trail Content=False, Is Disabled=False, Has LineBottom=False, Has LineTop=False", "description": "", "remote": true, "componentSetId": "1:2960", "documentationLinks": []}, "1:2952": {"key": "32effea97e6fd37aa038d1f139b7f1179c95627f", "name": "Size=Small", "description": "", "remote": true, "componentSetId": "1:2951", "documentationLinks": []}, "1:5018": {"key": "7934d9c955fe82e761da5fae5d8fb102edc5c128", "name": "Mode=Unselected, State=Default, Has Label=False, Label Direct=Row Reverse, Align=Center, Is OnColor=False, Is Disabled=False", "description": "", "remote": true, "componentSetId": "1:5017", "documentationLinks": []}, "1:3729": {"key": "9e298f5399dffe054d058b1a6b7797892b7a51ad", "name": "State=Default, Is OnColor=False, Has Paragraph=False, Has Lead Content=False, Has Trail Content=True, Is Disabled=False, Has LineBottom=True, Has LineTop=False", "description": "", "remote": true, "componentSetId": "1:2960", "documentationLinks": []}, "1:5705": {"key": "81efe74ec12a9753aae059b00a90c5ff6ed218ca", "name": "Type=Primary, Size=Mid, State=Default, Is OnColor=False, Is Disabled=False, Has Floating=False, Is Quickbutton=False", "description": "", "remote": true, "componentSetId": "1:5545", "documentationLinks": []}, "1:226": {"key": "5ee99fef31f5f352907f3e6199b586472436d4b9", "name": "ui-icon-placeholder", "description": "", "remote": true, "documentationLinks": []}, "16:5005": {"key": "4de4e90a9ebfbeb0d9475bd529e9a98b6297f3c8", "name": "Property 1=Focused", "description": "", "remote": false, "componentSetId": "16:4995", "documentationLinks": []}, "13:19752": {"key": "27303c58ebd457c54755e6c4d27d5e77815be21f", "name": "Is OnColor=False, Is Hovered=False, Is Filled=False, Is Focused=True, Is Disabled=False, Has Button Text=False, Has Helper Text=False", "description": "", "remote": true, "componentSetId": "13:19483", "documentationLinks": []}}, "component_sets": {"16:4995": {"key": "1e7b623c6d9fa17483e90ebbe504fd039ad66e1a", "name": "Busca default - buscar por Cliente", "description": "", "documentationLinks": []}, "13:19483": {"key": "2024519167285bb4ede28d7fc23684d7dd0945c0", "name": "Text Field Search [Static]", "description": "✅ CORE COMPONENT\n\n\nA pesquisa é um método intuitivo de descoberta, oferecendo aos usuários uma maneira de explorar um site ou aplicativo usando palavras-chave. O método de pesquisa depende do tamanho do conjunto de dados que está sendo pesquisado e da localização do componente no produto.", "remote": true}, "1:2246": {"key": "eaeef28834cd335b1e6ef22b54c2ab49c21f85f7", "name": ".Icon", "description": "🚧 BUILDING BLOCKS", "remote": true}, "1:5786": {"key": "276152ef90947df5ab4ec338177d7442142494d2", "name": "Dropdown", "description": "✅ CORE COMPONENT\n\n\nO Dropdown é um componente de lista utilizado em menus.", "remote": true}, "1:2960": {"key": "cb66cc908502ecfc00da12d94d5dd3b161f8a2ec", "name": "List Item", "description": "✅ CORE COMPONENT\n\n\nÉ um componente que permite agrupar e organizar verticalmente itens de conteúdo relacionado.", "remote": true}, "1:2951": {"key": "32ef9693da2b3eab0d8ab0376bd2ca5573b516a3", "name": "Slot", "description": "", "remote": true}, "1:5017": {"key": "7fd1543937739b18a3ec089256f67815a1aa25e4", "name": "Check box", "description": "✅ CORE COMPONENT\n\n\nÉ um componente que pode ser apresentado em conjunto ou sozinho e que permite única ou múltipla seleção.", "remote": true}, "1:5545": {"key": "2aa0f0b4f4f58003d1a5d9e91891c17a9db39b26", "name": "<PERSON><PERSON>", "description": "✅ CORE COMPONENT\n\n\nOs botões permitem que os usuários executem uma ação ou naveguem para outra página. Eles têm vários estilos para diversas necessidades e são ideais para chamar a atenção para onde um usuário precisa fazer algo para avançar em um fluxo.\n#QuickButton", "remote": true}}, "global_vars": {"styles": {"stroke_f7f3cb41": {"colors": [{"type": "SOLID", "hex": "#9747ff", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "weights": {"top": 1.0, "right": 1.0, "bottom": 1.0, "left": 1.0}, "align": "inside", "dash_pattern": [10.0, 5.0]}, "layout_9b4c115b": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": null, "padding": null, "sizing": null, "overflow_scroll": null, "position": null}, "layout_40163364": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"width": 758}, "padding": null, "sizing": {"horizontal": "fixed", "vertical": "hug"}, "overflow_scroll": null, "position": null}, "layout_b2eb7a4c": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": null, "padding": null, "sizing": {"horizontal": "fill", "vertical": "hug"}, "overflow_scroll": null, "position": null}, "fill_b2d4e19d": [{"type": "SOLID", "hex": "#fafbff", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "stroke_8b43a06e": {"colors": [{"type": "SOLID", "hex": "#47484c", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "weights": {"top": 0.0, "right": 0.0, "bottom": 2.0, "left": 0.0}, "align": "inside", "dash_pattern": null}, "layout_2fa345d1": {"mode": "row", "justify_content": null, "align_items": "center", "align_self": "stretch", "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": null, "padding": null, "sizing": {"horizontal": "fill", "vertical": "hug"}, "overflow_scroll": null, "position": null}, "layout_826ee542": {"mode": "column", "justify_content": "center", "align_items": null, "align_self": "stretch", "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": null, "padding": "4.0px 12.0px", "sizing": {"horizontal": "fill", "vertical": "fill"}, "overflow_scroll": null, "position": null}, "style_2307eb9b": {"font_family": "Bradesco Sans", "font_weight": 600, "font_size": 12.0, "line_height": "1.3333333333333333em", "text_align_horizontal": "left", "text_align_vertical": "top"}, "fill_64a0e084": [{"type": "SOLID", "hex": "#47484c", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "layout_f0f11426": {"mode": "row", "justify_content": null, "align_items": "flex-end", "align_self": "stretch", "wrap": null, "gap": "4.0px", "location_relative_to_parent": null, "dimensions": null, "padding": null, "sizing": {"horizontal": "fill", "vertical": "hug"}, "overflow_scroll": ["x"], "position": null}, "style_1816976b": {"font_family": "Bradesco Sans", "font_weight": 500, "font_size": 16.0, "line_height": "1.25em", "text_align_horizontal": "left", "text_align_vertical": "top"}, "fill_dac60cb0": [{"type": "SOLID", "hex": "#000000", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "layout_e875f8c6": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": null, "padding": null, "sizing": {"horizontal": "hug", "vertical": "hug"}, "overflow_scroll": null, "position": null}, "layout_c7720fd3": {"mode": "row", "justify_content": null, "align_items": "center", "align_self": "stretch", "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": null, "padding": null, "sizing": {"horizontal": "hug", "vertical": "fill"}, "overflow_scroll": null, "position": null}, "layout_c9140640": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"width": 48, "height": 48}, "padding": null, "sizing": {"horizontal": "fixed", "vertical": "fixed"}, "overflow_scroll": null, "position": null}, "layout_99543f67": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"width": 20, "height": 20}, "padding": null, "sizing": {"horizontal": "fixed", "vertical": "fixed"}, "overflow_scroll": null, "position": null}, "fill_e18bd0be": [{"type": "SOLID", "hex": "#3b69ff", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "fill_a5eed152": [{"type": "SOLID", "hex": "#d9dcdd", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "layout_cde2683d": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"width": 1, "height": 32}, "padding": null, "sizing": {"horizontal": "fixed", "vertical": "fixed"}, "overflow_scroll": null, "position": null}, "stroke_626ad390": {"colors": [{"type": "SOLID", "hex": "#a7a8ac", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "weights": {"top": 0.0, "right": 0.0, "bottom": 2.0, "left": 0.0}, "align": "inside", "dash_pattern": null}, "fill_f6dc0550": [{"type": "SOLID", "hex": "#6d6e71", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "layout_8a931b90": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"width": 758, "height": 50}, "padding": null, "sizing": {"horizontal": "fixed", "vertical": "fixed"}, "overflow_scroll": null, "position": null}, "layout_99ef3bb6": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"height": 52}, "padding": null, "sizing": {"horizontal": "fill", "vertical": "fixed"}, "overflow_scroll": null, "position": null}, "stroke_7906ef1b": {"colors": [{"type": "SOLID", "hex": "#3b69ff", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "weights": {"top": 0.0, "right": 0.0, "bottom": 2.0, "left": 0.0}, "align": "inside", "dash_pattern": null}, "stroke_d2c048c6": {"colors": [{"type": "SOLID", "hex": "#000000", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "weights": {"top": 1.0, "right": 1.0, "bottom": 1.0, "left": 1.0}, "align": "center", "dash_pattern": null}, "layout_6b458617": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"width": 0, "height": 24}, "padding": null, "sizing": {"horizontal": "fixed", "vertical": "fixed"}, "overflow_scroll": null, "position": null}, "effect_7de7775c": {"drop_shadow": [{"color": "rgba(0, 0, 0, 0.1599999964237213)", "offset": {"x": 0.0, "y": 8.0}, "radius": 16.0, "spread": 0, "blend_mode": "normal"}], "inner_shadow": null, "blur": null}, "fill_3acfdca0": [{"type": "SOLID", "hex": "#ffffff", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "layout_23867e30": {"mode": "column", "justify_content": null, "align_items": null, "align_self": "stretch", "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": null, "padding": null, "sizing": {"horizontal": "fill", "vertical": "hug"}, "overflow_scroll": null, "position": null}, "layout_4d88ed0d": {"mode": "column", "justify_content": "center", "align_items": null, "align_self": null, "wrap": null, "gap": "8.0px", "location_relative_to_parent": null, "dimensions": null, "padding": null, "sizing": {"horizontal": "fill", "vertical": "hug"}, "overflow_scroll": null, "position": null}, "style_bf0b927c": {"font_family": "Bradesco Sans", "font_weight": 600, "font_size": 16.0, "line_height": "1.25em", "text_align_horizontal": "left", "text_align_vertical": "middle"}, "layout_8de2537e": {"mode": "column", "justify_content": "center", "align_items": "flex-end", "align_self": "stretch", "wrap": null, "gap": "10.0px", "location_relative_to_parent": null, "dimensions": null, "padding": "16.0px 14.0px", "sizing": {"horizontal": "fill", "vertical": "hug"}, "overflow_scroll": null, "position": null}, "fill_cca01286": [{"type": "SOLID", "hex": "#cc092f", "rgba": null, "opacity": 1.0, "image_ref": null, "scale_mode": null, "gradient_handle_positions": null, "gradient_stops": null}], "layout_81bfee4a": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"height": 32}, "padding": null, "sizing": {"horizontal": "hug", "vertical": "fixed"}, "overflow_scroll": null, "position": null}, "style_94b5cec3": {"font_family": "Bradesco Sans", "font_weight": 700, "font_size": 14.0, "line_height": "1.1428571428571428em", "text_align_horizontal": "center", "text_align_vertical": "top"}, "style_965775aa": {"font_family": "Bradesco Sans", "font_weight": 500, "font_size": 16.0, "line_height": "1.25em", "text_align_horizontal": "left", "text_align_vertical": "bottom"}, "layout_4fe00eb3": {"mode": "none", "justify_content": null, "align_items": null, "align_self": null, "wrap": null, "gap": null, "location_relative_to_parent": null, "dimensions": {"height": 24}, "padding": null, "sizing": {"horizontal": "fill", "vertical": "fixed"}, "overflow_scroll": null, "position": null}}}}