#!/usr/bin/env python3
"""
Teste da função de sanitização de nomes de arquivo.
"""

import sys
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent))

# Importar função de sanitização
from process_selected_nodes import sanitize_filename

def test_sanitization():
    """Testa a função de sanitização."""
    print("🧪 TESTE: Sanitização de Nomes de Arquivo")
    print("=" * 50)
    
    test_cases = [
        # (input, expected_pattern)
        ("QUI, 06/09/2024", "QUI_06_09_2024"),
        ("Header Component", "Header_Component"),
        ("Button: Primary", "Button_Primary"),
        ("Logo/Brand", "Logo_Brand"),
        ("Text<>Special", "Text__Special"),
        ("File|Name?", "File_Name_"),
        ("Normal Name", "Normal_Name"),
        ("", "component"),
        ("   ", "component"),
        ("___", "component"),
        ("Very Long Component Name That Exceeds Fifty Characters Limit", "Very_Long_Component_Name_That_Exceeds_Fifty_Char"),
        ("acfi", "acfi"),
        ("logo-bradesco", "logo-bradesco"),
    ]
    
    print("Testando casos:")
    all_passed = True
    
    for i, (input_name, expected) in enumerate(test_cases, 1):
        result = sanitize_filename(input_name)
        passed = True
        
        # Verificações básicas
        if not result:
            passed = False
            print(f"❌ {i:2d}. '{input_name}' -> '{result}' (vazio)")
        elif any(char in result for char in '<>:"/\\|?*'):
            passed = False
            print(f"❌ {i:2d}. '{input_name}' -> '{result}' (caracteres inválidos)")
        elif ',' in result:
            passed = False
            print(f"❌ {i:2d}. '{input_name}' -> '{result}' (vírgula não removida)")
        elif len(result) > 55:  # 50 + extensão
            passed = False
            print(f"❌ {i:2d}. '{input_name}' -> '{result}' (muito longo: {len(result)})")
        else:
            print(f"✅ {i:2d}. '{input_name}' -> '{result}'")
        
        if not passed:
            all_passed = False
    
    print(f"\n{'✅ TODOS OS TESTES PASSARAM!' if all_passed else '❌ ALGUNS TESTES FALHARAM!'}")
    return all_passed

def test_problematic_names():
    """Testa nomes que causaram problemas."""
    print("\n🔍 TESTE: Nomes Problemáticos Específicos")
    print("=" * 50)
    
    problematic_names = [
        "QUI, 06/09/2024",
        "SEG, 02/09/2024", 
        "TER, 03/09/2024",
        "Component: Special/Name",
        "File<Name>",
        "Path\\To\\File",
        "Question?Mark",
        "Pipe|Name",
        "Star*Name",
    ]
    
    for name in problematic_names:
        sanitized = sanitize_filename(name)
        
        # Tentar criar arquivo de teste
        try:
            test_file = Path(f"data/temp/{sanitized}.html")
            test_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Testar criação do arquivo
            with open(test_file, 'w') as f:
                f.write("test")
            
            # Remover arquivo de teste
            test_file.unlink()
            
            print(f"✅ '{name}' -> '{sanitized}' (arquivo criado com sucesso)")
            
        except Exception as e:
            print(f"❌ '{name}' -> '{sanitized}' (erro: {e})")

if __name__ == "__main__":
    success = test_sanitization()
    test_problematic_names()
    
    if success:
        print("\n🎉 SANITIZAÇÃO FUNCIONANDO CORRETAMENTE!")
        print("   A função sanitize_filename() está tratando nomes problemáticos.")
    else:
        print("\n❌ PROBLEMAS NA SANITIZAÇÃO!")
        print("   Verifique a função sanitize_filename().")
    
    sys.exit(0 if success else 1)
